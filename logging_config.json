{"version": 1, "disable_existing_loggers": false, "formatters": {"standard": {"format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"}, "detailed": {"format": "%(asctime)s - %(name)s - %(levelname)s - %(module)s - %(funcName)s - %(lineno)d - %(message)s"}, "trading": {"format": "%(asctime)s - TRADING - %(levelname)s - %(message)s"}, "risk": {"format": "%(asctime)s - RISK - %(levelname)s - %(message)s"}, "performance": {"format": "%(asctime)s - PERFORMANCE - %(levelname)s - %(message)s"}, "json": {"format": "%(asctime)s", "class": "pythonjsonlogger.jsonlogger.JsonFormatter", "fmt": "%(asctime)s %(name)s %(levelname)s %(module)s %(funcName)s %(lineno)d %(message)s"}}, "handlers": {"console": {"class": "logging.StreamHandler", "level": "INFO", "formatter": "standard", "stream": "ext://sys.stdout"}, "main_file": {"class": "logging.handlers.RotatingFileHandler", "level": "DEBUG", "formatter": "detailed", "filename": "logs/synergy7.log", "maxBytes": 104857600, "backupCount": 10}, "error_file": {"class": "logging.handlers.RotatingFileHandler", "level": "ERROR", "formatter": "detailed", "filename": "logs/errors/synergy7_errors.log", "maxBytes": 52428800, "backupCount": 5}, "trading_file": {"class": "logging.handlers.RotatingFileHandler", "level": "INFO", "formatter": "trading", "filename": "logs/trading/trading.log", "maxBytes": 52428800, "backupCount": 7}, "risk_file": {"class": "logging.handlers.RotatingFileHandler", "level": "INFO", "formatter": "risk", "filename": "logs/risk/risk_management.log", "maxBytes": 52428800, "backupCount": 7}, "performance_file": {"class": "logging.handlers.RotatingFileHandler", "level": "INFO", "formatter": "performance", "filename": "logs/performance/strategy_performance.log", "maxBytes": 52428800, "backupCount": 7}, "system_file": {"class": "logging.handlers.RotatingFileHandler", "level": "INFO", "formatter": "detailed", "filename": "logs/system/system.log", "maxBytes": 52428800, "backupCount": 5}, "metrics_file": {"class": "logging.handlers.RotatingFileHandler", "level": "INFO", "formatter": "json", "filename": "logs/metrics/metrics.log", "maxBytes": 52428800, "backupCount": 10}}, "loggers": {"": {"handlers": ["console", "main_file", "error_file"], "level": "DEBUG", "propagate": false}, "trading": {"handlers": ["trading_file", "console"], "level": "INFO", "propagate": false}, "risk": {"handlers": ["risk_file", "console", "error_file"], "level": "INFO", "propagate": false}, "performance": {"handlers": ["performance_file", "console"], "level": "INFO", "propagate": false}, "system": {"handlers": ["system_file", "console"], "level": "INFO", "propagate": false}, "metrics": {"handlers": ["metrics_file", "console"], "level": "INFO", "propagate": false}, "core.strategies": {"handlers": ["main_file", "console"], "level": "DEBUG", "propagate": false}, "core.risk": {"handlers": ["risk_file", "main_file", "console"], "level": "DEBUG", "propagate": false}, "core.analytics": {"handlers": ["performance_file", "main_file", "console"], "level": "DEBUG", "propagate": false}, "core.data": {"handlers": ["main_file", "console"], "level": "DEBUG", "propagate": false}, "core.signals": {"handlers": ["main_file", "console"], "level": "DEBUG", "propagate": false}}}