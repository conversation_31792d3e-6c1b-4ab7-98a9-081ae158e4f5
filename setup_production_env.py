#!/usr/bin/env python3
"""
Production Environment Setup Script for Synergy7 Enhanced Trading System.

This script helps configure and validate the production environment,
ensuring all necessary components are properly set up.
"""

import os
import sys
import logging
import shutil
import json
from pathlib import Path
from typing import Dict, List, Any
import yaml

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ProductionEnvironmentSetup:
    """Handles production environment setup and validation."""

    def __init__(self):
        self.project_root = Path.cwd()
        self.required_directories = [
            'data', 'logs', 'backups', 'reports', 'keys'
        ]
        self.required_files = [
            '.env', 'config.yaml'
        ]
        self.api_keys = {
            'HELIUS_API_KEY': 'dda9f776-9a40-447d-9ca4-22a27c21169e',
            'BIRDEYE_API_KEY': 'a2679724762a47b58dde41b20fb55ce9',
            'QUICKNODE_API_KEY': 'QN_6bc9e73d888f418682d564eb13db68a',
            'TELEGRAM_CHAT_ID': '5135869709'
        }

    def create_directories(self) -> bool:
        """Create required directories for production."""
        logger.info("Creating required directories...")

        try:
            for directory in self.required_directories:
                dir_path = self.project_root / directory
                dir_path.mkdir(exist_ok=True)
                logger.info(f"✅ Created directory: {directory}")

                # Set appropriate permissions
                os.chmod(dir_path, 0o755)

                # Create subdirectories for specific purposes
                if directory == 'data':
                    (dir_path / 'market_data').mkdir(exist_ok=True)
                    (dir_path / 'whale_signals').mkdir(exist_ok=True)
                    (dir_path / 'strategy_performance').mkdir(exist_ok=True)
                elif directory == 'logs':
                    (dir_path / 'trading').mkdir(exist_ok=True)
                    (dir_path / 'system').mkdir(exist_ok=True)
                    (dir_path / 'errors').mkdir(exist_ok=True)
                elif directory == 'reports':
                    (dir_path / 'daily').mkdir(exist_ok=True)
                    (dir_path / 'weekly').mkdir(exist_ok=True)
                    (dir_path / 'monthly').mkdir(exist_ok=True)

            return True
        except Exception as e:
            logger.error(f"Error creating directories: {str(e)}")
            return False

    def setup_environment_file(self) -> bool:
        """Set up the production environment file."""
        logger.info("Setting up production environment file...")

        try:
            env_production_path = self.project_root / '.env.production'
            env_path = self.project_root / '.env'

            if not env_production_path.exists():
                logger.error("❌ .env.production template not found!")
                return False

            # Copy production template to .env if it doesn't exist
            if not env_path.exists():
                shutil.copy2(env_production_path, env_path)
                logger.info("✅ Created .env from production template")
            else:
                logger.info("ℹ️  .env file already exists, skipping copy")

            # Validate environment variables
            return self.validate_environment_variables()

        except Exception as e:
            logger.error(f"Error setting up environment file: {str(e)}")
            return False

    def validate_environment_variables(self) -> bool:
        """Validate that all required environment variables are set."""
        logger.info("Validating environment variables...")

        try:
            # Load environment variables from .env file
            env_path = self.project_root / '.env'
            if env_path.exists():
                with open(env_path, 'r') as f:
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith('#') and '=' in line:
                            key, value = line.split('=', 1)
                            os.environ[key] = value

            # Check critical environment variables
            critical_vars = [
                'TRADING_MODE', 'MARKET_REGIME_ENABLED', 'VAR_ENABLED',
                'STRATEGY_ATTRIBUTION_ENABLED', 'ADAPTIVE_WEIGHTING_ENABLED'
            ]

            missing_vars = []
            for var in critical_vars:
                if var not in os.environ:
                    missing_vars.append(var)

            if missing_vars:
                logger.error(f"❌ Missing critical environment variables: {missing_vars}")
                return False

            # Validate API keys
            for key, default_value in self.api_keys.items():
                env_value = os.environ.get(key, '')
                if not env_value or env_value == 'your_api_key_here':
                    if key == 'TELEGRAM_BOT_TOKEN':
                        logger.warning(f"⚠️  {key} not configured - Telegram alerts will be disabled")
                    else:
                        logger.info(f"✅ {key} using default/provided value")
                else:
                    logger.info(f"✅ {key} configured")

            logger.info("✅ Environment variables validation completed")
            return True

        except Exception as e:
            logger.error(f"Error validating environment variables: {str(e)}")
            return False

    def validate_configuration(self) -> bool:
        """Validate the configuration file."""
        logger.info("Validating configuration file...")

        try:
            config_path = self.project_root / 'config.yaml'
            if not config_path.exists():
                logger.error("❌ config.yaml not found!")
                return False

            # Load and validate configuration
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)

            # Check required configuration sections
            required_sections = [
                'market_regime', 'whale_watching', 'risk_management',
                'strategy_attribution', 'adaptive_weighting'
            ]

            missing_sections = []
            for section in required_sections:
                if section not in config:
                    missing_sections.append(section)

            if missing_sections:
                logger.error(f"❌ Missing configuration sections: {missing_sections}")
                return False

            logger.info("✅ Configuration file validation completed")
            return True

        except Exception as e:
            logger.error(f"Error validating configuration: {str(e)}")
            return False

    def check_dependencies(self) -> bool:
        """Check that all required Python packages are installed."""
        logger.info("Checking Python dependencies...")

        # Package mapping for import names vs pip names
        required_packages = {
            'numpy': 'numpy',
            'pandas': 'pandas',
            'scipy': 'scipy',
            'sklearn': 'scikit-learn',
            'yaml': 'pyyaml',
            'httpx': 'httpx'
        }

        missing_packages = []

        for import_name, pip_name in required_packages.items():
            try:
                __import__(import_name)
                logger.debug(f"✅ {pip_name} installed")
            except ImportError:
                missing_packages.append(pip_name)
                logger.error(f"❌ {pip_name} not installed")

        # Check optional packages
        optional_packages = ['asyncio', 'aiohttp', 'websockets']
        for package in optional_packages:
            try:
                __import__(package)
                logger.debug(f"✅ {package} installed")
            except ImportError:
                logger.warning(f"⚠️  {package} not installed (optional)")

        if missing_packages:
            logger.error(f"Missing packages: {missing_packages}")
            logger.info("Install missing packages with: pip install " + " ".join(missing_packages))
            return False

        logger.info("✅ All required dependencies are installed")
        return True

    def create_sample_wallet_config(self) -> bool:
        """Create a sample wallet configuration file."""
        logger.info("Creating sample wallet configuration...")

        try:
            keys_dir = self.project_root / 'keys'
            wallet_config_path = keys_dir / 'wallet_config.json'

            if wallet_config_path.exists():
                logger.info("ℹ️  Wallet config already exists, skipping creation")
                return True

            sample_config = {
                "wallet_address": "your_solana_wallet_address_here",
                "private_key_file": "wallet.json",
                "network": "mainnet-beta",
                "rpc_url": "https://api.mainnet-beta.solana.com",
                "commitment": "confirmed"
            }

            with open(wallet_config_path, 'w') as f:
                json.dump(sample_config, f, indent=2)

            # Set restrictive permissions for wallet config
            os.chmod(wallet_config_path, 0o600)

            logger.info("✅ Created sample wallet configuration")
            logger.warning("⚠️  Please update keys/wallet_config.json with your actual wallet details")

            return True

        except Exception as e:
            logger.error(f"Error creating wallet configuration: {str(e)}")
            return False

    def setup_logging_configuration(self) -> bool:
        """Set up logging configuration for production."""
        logger.info("Setting up logging configuration...")

        try:
            logging_config = {
                "version": 1,
                "disable_existing_loggers": False,
                "formatters": {
                    "standard": {
                        "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
                    },
                    "detailed": {
                        "format": "%(asctime)s - %(name)s - %(levelname)s - %(module)s - %(funcName)s - %(message)s"
                    }
                },
                "handlers": {
                    "console": {
                        "class": "logging.StreamHandler",
                        "level": "INFO",
                        "formatter": "standard",
                        "stream": "ext://sys.stdout"
                    },
                    "file": {
                        "class": "logging.handlers.RotatingFileHandler",
                        "level": "DEBUG",
                        "formatter": "detailed",
                        "filename": "logs/synergy7.log",
                        "maxBytes": 104857600,
                        "backupCount": 5
                    },
                    "error_file": {
                        "class": "logging.handlers.RotatingFileHandler",
                        "level": "ERROR",
                        "formatter": "detailed",
                        "filename": "logs/errors/synergy7_errors.log",
                        "maxBytes": 52428800,
                        "backupCount": 3
                    }
                },
                "loggers": {
                    "": {
                        "handlers": ["console", "file", "error_file"],
                        "level": "DEBUG",
                        "propagate": False
                    }
                }
            }

            logging_config_path = self.project_root / 'logging_config.json'
            with open(logging_config_path, 'w') as f:
                json.dump(logging_config, f, indent=2)

            logger.info("✅ Created logging configuration")
            return True

        except Exception as e:
            logger.error(f"Error setting up logging configuration: {str(e)}")
            return False

    def create_systemd_service(self) -> bool:
        """Create a systemd service file for production deployment."""
        logger.info("Creating systemd service file...")

        try:
            service_content = f"""[Unit]
Description=Synergy7 Enhanced Trading System
After=network.target
Wants=network.target

[Service]
Type=simple
User=ubuntu
Group=ubuntu
WorkingDirectory={self.project_root}
Environment=PATH={sys.executable}
ExecStart={sys.executable} phase_4_deployment/unified_runner.py --mode production
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=synergy7

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths={self.project_root}

[Install]
WantedBy=multi-user.target
"""

            service_path = self.project_root / 'synergy7.service'
            with open(service_path, 'w') as f:
                f.write(service_content)

            logger.info("✅ Created systemd service file: synergy7.service")
            logger.info("To install: sudo cp synergy7.service /etc/systemd/system/")
            logger.info("To enable: sudo systemctl enable synergy7")
            logger.info("To start: sudo systemctl start synergy7")

            return True

        except Exception as e:
            logger.error(f"Error creating systemd service: {str(e)}")
            return False

    def run_setup(self) -> bool:
        """Run the complete production environment setup."""
        logger.info("="*60)
        logger.info("SYNERGY7 PRODUCTION ENVIRONMENT SETUP")
        logger.info("="*60)

        setup_steps = [
            ("Creating directories", self.create_directories),
            ("Setting up environment file", self.setup_environment_file),
            ("Validating configuration", self.validate_configuration),
            ("Checking dependencies", self.check_dependencies),
            ("Creating wallet configuration", self.create_sample_wallet_config),
            ("Setting up logging", self.setup_logging_configuration),
            ("Creating systemd service", self.create_systemd_service)
        ]

        failed_steps = []

        for step_name, step_function in setup_steps:
            logger.info(f"\n📋 {step_name}...")
            try:
                if step_function():
                    logger.info(f"✅ {step_name} completed successfully")
                else:
                    logger.error(f"❌ {step_name} failed")
                    failed_steps.append(step_name)
            except Exception as e:
                logger.error(f"❌ {step_name} failed with error: {str(e)}")
                failed_steps.append(step_name)

        # Final summary
        logger.info("\n" + "="*60)
        logger.info("PRODUCTION SETUP SUMMARY")
        logger.info("="*60)

        if not failed_steps:
            logger.info("🎉 ALL SETUP STEPS COMPLETED SUCCESSFULLY!")
            logger.info("\n📋 Next Steps:")
            logger.info("1. Review and update .env file with your specific values")
            logger.info("2. Configure your Solana wallet in keys/wallet_config.json")
            logger.info("3. Set up your Telegram bot token if using alerts")
            logger.info("4. Run initial tests: python test_complete_integration.py")
            logger.info("5. Start with paper trading mode first")
            logger.info("6. Monitor logs and performance before going live")
            return True
        else:
            logger.error(f"❌ SETUP FAILED - Failed steps: {failed_steps}")
            logger.info("\n📋 Please fix the failed steps and run setup again")
            return False

def main():
    """Main setup function."""
    setup = ProductionEnvironmentSetup()
    success = setup.run_setup()

    if success:
        logger.info("\n🚀 Synergy7 Enhanced Trading System is ready for production!")
    else:
        logger.error("\n💥 Setup failed. Please review errors and try again.")

    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
