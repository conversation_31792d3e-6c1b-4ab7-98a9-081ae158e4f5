#!/usr/bin/env python3
"""
Simple Real-Time Monitoring Dashboard for Synergy7 Enhanced Trading System.

This dashboard shows real-time metrics from the running paper trading system
and system health information.
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import json
import os
import sys
from datetime import datetime, timedelta
from pathlib import Path
import time
import psutil
import httpx
import asyncio

# Configure page
st.set_page_config(
    page_title="Synergy7 Enhanced Trading System - Live Monitor",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Add project root to path
project_root = Path.cwd()
sys.path.append(str(project_root))

def get_system_metrics():
    """Get real-time system metrics."""
    try:
        # Memory metrics
        memory = psutil.virtual_memory()
        
        # CPU metrics
        cpu_percent = psutil.cpu_percent(interval=1)
        
        # Disk metrics
        disk = psutil.disk_usage('.')
        
        # Process metrics
        try:
            process = psutil.Process()
            process_memory = process.memory_info().rss / (1024**2)  # MB
            process_cpu = process.cpu_percent()
        except:
            process_memory = 0
            process_cpu = 0
        
        return {
            'timestamp': datetime.now(),
            'memory_total_gb': round(memory.total / (1024**3), 1),
            'memory_used_pct': memory.percent,
            'memory_available_gb': round(memory.available / (1024**3), 1),
            'cpu_usage_pct': cpu_percent,
            'cpu_cores': psutil.cpu_count(),
            'disk_total_gb': round(disk.total / (1024**3), 1),
            'disk_used_pct': round((disk.used / disk.total) * 100, 1),
            'disk_free_gb': round(disk.free / (1024**3), 1),
            'process_memory_mb': round(process_memory, 1),
            'process_cpu_pct': process_cpu
        }
    except Exception as e:
        st.error(f"Error getting system metrics: {str(e)}")
        return {}

def get_health_report():
    """Get latest health report."""
    try:
        reports_dir = project_root / 'reports' / 'daily'
        if reports_dir.exists():
            # Get latest health report
            health_files = list(reports_dir.glob('health_report_*.json'))
            if health_files:
                latest_file = max(health_files, key=lambda x: x.stat().st_mtime)
                with open(latest_file, 'r') as f:
                    return json.load(f)
        return {}
    except Exception as e:
        st.error(f"Error loading health report: {str(e)}")
        return {}

def check_api_status():
    """Check API status."""
    try:
        api_status = {}
        
        # Check Helius API
        helius_key = os.environ.get('HELIUS_API_KEY')
        if helius_key:
            try:
                import httpx
                response = httpx.post(
                    f"https://rpc.helius.xyz/?api-key={helius_key}",
                    json={"jsonrpc": "2.0", "id": 1, "method": "getHealth"},
                    timeout=5
                )
                api_status['helius'] = {
                    'status': 'healthy' if response.status_code == 200 else 'error',
                    'response_time': response.elapsed.total_seconds() * 1000,
                    'status_code': response.status_code
                }
            except Exception as e:
                api_status['helius'] = {'status': 'error', 'error': str(e)}
        
        return api_status
    except Exception as e:
        return {'error': str(e)}

def get_log_metrics():
    """Get recent log metrics."""
    try:
        log_file = project_root / 'logs' / 'synergy7.log'
        if log_file.exists():
            # Read last 100 lines
            with open(log_file, 'r') as f:
                lines = f.readlines()
                recent_lines = lines[-100:] if len(lines) > 100 else lines
            
            # Count log levels
            info_count = sum(1 for line in recent_lines if 'INFO' in line)
            warning_count = sum(1 for line in recent_lines if 'WARNING' in line)
            error_count = sum(1 for line in recent_lines if 'ERROR' in line)
            
            # Get recent messages
            recent_messages = []
            for line in recent_lines[-10:]:
                if any(level in line for level in ['INFO', 'WARNING', 'ERROR']):
                    recent_messages.append(line.strip())
            
            return {
                'total_lines': len(recent_lines),
                'info_count': info_count,
                'warning_count': warning_count,
                'error_count': error_count,
                'recent_messages': recent_messages
            }
        return {}
    except Exception as e:
        return {'error': str(e)}

def main():
    """Main dashboard function."""
    st.title("🚀 Synergy7 Enhanced Trading System - Live Monitor")
    st.markdown("Real-time monitoring of system performance and trading operations")
    
    # Sidebar controls
    st.sidebar.title("⚙️ Controls")
    auto_refresh = st.sidebar.checkbox("Auto-refresh", value=True)
    refresh_interval = st.sidebar.slider("Refresh interval (seconds)", 5, 60, 10)
    
    if st.sidebar.button("🔄 Refresh Now"):
        st.experimental_rerun()
    
    # Create tabs
    tab1, tab2, tab3, tab4 = st.tabs(["📊 System Overview", "💻 System Metrics", "📋 Logs", "🔗 API Status"])
    
    # Get data
    system_metrics = get_system_metrics()
    health_report = get_health_report()
    log_metrics = get_log_metrics()
    api_status = check_api_status()
    
    with tab1:
        st.header("System Overview")
        
        # Key metrics
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            if system_metrics:
                memory_color = "normal" if system_metrics['memory_used_pct'] < 80 else "inverse"
                st.metric(
                    "Memory Usage",
                    f"{system_metrics['memory_used_pct']:.1f}%",
                    delta=None,
                    delta_color=memory_color
                )
            else:
                st.metric("Memory Usage", "N/A")
        
        with col2:
            if system_metrics:
                cpu_color = "normal" if system_metrics['cpu_usage_pct'] < 80 else "inverse"
                st.metric(
                    "CPU Usage",
                    f"{system_metrics['cpu_usage_pct']:.1f}%",
                    delta=None,
                    delta_color=cpu_color
                )
            else:
                st.metric("CPU Usage", "N/A")
        
        with col3:
            if system_metrics:
                disk_color = "normal" if system_metrics['disk_used_pct'] < 90 else "inverse"
                st.metric(
                    "Disk Usage",
                    f"{system_metrics['disk_used_pct']:.1f}%",
                    delta=None,
                    delta_color=disk_color
                )
            else:
                st.metric("Disk Usage", "N/A")
        
        with col4:
            if api_status.get('helius', {}).get('status') == 'healthy':
                st.metric("API Status", "✅ Healthy")
            else:
                st.metric("API Status", "❌ Error")
        
        # System health summary
        st.subheader("System Health Summary")
        if health_report:
            overall_status = health_report.get('overall_status', 'unknown')
            status_emoji = {
                'healthy': '✅',
                'warning': '⚠️',
                'error': '❌',
                'critical': '🚨'
            }.get(overall_status, '❓')
            
            st.markdown(f"**Overall Status**: {status_emoji} {overall_status.upper()}")
            
            # API connectivity
            api_conn = health_report.get('api_connectivity', {})
            if api_conn:
                st.markdown("**API Connectivity**:")
                for api_name, api_data in api_conn.items():
                    status = api_data.get('status', 'unknown')
                    emoji = '✅' if status == 'healthy' else '❌'
                    response_time = api_data.get('response_time_ms', 0)
                    st.markdown(f"- {api_name}: {emoji} {status} ({response_time:.0f}ms)")
        else:
            st.info("No health report available")
    
    with tab2:
        st.header("System Metrics")
        
        if system_metrics:
            # Resource usage charts
            col1, col2 = st.columns(2)
            
            with col1:
                # Memory usage
                fig_memory = go.Figure(go.Indicator(
                    mode = "gauge+number+delta",
                    value = system_metrics['memory_used_pct'],
                    domain = {'x': [0, 1], 'y': [0, 1]},
                    title = {'text': "Memory Usage (%)"},
                    delta = {'reference': 50},
                    gauge = {
                        'axis': {'range': [None, 100]},
                        'bar': {'color': "darkblue"},
                        'steps': [
                            {'range': [0, 50], 'color': "lightgray"},
                            {'range': [50, 80], 'color': "yellow"},
                            {'range': [80, 100], 'color': "red"}
                        ],
                        'threshold': {
                            'line': {'color': "red", 'width': 4},
                            'thickness': 0.75,
                            'value': 90
                        }
                    }
                ))
                fig_memory.update_layout(height=300)
                st.plotly_chart(fig_memory, use_container_width=True)
            
            with col2:
                # CPU usage
                fig_cpu = go.Figure(go.Indicator(
                    mode = "gauge+number+delta",
                    value = system_metrics['cpu_usage_pct'],
                    domain = {'x': [0, 1], 'y': [0, 1]},
                    title = {'text': "CPU Usage (%)"},
                    delta = {'reference': 25},
                    gauge = {
                        'axis': {'range': [None, 100]},
                        'bar': {'color': "darkgreen"},
                        'steps': [
                            {'range': [0, 50], 'color': "lightgray"},
                            {'range': [50, 75], 'color': "yellow"},
                            {'range': [75, 100], 'color': "red"}
                        ],
                        'threshold': {
                            'line': {'color': "red", 'width': 4},
                            'thickness': 0.75,
                            'value': 80
                        }
                    }
                ))
                fig_cpu.update_layout(height=300)
                st.plotly_chart(fig_cpu, use_container_width=True)
            
            # Detailed metrics
            st.subheader("Detailed System Information")
            
            metrics_data = {
                'Metric': [
                    'Total Memory', 'Available Memory', 'CPU Cores', 
                    'Total Disk', 'Free Disk', 'Process Memory', 'Process CPU'
                ],
                'Value': [
                    f"{system_metrics['memory_total_gb']} GB",
                    f"{system_metrics['memory_available_gb']} GB",
                    f"{system_metrics['cpu_cores']} cores",
                    f"{system_metrics['disk_total_gb']} GB",
                    f"{system_metrics['disk_free_gb']} GB",
                    f"{system_metrics['process_memory_mb']} MB",
                    f"{system_metrics['process_cpu_pct']:.1f}%"
                ]
            }
            
            df_metrics = pd.DataFrame(metrics_data)
            st.dataframe(df_metrics, use_container_width=True)
        else:
            st.error("Unable to collect system metrics")
    
    with tab3:
        st.header("System Logs")
        
        if log_metrics and 'error' not in log_metrics:
            # Log level summary
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                st.metric("Total Lines", log_metrics['total_lines'])
            with col2:
                st.metric("Info Messages", log_metrics['info_count'])
            with col3:
                st.metric("Warnings", log_metrics['warning_count'])
            with col4:
                st.metric("Errors", log_metrics['error_count'])
            
            # Log level distribution
            if log_metrics['total_lines'] > 0:
                log_data = {
                    'Level': ['INFO', 'WARNING', 'ERROR'],
                    'Count': [log_metrics['info_count'], log_metrics['warning_count'], log_metrics['error_count']]
                }
                df_logs = pd.DataFrame(log_data)
                
                fig_logs = px.pie(df_logs, values='Count', names='Level', 
                                title="Log Level Distribution (Last 100 lines)")
                st.plotly_chart(fig_logs, use_container_width=True)
            
            # Recent log messages
            st.subheader("Recent Log Messages")
            if log_metrics['recent_messages']:
                for message in log_metrics['recent_messages']:
                    if 'ERROR' in message:
                        st.error(message)
                    elif 'WARNING' in message:
                        st.warning(message)
                    else:
                        st.info(message)
            else:
                st.info("No recent log messages")
        else:
            st.error("Unable to read log files")
    
    with tab4:
        st.header("API Status")
        
        if api_status:
            for api_name, api_data in api_status.items():
                if api_name != 'error':
                    st.subheader(f"{api_name.title()} API")
                    
                    status = api_data.get('status', 'unknown')
                    if status == 'healthy':
                        st.success(f"Status: ✅ {status}")
                        response_time = api_data.get('response_time', 0)
                        st.info(f"Response Time: {response_time:.0f}ms")
                    else:
                        st.error(f"Status: ❌ {status}")
                        if 'error' in api_data:
                            st.error(f"Error: {api_data['error']}")
            
            if 'error' in api_status:
                st.error(f"API check error: {api_status['error']}")
        else:
            st.warning("No API status information available")
    
    # Footer
    st.markdown("---")
    st.markdown(f"**Last Updated**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Auto-refresh
    if auto_refresh:
        time.sleep(refresh_interval)
        st.experimental_rerun()

if __name__ == "__main__":
    main()
