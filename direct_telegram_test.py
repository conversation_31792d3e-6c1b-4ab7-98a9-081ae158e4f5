#!/usr/bin/env python3
"""
Direct Telegram Test

This script directly tests sending a message to Telegram using the provided bot token and chat ID.
"""

import asyncio
import httpx
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("direct_telegram_test")

async def send_telegram_message(bot_token, chat_id, message):
    """Send a message to Telegram."""
    url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
    
    payload = {
        "chat_id": chat_id,
        "text": message,
        "parse_mode": "Markdown"
    }
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(url, json=payload)
            response.raise_for_status()
            logger.info(f"Sent Telegram message: {response.json()}")
            return True
        except Exception as e:
            logger.error(f"Error sending Telegram message: {str(e)}")
            return False

async def main():
    """Main function."""
    # Hardcoded credentials
    bot_token = "8081711336:AAHkahgcFf3Fy5V9Bdy8dB5AyE4o-8BsyrQ"
    chat_id = "5135869709"
    
    logger.info(f"Using Telegram bot token: {bot_token}")
    logger.info(f"Using Telegram chat ID: {chat_id}")
    
    # Send test message
    message = f"""
🚨 *Synergy7 Trading System Alert* 🚨

This is a direct test alert from the Synergy7 Trading System.

*Type*: Direct Test Alert
*Time*: {datetime.now().isoformat()}

The system is ready for the 24-hour test.
"""
    
    success = await send_telegram_message(bot_token, chat_id, message)
    
    if success:
        logger.info("Test message sent successfully. Please check your Telegram.")
    else:
        logger.error("Failed to send test message.")

if __name__ == "__main__":
    asyncio.run(main())
