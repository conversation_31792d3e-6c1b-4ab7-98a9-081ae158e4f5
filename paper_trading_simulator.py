#!/usr/bin/env python3
"""
Paper Trading Simulator for Synergy7 Trading System

This script simulates paper trading by generating transactions and updating the wallet balance.
It reads and writes to the transaction history and wallet balance files directly.
"""

import os
import sys
import json
import time
import random
import logging
import asyncio
from datetime import datetime
from typing import Dict, List, Any, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("paper_trading_simulator")

class PaperTradingSimulator:
    """Paper Trading Simulator for Synergy7 Trading System."""

    def __init__(self, initial_balance: float = None, sol_price_usd: float = 180.0):
        """
        Initialize the paper trading simulator.

        Args:
            initial_balance: Initial balance in SOL (if None, will be calculated from 1000 USD)
            sol_price_usd: SOL price in USD (default is current market price ~$180)
        """
        self.sol_price_usd = sol_price_usd

        # Calculate initial balance in SOL based on 1000 USD if not provided
        if initial_balance is None:
            initial_balance = 1000.0 / self.sol_price_usd
            logger.info(f"Setting initial balance to {initial_balance:.4f} SOL (1000 USD at ${self.sol_price_usd:.2f}/SOL)")

        self.initial_balance = initial_balance
        self.current_balance = initial_balance

        # Transaction history
        self.transactions = []

        # Output directories - use the same directory as the PnL reporter
        self.output_dir = "phase_4_deployment/output"
        self.tx_history_path = os.path.join(self.output_dir, "transactions", "tx_history.json")
        self.wallet_balance_path = os.path.join(self.output_dir, "wallet", "wallet_balance.json")

        # Create output directories
        os.makedirs(os.path.join(self.output_dir, "transactions"), exist_ok=True)
        os.makedirs(os.path.join(self.output_dir, "wallet"), exist_ok=True)

        # Load existing data if available
        self.load_data()

        logger.info(f"Initialized paper trading simulator with initial balance: {self.initial_balance} SOL (${self.initial_balance * self.sol_price_usd:.2f})")
        logger.info(f"Current balance: {self.current_balance} SOL (${self.current_balance * self.sol_price_usd:.2f})")

    def load_data(self):
        """Load existing transaction history and wallet balance."""
        # Load transaction history
        if os.path.exists(self.tx_history_path):
            try:
                with open(self.tx_history_path, "r") as f:
                    data = json.load(f)
                    self.transactions = data.get("transactions", [])
                logger.info(f"Loaded {len(self.transactions)} transactions from {self.tx_history_path}")
            except Exception as e:
                logger.error(f"Error loading transaction history: {str(e)}")

        # Load wallet balance
        if os.path.exists(self.wallet_balance_path):
            try:
                with open(self.wallet_balance_path, "r") as f:
                    data = json.load(f)
                    # Only use the existing balance if it's close to what we expect
                    existing_balance = data.get("wallet_balance", {}).get("trading_wallet", 0.0)
                    existing_initial = data.get("initial_balance", 0.0)

                    # If the existing initial balance is very different from our calculated one,
                    # it's likely using an old SOL price, so we'll use our new value
                    if abs(existing_initial - self.initial_balance) > 1.0:
                        logger.info(f"Existing initial balance ({existing_initial} SOL) is very different from calculated ({self.initial_balance} SOL)")
                        logger.info(f"Using calculated initial balance of {self.initial_balance} SOL (1000 USD at ${self.sol_price_usd:.2f}/SOL)")
                    else:
                        self.current_balance = existing_balance
                        self.initial_balance = existing_initial
                        logger.info(f"Loaded wallet balance from {self.wallet_balance_path}")
            except Exception as e:
                logger.error(f"Error loading wallet balance: {str(e)}")

    def save_data(self):
        """Save transaction history and wallet balance."""
        # Save transaction history
        try:
            with open(self.tx_history_path, "w") as f:
                json.dump({"transactions": self.transactions}, f, indent=2)
            logger.info(f"Saved {len(self.transactions)} transactions to {self.tx_history_path}")
        except Exception as e:
            logger.error(f"Error saving transaction history: {str(e)}")

        # Save wallet balance
        try:
            wallet_balance = {
                "wallet_balance": {
                    "trading_wallet": self.current_balance,
                    "reserve_wallet": 0.0
                },
                "initial_balance": self.initial_balance,
                "timestamp": datetime.now().timestamp()
            }
            with open(self.wallet_balance_path, "w") as f:
                json.dump(wallet_balance, f, indent=2)
            logger.info(f"Saved wallet balance to {self.wallet_balance_path}")
        except Exception as e:
            logger.error(f"Error saving wallet balance: {str(e)}")

    def generate_transaction(self, action: Optional[str] = None) -> Dict[str, Any]:
        """
        Generate a simulated transaction.

        Args:
            action: Transaction action (BUY or SELL), if None, a random action will be chosen

        Returns:
            Simulated transaction
        """
        # Choose action if not provided
        if action is None:
            action = random.choice(["BUY", "SELL"])

        # Choose market
        market = random.choice(["SOL-USDC", "BTC-USDC", "ETH-USDC", "BONK-USDC", "JUP-USDC"])

        # Generate price based on market with current market prices (May 2024)
        if market == "SOL-USDC":
            price = round(random.uniform(175.0, 185.0), 2)  # SOL ~$180
        elif market == "BTC-USDC":
            price = round(random.uniform(67000.0, 70000.0), 2)  # BTC ~$68,500
        elif market == "ETH-USDC":
            price = round(random.uniform(3800.0, 4000.0), 2)  # ETH ~$3,900
        elif market == "BONK-USDC":
            price = round(random.uniform(0.00002, 0.00003), 8)  # BONK ~$0.000025
        elif market == "JUP-USDC":
            price = round(random.uniform(1.8, 2.2), 2)  # JUP ~$2.0

        # Generate size based on market and balance
        if market == "SOL-USDC":
            max_size = min(0.5, self.current_balance * 0.1) if action == "BUY" else min(0.5, self.current_balance * 0.1)
            size = round(random.uniform(0.1, max_size), 2)
        elif market == "BTC-USDC":
            max_size = min(0.01, self.current_balance * 0.1 / (price / self.sol_price_usd)) if action == "BUY" else min(0.01, self.current_balance * 0.1)
            size = round(random.uniform(0.001, max_size), 3)
        elif market == "ETH-USDC":
            max_size = min(0.1, self.current_balance * 0.1 / (price / self.sol_price_usd)) if action == "BUY" else min(0.1, self.current_balance * 0.1)
            size = round(random.uniform(0.01, max_size), 2)
        elif market == "BONK-USDC":
            max_size = min(1000000, self.current_balance * 0.1 / (price / self.sol_price_usd)) if action == "BUY" else min(1000000, self.current_balance * 0.1)
            size = round(random.uniform(10000, max_size), 0)
        elif market == "JUP-USDC":
            max_size = min(100, self.current_balance * 0.1 / (price / self.sol_price_usd)) if action == "BUY" else min(100, self.current_balance * 0.1)
            size = round(random.uniform(10, max_size), 0)

        # Generate fee
        fee = round(random.uniform(0.0001, 0.001), 4)

        # Calculate pre and post balance
        pre_balance = self.current_balance

        # Update balance based on action
        if action == "BUY":
            # Convert size to SOL equivalent
            sol_equivalent = size * price / self.sol_price_usd
            self.current_balance -= sol_equivalent
            self.current_balance -= fee
        elif action == "SELL":
            # Convert size to SOL equivalent
            sol_equivalent = size * price / self.sol_price_usd
            self.current_balance += sol_equivalent
            self.current_balance -= fee

        # Ensure balance doesn't go below 0
        self.current_balance = max(0.0, self.current_balance)

        post_balance = self.current_balance

        # Generate transaction
        transaction = {
            "signature": f"{random.randint(1, 9)}xjkXUAWHZXFSDAJ5QR6VZJK8XzfL9uKnA1KvRSojJZnKQZfPBVPwSYGQALM2vN6YQHXz7oEXYuKKN1XZRFdFQVV",
            "status": "confirmed",
            "timestamp": int(datetime.now().timestamp()),
            "type": "swap",
            "action": action,
            "market": market,
            "price": price,
            "size": size,
            "fee": fee,
            "pre_balance": pre_balance,
            "post_balance": post_balance
        }

        return transaction

    def add_transaction(self, transaction: Dict[str, Any]):
        """
        Add a transaction to the history.

        Args:
            transaction: Transaction to add
        """
        self.transactions.append(transaction)
        self.save_data()

        logger.info(f"Added {transaction['action']} transaction for {transaction['market']} at {transaction['price']}")
        logger.info(f"Size: {transaction['size']}, Fee: {transaction['fee']}")
        logger.info(f"Balance: {transaction['pre_balance']} -> {transaction['post_balance']}")

    async def run(self, duration: int = 3600, interval: int = 60):
        """
        Run the paper trading simulator.

        Args:
            duration: Duration in seconds
            interval: Interval between transactions in seconds
        """
        logger.info(f"Starting paper trading simulator for {duration} seconds with {interval} second intervals")

        start_time = time.time()
        end_time = start_time + duration

        try:
            while time.time() < end_time:
                # Generate and add transaction
                transaction = self.generate_transaction()
                self.add_transaction(transaction)

                # Wait for next transaction
                await asyncio.sleep(interval)
        except KeyboardInterrupt:
            logger.info("Paper trading simulator stopped by user")
        except Exception as e:
            logger.error(f"Error in paper trading simulator: {str(e)}")
        finally:
            # Save final data
            self.save_data()

            logger.info(f"Paper trading simulator completed after {time.time() - start_time:.2f} seconds")
            logger.info(f"Final balance: {self.current_balance} SOL (${self.current_balance * self.sol_price_usd:.2f})")
            logger.info(f"Total transactions: {len(self.transactions)}")

async def main():
    """Main function."""
    # Parse command line arguments
    import argparse
    parser = argparse.ArgumentParser(description="Paper Trading Simulator for Synergy7 Trading System")
    parser.add_argument("--initial-balance", type=float, help="Initial balance in SOL (default: 1000 USD worth of SOL)")
    parser.add_argument("--sol-price", type=float, default=180.0, help="SOL price in USD (default: $180.0)")
    parser.add_argument("--duration", type=int, default=3600, help="Duration in seconds")
    parser.add_argument("--interval", type=int, default=60, help="Interval between transactions in seconds")
    args = parser.parse_args()

    # Create simulator
    simulator = PaperTradingSimulator(
        initial_balance=args.initial_balance,
        sol_price_usd=args.sol_price
    )

    # Run simulator
    await simulator.run(
        duration=args.duration,
        interval=args.interval
    )

if __name__ == "__main__":
    asyncio.run(main())
