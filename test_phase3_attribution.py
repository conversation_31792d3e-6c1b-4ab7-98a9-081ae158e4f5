#!/usr/bin/env python3
"""
Test script for Phase 3: Strategy Performance Attribution.

This script tests the strategy attribution tracker and performance analyzer
to ensure they work correctly with the configuration-driven architecture.
"""

import os
import sys
import logging
import numpy as np
from datetime import datetime, timedelta
import yaml
import re

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import our new modules
from core.analytics.strategy_attribution import StrategyAttributionTracker
from core.analytics.performance_analyzer import PerformanceAnalyzer

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_test_config():
    """Load test configuration with environment variable substitution."""
    try:
        # Set test environment variables
        os.environ.setdefault('STRATEGY_ATTRIBUTION_ENABLED', 'true')
        os.environ.setdefault('ATTRIBUTION_WINDOW_DAYS', '30')
        os.environ.setdefault('MIN_TRADES_ATTRIBUTION', '5')  # Lower for testing
        
        # Load and process config
        with open('config.yaml', 'r') as f:
            config_text = f.read()
        
        # Simple environment variable substitution
        def replace_env_var(match):
            var_expr = match.group(1)
            if ':-' in var_expr:
                var_name, default_value = var_expr.split(':-', 1)
                return os.environ.get(var_name, default_value)
            else:
                return os.environ.get(var_expr, '')
        
        config_text = re.sub(r'\$\{([^}]+)\}', replace_env_var, config_text)
        config = yaml.safe_load(config_text)
        
        # Convert string values to appropriate types
        def convert_types(obj):
            if isinstance(obj, dict):
                return {k: convert_types(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_types(item) for item in obj]
            elif isinstance(obj, str):
                if obj.lower() == 'true':
                    return True
                elif obj.lower() == 'false':
                    return False
                else:
                    try:
                        if '.' in obj:
                            return float(obj)
                        else:
                            return int(obj)
                    except ValueError:
                        return obj
            return obj
        
        config = convert_types(config)
        return config
    except Exception as e:
        logger.error(f"Error loading config: {str(e)}")
        return {}

def generate_mock_trades(strategy_name: str, num_trades: int = 20) -> list:
    """Generate mock trades for testing."""
    trades = []
    base_time = datetime.now() - timedelta(days=30)
    
    # Different strategy characteristics
    strategy_configs = {
        'momentum_strategy': {'win_rate': 0.6, 'avg_win': 0.02, 'avg_loss': -0.015, 'volatility': 0.01},
        'mean_reversion': {'win_rate': 0.55, 'avg_win': 0.015, 'avg_loss': -0.012, 'volatility': 0.008},
        'breakout_strategy': {'win_rate': 0.45, 'avg_win': 0.03, 'avg_loss': -0.018, 'volatility': 0.015},
        'scalping_strategy': {'win_rate': 0.7, 'avg_win': 0.005, 'avg_loss': -0.008, 'volatility': 0.003},
        'swing_strategy': {'win_rate': 0.5, 'avg_win': 0.025, 'avg_loss': -0.02, 'volatility': 0.012}
    }
    
    config = strategy_configs.get(strategy_name, strategy_configs['momentum_strategy'])
    
    np.random.seed(hash(strategy_name) % 2**32)  # Consistent randomness per strategy
    
    for i in range(num_trades):
        # Determine if trade is winning
        is_winner = np.random.random() < config['win_rate']
        
        # Generate PnL
        if is_winner:
            base_pnl = config['avg_win']
        else:
            base_pnl = config['avg_loss']
        
        # Add some randomness
        pnl = base_pnl + np.random.normal(0, config['volatility'])
        
        # Generate trade data
        trade_time = base_time + timedelta(hours=i * 6)  # Trade every 6 hours
        
        trade = {
            'timestamp': trade_time.isoformat(),
            'symbol': 'SOL/USDC',
            'side': 'buy' if pnl > 0 else 'sell',
            'quantity': np.random.uniform(100, 1000),
            'price': 25.0 + np.random.normal(0, 2),
            'pnl': pnl,
            'commission': abs(pnl) * 0.001,  # 0.1% commission
            'slippage': abs(pnl) * 0.0005,  # 0.05% slippage
            'signal_strength': np.random.uniform(0.5, 1.0),
            'market_regime': np.random.choice(['trending_up', 'trending_down', 'ranging', 'volatile']),
            'trade_id': f"{strategy_name}_trade_{i}"
        }
        
        trades.append(trade)
    
    return trades

def test_strategy_attribution_tracker(config):
    """Test the strategy attribution tracker."""
    logger.info("Testing Strategy Attribution Tracker...")
    
    try:
        # Initialize tracker
        tracker = StrategyAttributionTracker(config)
        logger.info("Strategy Attribution Tracker initialized successfully")
        
        # Generate mock trades for multiple strategies
        strategies = ['momentum_strategy', 'mean_reversion', 'breakout_strategy', 'scalping_strategy', 'swing_strategy']
        
        for strategy_name in strategies:
            trades = generate_mock_trades(strategy_name, 15)
            
            # Record trades
            for trade in trades:
                tracker.record_trade(strategy_name, trade)
            
            logger.info(f"Recorded {len(trades)} trades for {strategy_name}")
        
        # Test performance calculation for each strategy
        for strategy_name in strategies:
            performance = tracker.calculate_strategy_performance(strategy_name)
            if performance:
                logger.info(f"{strategy_name} performance:")
                logger.info(f"  Total PnL: {performance['net_pnl']:.4f}")
                logger.info(f"  Win Rate: {performance['win_rate']:.2%}")
                logger.info(f"  Sharpe Ratio: {performance['sharpe_ratio']:.3f}")
                logger.info(f"  Max Drawdown: {performance['max_drawdown']:.4f}")
        
        # Test portfolio attribution
        strategy_weights = {strategy: 1.0/len(strategies) for strategy in strategies}
        attribution = tracker.calculate_portfolio_attribution(strategy_weights)
        
        if attribution:
            portfolio_metrics = attribution['portfolio_metrics']
            logger.info(f"Portfolio Attribution:")
            logger.info(f"  Total Portfolio PnL: {portfolio_metrics['total_portfolio_pnl']:.4f}")
            logger.info(f"  Weighted Win Rate: {portfolio_metrics['weighted_win_rate']:.2%}")
            logger.info(f"  Weighted Sharpe: {portfolio_metrics['weighted_sharpe_ratio']:.3f}")
        
        # Test strategy ranking
        rankings = tracker.rank_strategies('sharpe_ratio')
        logger.info(f"Strategy Rankings by Sharpe Ratio:")
        for i, (strategy, sharpe) in enumerate(rankings[:3]):
            logger.info(f"  {i+1}. {strategy}: {sharpe:.3f}")
        
        # Test strategy summary
        test_strategy = strategies[0]
        summary = tracker.get_strategy_summary(test_strategy)
        logger.info(f"Strategy Summary for {test_strategy}:")
        logger.info(f"  Total Trades: {summary.get('total_trades', 0)}")
        logger.info(f"  Trend: {summary.get('trend_7d', 'unknown')}")
        logger.info(f"  Current Weight: {summary.get('current_weight', 0):.2%}")
        
        # Test attribution summary
        attribution_summary = tracker.get_attribution_summary()
        logger.info(f"Attribution Summary:")
        logger.info(f"  Total Strategies: {attribution_summary.get('total_strategies', 0)}")
        logger.info(f"  Total Trades Tracked: {attribution_summary.get('total_trades_tracked', 0)}")
        
        return True
    
    except Exception as e:
        logger.error(f"Error testing strategy attribution tracker: {str(e)}")
        return False

def test_performance_analyzer(config):
    """Test the performance analyzer."""
    logger.info("Testing Performance Analyzer...")
    
    try:
        # Initialize analyzer
        analyzer = PerformanceAnalyzer(config)
        logger.info("Performance Analyzer initialized successfully")
        
        # Create tracker to get performance data
        tracker = StrategyAttributionTracker(config)
        
        # Generate mock trades for multiple strategies with different performance profiles
        strategies_config = {
            'excellent_strategy': {'win_rate': 0.8, 'avg_win': 0.03, 'avg_loss': -0.01, 'volatility': 0.005},
            'good_strategy': {'win_rate': 0.65, 'avg_win': 0.02, 'avg_loss': -0.015, 'volatility': 0.008},
            'average_strategy': {'win_rate': 0.55, 'avg_win': 0.015, 'avg_loss': -0.015, 'volatility': 0.01},
            'poor_strategy': {'win_rate': 0.4, 'avg_win': 0.01, 'avg_loss': -0.02, 'volatility': 0.015},
            'very_poor_strategy': {'win_rate': 0.3, 'avg_win': 0.005, 'avg_loss': -0.025, 'volatility': 0.02}
        }
        
        # Generate and record trades
        for strategy_name in strategies_config.keys():
            trades = generate_mock_trades(strategy_name, 20)
            for trade in trades:
                tracker.record_trade(strategy_name, trade)
        
        # Get performance data
        strategy_performance = {}
        for strategy_name in strategies_config.keys():
            perf = tracker.calculate_strategy_performance(strategy_name)
            if perf:
                strategy_performance[strategy_name] = perf
        
        # Test trend analysis
        trend_analysis = analyzer.analyze_strategy_trends(strategy_performance)
        logger.info("Strategy Trend Analysis:")
        for strategy, trend_data in trend_analysis.items():
            logger.info(f"  {strategy}: {trend_data['trend']} ({trend_data['performance_class']})")
        
        # Test underperformance identification
        underperforming = analyzer.identify_underperforming_strategies(strategy_performance)
        logger.info(f"Underperforming Strategies: {len(underperforming)}")
        for underperformer in underperforming:
            logger.info(f"  {underperformer['strategy_name']}: {underperformer['severity']} - {underperformer['issues']}")
        
        # Test optimization recommendations
        current_weights = {strategy: 1.0/len(strategies_config) for strategy in strategies_config.keys()}
        recommendations = analyzer.generate_optimization_recommendations(strategy_performance, current_weights)
        logger.info(f"Optimization Recommendations: {len(recommendations)}")
        for rec in recommendations[:3]:  # Show top 3
            logger.info(f"  {rec['type']} for {rec['strategy']}: {rec['reason']}")
        
        # Test portfolio analysis
        attribution_data = tracker.calculate_portfolio_attribution(current_weights)
        portfolio_analysis = analyzer.analyze_portfolio_performance(attribution_data)
        
        if portfolio_analysis:
            logger.info("Portfolio Analysis:")
            logger.info(f"  Health Status: {portfolio_analysis.get('health_status', 'unknown')}")
            logger.info(f"  Health Score: {portfolio_analysis.get('health_score', 0)}/100")
            logger.info(f"  Effective Strategies: {portfolio_analysis.get('effective_strategies', 0):.1f}")
            logger.info(f"  Top 3 Contribution: {portfolio_analysis.get('top_3_contribution_pct', 0):.1f}%")
        
        # Test analysis summary
        analysis_summary = analyzer.get_analysis_summary()
        logger.info(f"Analysis Summary: {analysis_summary.get('enabled', False)}")
        
        return True
    
    except Exception as e:
        logger.error(f"Error testing performance analyzer: {str(e)}")
        return False

def main():
    """Main test function."""
    logger.info("Starting Phase 3 Strategy Attribution Tests...")
    
    # Load configuration
    config = load_test_config()
    if not config:
        logger.error("Failed to load configuration")
        return False
    
    logger.info("Configuration loaded successfully")
    
    # Test results
    results = {}
    
    # Test strategy attribution tracker
    results['strategy_attribution_tracker'] = test_strategy_attribution_tracker(config)
    
    # Test performance analyzer
    results['performance_analyzer'] = test_performance_analyzer(config)
    
    # Print results summary
    logger.info("\n" + "="*50)
    logger.info("PHASE 3 STRATEGY ATTRIBUTION TEST RESULTS")
    logger.info("="*50)
    
    all_passed = True
    for test_name, passed in results.items():
        status = "PASS" if passed else "FAIL"
        logger.info(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    logger.info("="*50)
    overall_status = "ALL TESTS PASSED" if all_passed else "SOME TESTS FAILED"
    logger.info(f"Overall Status: {overall_status}")
    logger.info("="*50)
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
