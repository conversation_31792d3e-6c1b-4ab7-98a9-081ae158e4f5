{"dataset_name": "solana_meme_master", "version": "1.0.0", "created_at": "2024-05-22T12:00:00Z", "description": "Enhanced dataset for Solana meme tokens with additional metrics for the new strategy module", "tokens": [{"symbol": "SOL", "name": "Solana", "address": "So11111111111111111111111111111111111111112", "decimals": 9, "category": "layer1"}, {"symbol": "JUP", "name": "Jupiter", "address": "JUPyiwrYJFskUPiHa7hkeR8VUtAeFoSYbKedZNsDvCN", "decimals": 6, "category": "defi"}, {"symbol": "BONK", "name": "Bonk", "address": "DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263", "decimals": 5, "category": "meme"}], "strategies": [{"id": "momentum_sol_usdc", "name": "SOL-USDC Momentum Strategy", "type": "momentum", "parameters": {"fast_period": 12, "slow_period": 26, "signal_period": 9, "threshold": 0.0, "confidence_multiplier": 1.5}}, {"id": "momentum_jup_usdc", "name": "JUP-USDC Momentum Strategy", "type": "momentum", "parameters": {"fast_period": 12, "slow_period": 26, "signal_period": 9, "threshold": 0.0, "confidence_multiplier": 1.5}}, {"id": "mean_reversion_bonk_usdc", "name": "BONK-USDC Mean Reversion Strategy", "type": "mean_reversion", "parameters": {"lookback_period": 14, "overbought_threshold": 70, "oversold_threshold": 30, "confidence_multiplier": 1.2}}], "time_period": {"start": "2024-05-08T00:00:00Z", "end": "2024-05-08T01:00:00Z", "interval": "1m"}, "metrics": {"momentum_sol_usdc": {"win_rate": 0.8, "profit_factor": 3.25, "sharpe_ratio": 2.15, "max_drawdown": 0.0215, "total_trades": 5, "profitable_trades": 4, "losing_trades": 1, "avg_profit_pct": 0.0489, "avg_loss_pct": 0.0215, "avg_trade_duration_seconds": 595}, "momentum_jup_usdc": {"win_rate": 0.8, "profit_factor": 3.05, "sharpe_ratio": 1.95, "max_drawdown": 0.0267, "total_trades": 5, "profitable_trades": 4, "losing_trades": 1, "avg_profit_pct": 0.0473, "avg_loss_pct": 0.0267, "avg_trade_duration_seconds": 595}, "mean_reversion_bonk_usdc": {"win_rate": 1.0, "profit_factor": 0.0, "sharpe_ratio": 2.85, "max_drawdown": 0.0, "total_trades": 6, "profitable_trades": 6, "losing_trades": 0, "avg_profit_pct": 0.0326, "avg_loss_pct": 0.0, "avg_trade_duration_seconds": 595}}, "enhanced_features": ["WalletTxCount", "New<PERSON><PERSON><PERSON>", "DeployerFunded", "SlippageScore", "alpha_wallet_score", "liquidity_score", "volatility_score", "momentum_score"], "data_sources": ["Birdeye API", "Helius API", "Jupiter API"]}