{"liquidity_events": [{"timestamp": **********, "event": "add_liquidity", "token_amount": 100000000.0, "usdc_amount": 1000.0, "wallet": "wallet_deployer", "trigger": "initial liquidity"}, {"timestamp": **********, "event": "add_liquidity", "token_amount": 50000000.0, "usdc_amount": 550.0, "wallet": "LP_provider_2", "trigger": "price stability"}, {"timestamp": **********, "event": "add_liquidity", "token_amount": 75000000.0, "usdc_amount": 900.0, "wallet": "LP_provider_3", "trigger": "increasing volume"}, {"timestamp": **********, "event": "remove_liquidity", "token_amount": 25000000.0, "usdc_amount": 325.0, "wallet": "LP_provider_2", "trigger": "profit taking"}, {"timestamp": **********, "event": "add_liquidity", "token_amount": 100000000.0, "usdc_amount": 1620.0, "wallet": "LP_provider_4", "trigger": "price uptrend"}, {"timestamp": **********, "event": "remove_liquidity", "token_amount": 50000000.0, "usdc_amount": 820.0, "wallet": "wallet_deployer", "trigger": "before price crash"}, {"timestamp": **********, "event": "remove_liquidity", "token_amount": 30000000.0, "usdc_amount": 540.0, "wallet": "LP_provider_3", "trigger": "profit taking"}, {"timestamp": **********, "event": "add_liquidity", "token_amount": 150000000.0, "usdc_amount": 3000.0, "wallet": "LP_provider_5", "trigger": "strong uptrend"}]}