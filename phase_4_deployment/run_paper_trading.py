#!/usr/bin/env python3
"""
Run Paper Trading Script for Synergy7 Trading System

This script runs the Synergy7 Trading System in paper trading mode with dry run disabled.
This allows the system to simulate trades with real market data but without using real funds.
"""

import os
import sys
import asyncio
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("paper_trading")

# Add parent directory to path
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

# Import necessary modules
from phase_4_deployment.start_live_trading import run_live_trading

async def main():
    """Main function to run paper trading."""
    print("\n======================================")
    print("STARTING SYNERGY7 SYSTEM IN PAPER TRADING MODE")
    print("======================================\n")
    
    print("This will simulate trades with real market data but without using real funds.")
    print("The system will execute simulated transactions and track performance metrics.\n")
    
    # Set environment variables for paper trading
    os.environ['DRY_RUN'] = 'false'  # Disable dry run to allow transaction simulation
    os.environ['PAPER_TRADING'] = 'true'  # Enable paper trading
    os.environ['TRADING_ENABLED'] = 'true'  # Enable trading
    
    # Set initial balance to 40 SOL (equivalent to 1000 USD at $25/SOL)
    os.environ['INITIAL_BALANCE_SOL'] = '40.0'
    
    # Set wallet address (use test wallet for paper trading)
    if not os.environ.get('WALLET_ADDRESS'):
        os.environ['WALLET_ADDRESS'] = 'DummyWalletAddressForPaperTrading123456789'
    
    # Print configuration summary
    print(f"Paper trading configuration:")
    print(f"DRY_RUN: {os.environ.get('DRY_RUN')}")
    print(f"PAPER_TRADING: {os.environ.get('PAPER_TRADING')}")
    print(f"TRADING_ENABLED: {os.environ.get('TRADING_ENABLED')}")
    print(f"INITIAL_BALANCE_SOL: {os.environ.get('INITIAL_BALANCE_SOL')} SOL (1000 USD)")
    print(f"WALLET_ADDRESS: {os.environ.get('WALLET_ADDRESS')}\n")
    
    # Create output directories
    os.makedirs('phase_4_deployment/output/transactions', exist_ok=True)
    os.makedirs('phase_4_deployment/output/wallet', exist_ok=True)
    os.makedirs('phase_4_deployment/output/signals', exist_ok=True)
    
    # Create initial wallet balance file
    import json
    wallet_balance_path = 'phase_4_deployment/output/wallet/wallet_balance.json'
    initial_balance = float(os.environ.get('INITIAL_BALANCE_SOL', 40.0))
    
    wallet_balance = {
        "wallet_balance": {
            "trading_wallet": initial_balance,
            "reserve_wallet": 0.0
        },
        "initial_balance": initial_balance,
        "timestamp": datetime.now().timestamp()
    }
    
    with open(wallet_balance_path, 'w') as f:
        json.dump(wallet_balance, f, indent=2)
    
    print(f"Created initial wallet balance file with {initial_balance} SOL")
    
    # Create empty transaction history file
    tx_history_path = 'phase_4_deployment/output/transactions/tx_history.json'
    tx_history = {
        "transactions": []
    }
    
    with open(tx_history_path, 'w') as f:
        json.dump(tx_history, f, indent=2)
    
    print(f"Created empty transaction history file")
    
    print("\nStarting paper trading in 5 seconds...")
    print("Press Ctrl+C to stop at any time.\n")
    
    # Wait 5 seconds before starting
    for i in range(5, 0, -1):
        print(f"Starting in {i} seconds...", end="\r")
        await asyncio.sleep(1)
    
    print("\nPaper trading started!\n")
    
    # Run live trading in paper trading mode
    await run_live_trading()

if __name__ == "__main__":
    asyncio.run(main())
