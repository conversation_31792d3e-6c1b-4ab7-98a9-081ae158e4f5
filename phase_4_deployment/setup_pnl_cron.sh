#!/bin/bash
# Setup PnL Cron Job
# This script sets up a cron job to automatically send PnL reports at regular intervals.

# Get the absolute path to the report_pnl.sh script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
REPORT_SCRIPT="$SCRIPT_DIR/report_pnl.sh"

# Check if the report_pnl.sh script exists
if [ ! -f "$REPORT_SCRIPT" ]; then
    echo "Error: report_pnl.sh script not found at $REPORT_SCRIPT"
    exit 1
fi

# Make sure the script is executable
chmod +x "$REPORT_SCRIPT"

# Create a temporary file for the crontab
TEMP_CRONTAB=$(mktemp)

# Export the current crontab
crontab -l > "$TEMP_CRONTAB" 2>/dev/null || echo "# Synergy7 Trading System Cron Jobs" > "$TEMP_CRONTAB"

# Check if the cron job already exists
if grep -q "report_pnl.sh" "$TEMP_CRONTAB"; then
    echo "PnL report cron job already exists. Updating..."
    # Remove the existing cron job
    grep -v "report_pnl.sh" "$TEMP_CRONTAB" > "${TEMP_CRONTAB}.new"
    mv "${TEMP_CRONTAB}.new" "$TEMP_CRONTAB"
fi

# Add the cron job to run every hour
echo "# Run PnL report every hour" >> "$TEMP_CRONTAB"
echo "0 * * * * $REPORT_SCRIPT >> $SCRIPT_DIR/output/pnl_report.log 2>&1" >> "$TEMP_CRONTAB"

# Install the new crontab
crontab "$TEMP_CRONTAB"

# Clean up
rm "$TEMP_CRONTAB"

echo "PnL report cron job set up to run every hour."
echo "You can also run it manually with: $REPORT_SCRIPT"
