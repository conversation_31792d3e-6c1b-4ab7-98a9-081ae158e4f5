#!/bin/bash
# Run Simulation Test for Synergy7 Unified Dashboard
# This script runs a 1-minute simulation and the unified dashboard for testing.

# Set colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Print header
function print_header() {
    echo -e "\n${YELLOW}=== $1 ===${NC}\n"
}

# Print success message
function print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

# Print error message
function print_error() {
    echo -e "${RED}✗ $1${NC}"
}

# Set the directory to the script's directory
cd "$(dirname "$0")"

# Parse command line arguments
DURATION=60
INTERVAL=1.0
PORT=8502
HOST="0.0.0.0"
HEADLESS=false

while [[ $# -gt 0 ]]; do
    key="$1"
    case $key in
        --duration)
            DURATION="$2"
            shift
            shift
            ;;
        --interval)
            INTERVAL="$2"
            shift
            shift
            ;;
        --port)
            PORT="$2"
            shift
            shift
            ;;
        --host)
            HOST="$2"
            shift
            shift
            ;;
        --headless)
            HEADLESS=true
            shift
            ;;
        --help)
            echo "Usage: $0 [options]"
            echo "Options:"
            echo "  --duration SEC    Test duration in seconds (default: 60)"
            echo "  --interval SEC    Interval between updates in seconds (default: 1.0)"
            echo "  --port PORT       Port to run Streamlit on (default: 8502)"
            echo "  --host HOST       Host to run Streamlit on (default: 0.0.0.0)"
            echo "  --headless        Run in headless mode"
            echo "  --help            Show this help message"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

print_header "Starting Synergy7 Simulation Test"

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    print_error "Python 3 is not installed"
    exit 1
fi

# Check if the simulation script exists
SIMULATION_SCRIPT="unified_dashboard/simulation.py"
if [ ! -f "$SIMULATION_SCRIPT" ]; then
    print_error "Simulation script not found: $SIMULATION_SCRIPT"
    exit 1
fi

# Check if the dashboard script exists
DASHBOARD_SCRIPT="unified_dashboard/run_dashboard.py"
if [ ! -f "$DASHBOARD_SCRIPT" ]; then
    print_error "Dashboard script not found: $DASHBOARD_SCRIPT"
    exit 1
fi

# Make the scripts executable
chmod +x "$SIMULATION_SCRIPT" "$DASHBOARD_SCRIPT"

# Create output directory if it doesn't exist
mkdir -p output

# Start the dashboard in the background
print_header "Starting Unified Dashboard"

# Build dashboard command
DASHBOARD_CMD="python3 $DASHBOARD_SCRIPT --port $PORT --host $HOST"

if [ "$HEADLESS" = true ]; then
    DASHBOARD_CMD="$DASHBOARD_CMD --headless"
fi

print_success "Running Synergy7 Unified Dashboard on $HOST:$PORT"
echo -e "${YELLOW}Command: $DASHBOARD_CMD${NC}"

# Start the dashboard in the background
$DASHBOARD_CMD &
DASHBOARD_PID=$!

# Wait for the dashboard to start
sleep 2

# Run the simulation
print_header "Running Simulation"

# Build simulation command
SIMULATION_CMD="python3 $SIMULATION_SCRIPT --output-dir output --duration $DURATION --interval $INTERVAL"

print_success "Running Synergy7 Simulation for $DURATION seconds"
echo -e "${YELLOW}Command: $SIMULATION_CMD${NC}"

# Run the simulation
$SIMULATION_CMD

# Wait for the dashboard to be used
print_header "Simulation Completed"
print_success "The dashboard is now running with simulated data"
print_success "Press Ctrl+C to stop the dashboard when you're done"

# Wait for the dashboard process
wait $DASHBOARD_PID

# Exit with the dashboard's exit code
exit $?
