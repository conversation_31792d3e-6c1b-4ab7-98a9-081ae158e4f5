#!/bin/bash
# Run Live Dashboard for Synergy7 Trading System
# This script runs the live trading integration and the unified dashboard.

# Set colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Print header
function print_header() {
    echo -e "\n${YELLOW}=== $1 ===${NC}\n"
}

# Print success message
function print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

# Print error message
function print_error() {
    echo -e "${RED}✗ $1${NC}"
}

# Set the directory to the script's directory
cd "$(dirname "$0")"

# Parse command line arguments
PORT=8501
HOST="0.0.0.0"
HEADLESS=false

while [[ $# -gt 0 ]]; do
    key="$1"
    case $key in
        --port)
            PORT="$2"
            shift
            shift
            ;;
        --host)
            HOST="$2"
            shift
            shift
            ;;
        --headless)
            HEADLESS=true
            shift
            ;;
        --help)
            echo "Usage: $0 [options]"
            echo "Options:"
            echo "  --port PORT       Port to run Streamlit on (default: 8501)"
            echo "  --host HOST       Host to run Streamlit on (default: 0.0.0.0)"
            echo "  --headless        Run in headless mode"
            echo "  --help            Show this help message"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

print_header "Starting Synergy7 Live Dashboard"

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    print_error "Python 3 is not installed"
    exit 1
fi

# Check if the live integration script exists
INTEGRATION_SCRIPT="run_live_integration.py"
if [ ! -f "$INTEGRATION_SCRIPT" ]; then
    print_error "Live integration script not found: $INTEGRATION_SCRIPT"
    exit 1
fi

# Check if the dashboard script exists
DASHBOARD_SCRIPT="unified_dashboard/run_dashboard.py"
if [ ! -f "$DASHBOARD_SCRIPT" ]; then
    print_error "Dashboard script not found: $DASHBOARD_SCRIPT"
    exit 1
fi

# Make the scripts executable
chmod +x "$INTEGRATION_SCRIPT" "$DASHBOARD_SCRIPT"

# Create output directory if it doesn't exist
mkdir -p output

# Start the live integration in the background
print_header "Starting Live Trading Integration"

# Build integration command
INTEGRATION_CMD="python3 $INTEGRATION_SCRIPT"

print_success "Running Synergy7 Live Trading Integration"
echo -e "${YELLOW}Command: $INTEGRATION_CMD${NC}"

# Start the live integration in the background
$INTEGRATION_CMD &
INTEGRATION_PID=$!

# Wait for the live integration to start
sleep 2

# Start the dashboard in the background
print_header "Starting Unified Dashboard"

# Build dashboard command
DASHBOARD_CMD="python3 $DASHBOARD_SCRIPT --port $PORT --host $HOST"

if [ "$HEADLESS" = true ]; then
    DASHBOARD_CMD="$DASHBOARD_CMD --headless"
fi

print_success "Running Synergy7 Unified Dashboard on $HOST:$PORT"
echo -e "${YELLOW}Command: $DASHBOARD_CMD${NC}"

# Start the dashboard in the background
$DASHBOARD_CMD &
DASHBOARD_PID=$!

# Wait for the dashboard to start
sleep 2

print_header "Synergy7 Live Dashboard is Running"
print_success "Live Trading Integration PID: $INTEGRATION_PID"
print_success "Unified Dashboard PID: $DASHBOARD_PID"
print_success "Press Ctrl+C to stop all processes"

# Trap Ctrl+C to kill all processes
trap 'kill $INTEGRATION_PID $DASHBOARD_PID 2>/dev/null' INT TERM

# Wait for processes to exit
echo "Press Ctrl+C to stop all processes"
wait $INTEGRATION_PID $DASHBOARD_PID

# Kill all processes (if they haven't exited already)
kill $INTEGRATION_PID $DASHBOARD_PID 2>/dev/null

print_header "Synergy7 Live Dashboard Stopped"

exit 0
