#!/usr/bin/env python3
"""
Start Synergy7 System in live trading mode.
This script enables live trading mode for the Synergy7 System.
"""

import os
import sys
import time
import json
import logging
import asyncio
from datetime import datetime

# Add parent directory to path to import modules
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

# Import configuration loader
from utils.config.loader import load_config

# Load configuration
config = load_config(environment='production', components=['carbon_core'])

# Configure logging
logging_level = config.get('monitoring', {}).get('log_level', 'INFO').upper()
logging.basicConfig(
    level=getattr(logging, logging_level),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("live_trading")

# Import monitoring modules
try:
    from shared.utils.monitoring import get_monitoring_service
    # Check if setup_telegram_alerts exists
    try:
        from shared.utils.monitoring import setup_telegram_alerts
    except ImportError:
        # Define a placeholder function
        def setup_telegram_alerts(bot_token, chat_id, rate_limit_seconds=300):
            """Placeholder for setup_telegram_alerts."""
            logger.warning("setup_telegram_alerts not available, using placeholder")
            return lambda *args, **kwargs: None
except ImportError:
    # Use mock monitoring service
    from phase_4_deployment.monitoring.mock_monitoring_service import get_monitoring_service
    # Define a placeholder function
    def setup_telegram_alerts(bot_token, chat_id, rate_limit_seconds=300):
        """Placeholder for setup_telegram_alerts."""
        logger.warning("setup_telegram_alerts not available, using placeholder")
        return lambda *args, **kwargs: None

# Import health server
from phase_4_deployment.monitoring.health_server import start_health_server

async def check_wallet_balance():
    """Check wallet balance before starting live trading."""
    try:
        # Import necessary modules
        from rpc_execution.helius_client import HeliusClient

        # Get wallet address from config
        wallet_address = config.get('wallet', {}).get('address')
        if not wallet_address:
            wallet_address = os.environ.get('WALLET_ADDRESS')
            if not wallet_address:
                logger.error("Wallet address not found in config or environment variables")
                return False

        # Get Helius API key from config
        helius_api_key = config.get('apis', {}).get('helius', {}).get('api_key')
        if not helius_api_key:
            helius_api_key = os.environ.get('HELIUS_API_KEY')
            if not helius_api_key:
                logger.error("Helius API key not found in config or environment variables")
                return False

        # Get RPC URL from config
        rpc_url = config.get('apis', {}).get('helius', {}).get('rpc_endpoint')
        if not rpc_url:
            rpc_url = f'https://mainnet.helius-rpc.com/?api-key={helius_api_key}'

        client = HeliusClient(rpc_url)
        balance_data = await client.get_balance(wallet_address)

        print(f'Wallet: {wallet_address}')

        # Extract the balance in SOL from the response
        if isinstance(balance_data, dict) and 'balance_sol' in balance_data:
            balance_sol = balance_data['balance_sol']
            print(f'Balance: {balance_sol} SOL')

            # Get minimum balance thresholds from config
            min_balance_warning = config.get('wallet', {}).get('min_balance_warning', 1.0)
            min_balance_critical = config.get('wallet', {}).get('min_balance_critical', 0.1)

            # Check if balance is sufficient
            if balance_sol < min_balance_critical:
                print('\033[0;31mWARNING: Balance is very low!\033[0m')
                return False
            elif balance_sol < min_balance_warning:
                print('\033[0;33mWARNING: Balance is low!\033[0m')
                return True
            else:
                print('\033[0;32mBalance is sufficient for trading\033[0m')
                return True
        else:
            print(f'Balance data: {balance_data}')
            print('\033[0;31mWARNING: Could not determine balance!\033[0m')
            return False

    except Exception as e:
        logger.error(f"Error checking wallet balance: {str(e)}")
        return False
    finally:
        if 'client' in locals():
            await client.close()

async def run_live_trading(filter_chain=None, signal_enricher=None, rl_data_collector=None):
    """
    Run the system in live trading mode.

    Args:
        filter_chain: Filter chain for signal filtering
        signal_enricher: Signal enricher for signal enrichment
        rl_data_collector: RL data collector for RL data collection
    """

    # Log component status
    if filter_chain:
        logger.info(f"Using filter chain with {len(filter_chain.filters)} filters")
    else:
        logger.info("No filter chain provided, signals will not be filtered")

    if signal_enricher:
        logger.info(f"Using signal enricher with algorithm: {signal_enricher.ranking_algorithm}")
    else:
        logger.info("No signal enricher provided, signals will not be enriched")

    if rl_data_collector:
        logger.info(f"Using RL data collector with data collection: {rl_data_collector.data_collection}")
    else:
        logger.info("No RL data collector provided, RL data will not be collected")
    try:
        # Import necessary modules
        from data_router.birdeye_scanner import BirdeyeScanner
        from data_router.whale_watcher import main as track_whales
        from phase_4_deployment.signal_generator.signal_enricher import SignalEnricher
        from rpc_execution.tx_builder import TxBuilder
        from rpc_execution.helius_client import HeliusClient
        from rpc_execution.lil_jito_client import LilJitoClient
        from rpc_execution.transaction_executor import TransactionExecutor
        from stream_data_ingestor.client import StreamDataIngestor, StreamType
        from phase_4_deployment.core.carbon_core_manager import CarbonCoreManager

        # Try to import the transaction preparation service
        try:
            from solana_tx_utils.tx_prep import TransactionPreparationService
            USING_TX_PREP = True
        except ImportError:
            USING_TX_PREP = False
            logger.warning("Transaction preparation service not available, using legacy transaction building")

        # Get monitoring service
        monitoring = get_monitoring_service()

        # Initialize components
        wallet_address = os.environ.get('WALLET_ADDRESS')
        keypair_path = os.environ.get('KEYPAIR_PATH')
        tx_builder = TxBuilder(wallet_address)

        # Initialize RPC clients
        helius_client = HeliusClient()
        liljito_client = LilJitoClient()

        # Initialize Carbon Core Manager
        carbon_core_config_path = os.environ.get('CARBON_CORE_CONFIG_PATH', 'carbon_core_config.yaml')
        carbon_core_manager = CarbonCoreManager(config_path=carbon_core_config_path)

        # Start Carbon Core
        carbon_core_started = await carbon_core_manager.start()
        if carbon_core_started:
            logger.info(f"Carbon Core started successfully (using fallback: {carbon_core_manager.using_fallback})")

            # Register Carbon Core health check with monitoring
            monitoring.register_component('carbon_core', carbon_core_manager.is_healthy)
        else:
            logger.warning("Failed to start Carbon Core, some advanced features may not be available")

        # Initialize transaction executor with Helius client (primary)
        executor = TransactionExecutor(
            rpc_client=helius_client,
            keypair_path=keypair_path,
            max_retries=3,
            retry_delay=1.0
        )

        # Initialize transaction executor with Lil' Jito client (for bundles)
        liljito_executor = TransactionExecutor(
            rpc_client=liljito_client,
            keypair_path=keypair_path,
            max_retries=3,
            retry_delay=1.0
        )

        # Initialize transaction preparation service if available
        tx_prep_service = None
        if USING_TX_PREP and keypair_path:
            # Get RPC URL from environment or use default
            rpc_url = os.environ.get('SOLANA_RPC_URL', 'https://api.mainnet-beta.solana.com')
            tx_prep_service = TransactionPreparationService(rpc_url)

            # Load keypair
            tx_prep_service.load_keypair('default', keypair_path)
            logger.info(f"Loaded keypair from {keypair_path}")

        # Initialize stream data ingestor for Lil' Jito
        liljito_api_key = os.environ.get('LILJITO_QUICKNODE_API_KEY')
        if liljito_api_key:
            liljito_stream = StreamDataIngestor(
                stream_type=StreamType.CUSTOM_WEBSOCKET,
                stream_url=f"wss://lil-jito.quiknode.pro/{liljito_api_key}/",
                api_key=liljito_api_key,
                subscription_params={
                    "subscription_message": {
                        "jsonrpc": "2.0",
                        "id": 1,
                        "method": "blockSubscribe",
                        "params": {
                            "commitment": "confirmed",
                            "encoding": "json",
                            "transactionDetails": "full",
                            "showRewards": False
                        }
                    },
                    "expect_confirmation": True
                }
            )

            # Register callback for stream data
            async def on_stream_message(message):
                logger.info(f"Received stream message: {message.get('method', 'unknown')}")
                monitoring.track_stream_message('liljito', 'success')

            liljito_stream.on_message(on_stream_message)

            # Start consuming data
            await liljito_stream.start()
            logger.info("Started Lil' Jito stream data ingestion")

        logger.info('Starting live trading mode')

        # Create output directory for logs
        os.makedirs('phase_4_deployment/output/live_trading_logs', exist_ok=True)
        log_file = f'phase_4_deployment/output/live_trading_logs/live_trading_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'

        # Add file handler to logger
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
        logger.addHandler(file_handler)

        logger.info(f'Live trading started at {datetime.now().isoformat()}')
        logger.info(f'Wallet address: {os.environ.get("WALLET_ADDRESS")}')
        logger.info(f'DRY_RUN: {os.environ.get("DRY_RUN")}')
        logger.info(f'PAPER_TRADING: {os.environ.get("PAPER_TRADING")}')
        logger.info(f'TRADING_ENABLED: {os.environ.get("TRADING_ENABLED")}')

        # Run trading loop
        while True:
            try:
                logger.info('Running trading cycle')

                # Scan for tokens
                logger.info('Scanning for tokens')
                api_key = os.environ.get('BIRDEYE_API_KEY', '')
                scanner = BirdeyeScanner(api_key)

                # Track API request in monitoring
                start_time = time.time()
                try:
                    opportunities = await scanner.scan_for_opportunities()
                    monitoring.track_api_request('birdeye', 'scan_for_opportunities', 'success', time.time() - start_time)
                except Exception as e:
                    monitoring.track_api_request('birdeye', 'scan_for_opportunities', 'error', time.time() - start_time)
                    raise e
                finally:
                    await scanner.close()

                # Track whales
                logger.info('Tracking whale activity')
                start_time = time.time()
                try:
                    await track_whales()
                    monitoring.track_api_request('helius', 'track_whales', 'success', time.time() - start_time)
                except Exception as e:
                    monitoring.track_api_request('helius', 'track_whales', 'error', time.time() - start_time)
                    raise e

                # Enrich signals
                logger.info('Enriching signals')
                start_time = time.time()
                try:
                    # Use the new signal enricher if provided, otherwise use the legacy one
                    if signal_enricher:
                        # Load signals from file
                        signals_path = os.path.join(
                            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
                            'phase_4_deployment/output/signals/raw_signals.json'
                        )

                        # Create directory if it doesn't exist
                        os.makedirs(os.path.dirname(signals_path), exist_ok=True)

                        # Check if file exists
                        if os.path.exists(signals_path):
                            # Load signals
                            with open(signals_path, 'r') as f:
                                try:
                                    data = json.load(f)
                                    signals = data.get('signals', [])
                                except json.JSONDecodeError:
                                    logger.error(f"Error decoding JSON from {signals_path}")
                                    signals = []
                        else:
                            # Create empty signals file
                            signals = []
                            with open(signals_path, 'w') as f:
                                json.dump({'signals': signals}, f)

                        # Enrich signals
                        enriched_signals = signal_enricher.enrich_signals(signals)

                        # Save enriched signals
                        enriched_path = os.path.join(
                            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
                            'phase_4_deployment/output/signals/enriched_signals.json'
                        )

                        # Create directory if it doesn't exist
                        os.makedirs(os.path.dirname(enriched_path), exist_ok=True)

                        # Save enriched signals
                        with open(enriched_path, 'w') as f:
                            json.dump({
                                'timestamp': time.time(),
                                'count': len(enriched_signals),
                                'signals': enriched_signals
                            }, f, indent=2)

                        logger.info(f"Enriched {len(signals)} signals with new signal enricher")
                    else:
                        # Use new signal enricher with legacy compatibility
                        signal_enricher = SignalEnricher()
                        signals_path = os.path.join(
                            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
                            'phase_4_deployment/output/signals/raw_signals.json'
                        )

                        # Load raw signals
                        raw_signals = []
                        if os.path.exists(signals_path):
                            try:
                                with open(signals_path, 'r') as f:
                                    data = json.load(f)
                                    raw_signals = data.get('signals', [])
                            except Exception as e:
                                logger.error(f"Error loading raw signals: {str(e)}")

                        # Enrich signals
                        enriched_signals = [signal_enricher.enrich_signal(signal) for signal in raw_signals]

                        # Save enriched signals
                        enriched_path = os.path.join(
                            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
                            'phase_4_deployment/output/signals/enriched_signals.json'
                        )

                        # Create directory if it doesn't exist
                        os.makedirs(os.path.dirname(enriched_path), exist_ok=True)

                        # Save enriched signals
                        with open(enriched_path, 'w') as f:
                            json.dump({
                                'timestamp': time.time(),
                                'count': len(enriched_signals),
                                'signals': enriched_signals
                            }, f, indent=2)

                        logger.info(f"Enriched {len(enriched_signals)} signals with new signal enricher (legacy compatibility mode)")

                    monitoring.track_api_request('signal_enricher', 'enrich_signals', 'success', time.time() - start_time)
                except Exception as e:
                    monitoring.track_api_request('signal_enricher', 'enrich_signals', 'error', time.time() - start_time)
                    logger.error(f"Error enriching signals: {str(e)}")
                    # Don't raise the exception, just log it and continue

                # Check for trading opportunities
                logger.info('Checking for trading opportunities')

                # Load enriched signals if available
                enriched_path = os.path.join(
                    os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
                    'phase_4_deployment/output/signals/enriched_signals.json'
                )

                signals_to_process = []

                if os.path.exists(enriched_path):
                    try:
                        with open(enriched_path, 'r') as f:
                            data = json.load(f)
                            signals_to_process = data.get('signals', [])
                        logger.info(f"Loaded {len(signals_to_process)} enriched signals")
                    except Exception as e:
                        logger.error(f"Error loading enriched signals: {str(e)}")

                # Filter signals if filter chain is available
                filtered_signals = []

                if filter_chain and signals_to_process:
                    logger.info(f"Filtering {len(signals_to_process)} signals")

                    # Filter each signal
                    for signal in signals_to_process:
                        try:
                            # Apply filter chain
                            passed, metadata = await filter_chain.filter_signal(signal)

                            # Add filter results to signal metadata
                            if 'metadata' not in signal:
                                signal['metadata'] = {}

                            signal['metadata']['filter_results'] = metadata
                            signal['metadata']['passed_filters'] = passed

                            # Add to filtered signals if passed
                            if passed:
                                filtered_signals.append(signal)
                                logger.info(f"Signal passed filters: {signal.get('market', 'unknown')}")
                            else:
                                logger.info(f"Signal rejected by filters: {signal.get('market', 'unknown')}")
                        except Exception as e:
                            logger.error(f"Error filtering signal: {str(e)}")

                    logger.info(f"Filtered signals: {len(filtered_signals)}/{len(signals_to_process)} passed")

                    # Save filtered signals
                    filtered_path = os.path.join(
                        os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
                        'phase_4_deployment/output/signals/filtered_signals.json'
                    )

                    # Create directory if it doesn't exist
                    os.makedirs(os.path.dirname(filtered_path), exist_ok=True)

                    # Save filtered signals
                    with open(filtered_path, 'w') as f:
                        json.dump({
                            'timestamp': time.time(),
                            'count': len(filtered_signals),
                            'signals': filtered_signals
                        }, f, indent=2)
                elif signals_to_process:
                    # No filter chain, use all signals
                    filtered_signals = signals_to_process
                    logger.info(f"No filter chain available, using all {len(signals_to_process)} signals")

                # Collect RL data if available
                if rl_data_collector and filtered_signals:
                    logger.info(f"Collecting RL data for {len(filtered_signals)} signals")

                    # Store signals for later pairing with results
                    for signal in filtered_signals:
                        signal_id = rl_data_collector.store_signal(signal)

                        # Add signal ID to metadata
                        if 'metadata' not in signal:
                            signal['metadata'] = {}

                        signal['metadata']['signal_id'] = signal_id

                # Use the best signal or a default example
                if filtered_signals:
                    # Sort by priority score if available
                    filtered_signals.sort(
                        key=lambda s: s.get('metadata', {}).get('priority_score', 0),
                        reverse=True
                    )

                    # Use the best signal
                    best_signal = filtered_signals[0]

                    # Convert to the format expected by tx_builder
                    example_signal = {
                        "action": best_signal.get('action', 'BUY'),
                        "market": best_signal.get('market', 'SOL-USDC'),
                        "price": best_signal.get('price', 25.10),
                        "size": best_signal.get('size', 0.1),  # Small size for testing
                        "confidence": best_signal.get('metadata', {}).get('confidence', 0.0),
                        "timestamp": datetime.now().isoformat(),
                        "metadata": best_signal.get('metadata', {})
                    }

                    # Send trade alert
                    try:
                        from phase_4_deployment.utils.trading_alerts import send_trade_alert

                        # Create trade alert task
                        asyncio.create_task(send_trade_alert(example_signal))
                        logger.info(f"Sent trade alert for {example_signal['market']}")
                    except ImportError:
                        logger.warning("Trading alerts module not available - trade alert not sent")
                    except Exception as e:
                        logger.error(f"Error sending trade alert: {str(e)}")

                    logger.info(f"Using best signal: {example_signal['market']} with priority score {example_signal['metadata'].get('priority_score', 0)}")
                else:
                    # Use default example signal
                    example_signal = {
                        "action": "BUY",
                        "market": "SOL-USDC",
                        "price": 25.10,
                        "size": 0.1,  # Small size for testing
                        "confidence": 0.92,
                        "timestamp": datetime.now().isoformat(),
                        "metadata": {}
                    }

                    logger.info("No signals available, using default example signal")

                    # Send trade alert for default signal
                    try:
                        from phase_4_deployment.utils.trading_alerts import send_trade_alert

                        # Create trade alert task
                        asyncio.create_task(send_trade_alert(example_signal))
                        logger.info(f"Sent trade alert for default signal {example_signal['market']}")
                    except ImportError:
                        logger.warning("Trading alerts module not available - trade alert not sent")
                    except Exception as e:
                        logger.error(f"Error sending trade alert: {str(e)}")

                # Track trading signal
                monitoring.track_trading_signal('example', example_signal['action'], example_signal['market'])

                # In live mode, we build and execute transactions
                if os.environ.get('DRY_RUN') == 'false':
                    logger.info('Building transaction from signal')

                    # Use transaction preparation service if available
                    if USING_TX_PREP and tx_prep_service:
                        try:
                            # Get a recent blockhash
                            blockhash = tx_prep_service.get_recent_blockhash()

                            # Get the active pubkey
                            pubkey = tx_prep_service.get_active_pubkey()

                            # Build a simple transaction (just a memo instruction for testing)
                            # In a real implementation, this would use the signal to build a swap transaction
                            instructions = [
                                {
                                    "programId": "MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr",
                                    "accounts": [
                                        {
                                            "pubkey": pubkey,
                                            "isSigner": True,
                                            "isWritable": True
                                        }
                                    ],
                                    "data": "48656c6c6f2c20776f726c6421"  # "Hello, world!" in hex
                                }
                            ]

                            # Build the transaction
                            tx_bytes = tx_prep_service.build_transaction(
                                instructions=instructions,
                                fee_payer=pubkey,
                                recent_blockhash=blockhash,
                                priority_fee_microlamports=10000,
                                compute_unit_limit=200000,
                                is_versioned=True
                            )

                            # Sign the transaction
                            signed_tx_bytes = tx_prep_service.sign_transaction(tx_bytes, is_versioned=True)

                            # Use the transaction executor to execute the transaction
                            logger.info('Transaction built and signed successfully, executing now')
                            start_time = time.time()
                            result = await executor.execute_transaction(signed_tx_bytes)
                            execution_time = time.time() - start_time
                        except Exception as e:
                            logger.error(f"Error building transaction with preparation service: {str(e)}")
                            # Fall back to legacy transaction building
                            tx_message = tx_builder.build_swap_tx(example_signal)
                            if tx_message:
                                logger.info('Transaction built with legacy builder, executing now')
                                start_time = time.time()
                                result = await executor.execute_transaction(tx_message)
                                execution_time = time.time() - start_time
                            else:
                                logger.error("Failed to build transaction with legacy builder")
                                result = None
                                execution_time = 0
                    else:
                        # Use legacy transaction builder
                        tx_message = tx_builder.build_swap_tx(example_signal)
                        if tx_message:
                            logger.info('Transaction built with legacy builder, executing now')
                            start_time = time.time()
                            result = await executor.execute_transaction(tx_message)
                            execution_time = time.time() - start_time
                        else:
                            logger.error("Failed to build transaction with legacy builder")
                            result = None
                            execution_time = 0

                    # Process the result
                    if result and result.get('success', False) and 'signature' in result:
                        logger.info(f"Transaction executed successfully: {result['signature']}")

                        # Track transaction with execution time
                        is_paper_trading = result.get('provider') == 'paper_trading'
                        monitoring.track_transaction('success', 'swap', execution_time, is_paper_trading)

                        # Track API request
                        monitoring.track_api_request(result.get('provider', 'unknown'), 'execute_transaction', 'success', execution_time)

                        # Log if it was a paper trading transaction
                        if is_paper_trading:
                            logger.info("Transaction was executed in paper trading mode")

                        # Send performance metrics alert
                        try:
                            from phase_4_deployment.utils.trading_alerts import get_trading_alerts

                            # Get trading alerts instance
                            trading_alerts = get_trading_alerts()

                            # Update wallet balance
                            try:
                                from rpc_execution.helius_client import HeliusClient
                                helius_api_key = os.environ.get('HELIUS_API_KEY')
                                client = HeliusClient(api_key=helius_api_key)
                                balance_data = await client.get_balance(wallet_address)
                                if 'balance_sol' in balance_data:
                                    # Update wallet balance in trading alerts
                                    trading_alerts.update_wallet_balance(balance_data['balance_sol'])
                                await client.close()
                            except Exception as e:
                                logger.error(f"Error updating wallet balance for metrics: {str(e)}")

                            # Send performance metrics
                            asyncio.create_task(trading_alerts.send_performance_metrics())
                            logger.info("Sent performance metrics alert")
                        except ImportError:
                            logger.warning("Trading alerts module not available - performance metrics not sent")
                        except Exception as e:
                            logger.error(f"Error sending performance metrics: {str(e)}")

                        # Store RL data if available
                        if rl_data_collector and 'metadata' in example_signal and 'signal_id' in example_signal['metadata']:
                            signal_id = example_signal['metadata']['signal_id']

                            # Create result data
                            result_data = {
                                'success': True,
                                'signature': result['signature'],
                                'provider': result.get('provider', 'unknown'),
                                'execution_time': execution_time,
                                'profit_loss': 0.0,  # This would be calculated based on actual trade result
                                'execution_quality': 1.0,  # This would be calculated based on slippage, etc.
                                'timestamp': time.time()
                            }

                            # Store result
                            rl_data_collector.store_result(signal_id, result_data)
                            logger.info(f"Stored RL data for signal {signal_id}")
                    elif result:
                        logger.warning(f"Transaction execution failed: {result.get('error', 'Unknown error')}")

                        # Track transaction with execution time
                        monitoring.track_transaction('error', 'swap', execution_time)

                        # Track API request
                        monitoring.track_api_request(result.get('provider', 'unknown'), 'execute_transaction', 'error', execution_time)

                        # Trigger alert for transaction error
                        monitoring._trigger_alert('transaction_error', {
                            'type': 'swap',
                            'error': str(result.get('error', 'Unknown error')),
                            'timestamp': datetime.now().isoformat()
                        })

                        # Store RL data if available
                        if rl_data_collector and 'metadata' in example_signal and 'signal_id' in example_signal['metadata']:
                            signal_id = example_signal['metadata']['signal_id']

                            # Create result data
                            result_data = {
                                'success': False,
                                'error': str(result.get('error', 'Unknown error')),
                                'provider': result.get('provider', 'unknown'),
                                'execution_time': execution_time,
                                'profit_loss': -0.01,  # Small negative value for failed transactions
                                'execution_quality': 0.0,  # Failed execution
                                'timestamp': time.time()
                            }

                            # Store result
                            rl_data_collector.store_result(signal_id, result_data)
                            logger.info(f"Stored RL data for failed signal {signal_id}")
                    else:
                        logger.error("No result returned from transaction execution")

                        # Track transaction with execution time
                        monitoring.track_transaction('error', 'swap', execution_time)

                        # Track API request
                        monitoring.track_api_request('unknown', 'execute_transaction', 'error', execution_time)

                        # Trigger alert for transaction error
                        monitoring._trigger_alert('transaction_error', {
                            'type': 'swap',
                            'error': 'No result returned from transaction execution',
                            'timestamp': datetime.now().isoformat()
                        })

                        # Store RL data if available
                        if rl_data_collector and 'metadata' in example_signal and 'signal_id' in example_signal['metadata']:
                            signal_id = example_signal['metadata']['signal_id']

                            # Create result data
                            result_data = {
                                'success': False,
                                'error': 'No result returned from transaction execution',
                                'provider': 'unknown',
                                'execution_time': execution_time,
                                'profit_loss': -0.01,  # Small negative value for failed transactions
                                'execution_quality': 0.0,  # Failed execution
                                'timestamp': time.time()
                            }

                            # Store result
                            rl_data_collector.store_result(signal_id, result_data)
                            logger.info(f"Stored RL data for failed signal {signal_id}")
                else:
                    logger.info('Dry run mode - not building or executing transactions')

                # Update circuit breaker status
                if hasattr(scanner, 'circuit_breaker'):
                    circuit_state = scanner.circuit_breaker.state
                    monitoring.update_circuit_breaker_status('birdeye', circuit_state)

                    # Trigger alert if circuit breaker is open
                    if circuit_state == "OPEN":
                        monitoring._trigger_alert('circuit_breaker_open', {
                            'api': 'birdeye',
                            'timestamp': datetime.now().isoformat()
                        })

                # Update wallet balance periodically
                if time.time() % 300 < 60:  # Update every 5 minutes
                    try:
                        from rpc_execution.helius_client import HeliusClient
                        helius_api_key = os.environ.get('HELIUS_API_KEY')
                        client = HeliusClient(api_key=helius_api_key)
                        balance_data = await client.get_balance(wallet_address)
                        if 'balance_sol' in balance_data:
                            monitoring.update_wallet_balance(wallet_address, balance_data['balance_sol'])
                        await client.close()
                    except Exception as e:
                        logger.error(f"Error updating wallet balance: {str(e)}")

                # Update Carbon Core metrics
                if 'carbon_core_manager' in locals() and carbon_core_started:
                    try:
                        # Get Carbon Core metrics
                        carbon_core_metrics = await carbon_core_manager.get_metrics()

                        # Update monitoring with Carbon Core metrics
                        if carbon_core_metrics:
                            # Track Carbon Core status
                            monitoring.update_component_status('carbon_core',
                                                             carbon_core_metrics.get('status', 'unknown'),
                                                             carbon_core_metrics.get('using_fallback', False))

                            # Track Carbon Core metrics
                            if 'market_microstructure' in carbon_core_metrics:
                                for market, data in carbon_core_metrics['market_microstructure'].get('markets', {}).items():
                                    if data:
                                        monitoring.track_market_microstructure(market, data)

                            # Track statistical signals
                            if 'statistical_signals' in carbon_core_metrics:
                                for signal_type, data in carbon_core_metrics['statistical_signals'].get('signals', {}).items():
                                    if data:
                                        monitoring.track_statistical_signal(signal_type, data)

                            # Log Carbon Core status
                            logger.info(f"Carbon Core status: {carbon_core_metrics.get('status', 'unknown')} " +
                                       f"(using fallback: {carbon_core_metrics.get('using_fallback', False)})")
                    except Exception as e:
                        logger.error(f"Error updating Carbon Core metrics: {str(e)}")

                # Wait for next cycle
                logger.info('Waiting for next cycle')
                await asyncio.sleep(60)  # Run every minute

            except Exception as e:
                logger.error(f'Error in trading cycle: {str(e)}')
                await asyncio.sleep(10)  # Wait before retrying

    except KeyboardInterrupt:
        logger.info('Live trading stopped by user')
    except Exception as e:
        logger.error(f'Error in live trading: {str(e)}')
    finally:
        # Close the executors
        if 'executor' in locals():
            await executor.close()

        if 'liljito_executor' in locals():
            await liljito_executor.close()

        # Close the transaction preparation service
        if 'tx_prep_service' in locals() and tx_prep_service:
            logger.info('Closing transaction preparation service')
            # No need to close the tx_prep_service as it doesn't have any resources to release

        # Close the stream data ingestor
        if 'liljito_stream' in locals():
            await liljito_stream.stop()
            logger.info('Stopped Lil\' Jito stream data ingestion')

        # Stop Carbon Core
        if 'carbon_core_manager' in locals() and carbon_core_started:
            await carbon_core_manager.stop()
            logger.info('Stopped Carbon Core')

        # Close filter chain
        if filter_chain:
            try:
                from phase_4_deployment.filters.filter_factory import FilterFactory
                await FilterFactory.close_filters(filter_chain)
                logger.info("Filter chain closed")
            except Exception as e:
                logger.error(f"Error closing filter chain: {str(e)}")

        # Clean up RL data collector
        if rl_data_collector:
            try:
                rl_data_collector.clear_memory()
                logger.info("RL data collector memory cleared")
            except Exception as e:
                logger.error(f"Error clearing RL data collector memory: {str(e)}")

        logger.info('Live trading session ended')

async def main():
    """Main function to start live trading."""
    # Parse command line arguments
    import argparse
    parser = argparse.ArgumentParser(description='Start Synergy7 System in live trading mode')
    parser.add_argument('--use-filters', action='store_true',
                        help='Use filter chain for signal filtering')
    parser.add_argument('--use-signal-enricher', action='store_true',
                        help='Use signal enricher for signal enrichment')
    parser.add_argument('--use-rl-data-collector', action='store_true',
                        help='Use RL data collector for RL data collection')
    args = parser.parse_args()
    print("\n======================================")
    print("STARTING SYNERGY7 SYSTEM IN LIVE TRADING MODE")
    print("======================================\n")

    print("WARNING: This will execute REAL transactions with REAL funds.")
    print("Make sure you have thoroughly tested the system in dry run mode first.\n")

    # Set environment variables for live trading based on config
    dry_run = not config.get('mode', {}).get('live_trading', False)
    paper_trading = config.get('mode', {}).get('paper_trading', False)

    os.environ['DRY_RUN'] = str(dry_run).lower()
    os.environ['PAPER_TRADING'] = str(paper_trading).lower()
    os.environ['TRADING_ENABLED'] = str(not dry_run).lower()

    # Print configuration summary
    print(f"Configuration loaded from environment: {config.get('environment', 'production')}")
    print(f"Live trading: {config.get('mode', {}).get('live_trading', False)}")
    print(f"Paper trading: {config.get('mode', {}).get('paper_trading', False)}")
    print(f"Dry run: {dry_run}")
    print(f"Trading enabled: {not dry_run}")
    print(f"Monitoring enabled: {config.get('monitoring', {}).get('enabled', True)}")
    print(f"Log level: {config.get('monitoring', {}).get('log_level', 'INFO')}")

    print("Environment variables set for live trading:")
    print(f"DRY_RUN: {os.environ.get('DRY_RUN')}")
    print(f"PAPER_TRADING: {os.environ.get('PAPER_TRADING')}")
    print(f"TRADING_ENABLED: {os.environ.get('TRADING_ENABLED')}")
    print(f"WALLET_ADDRESS: {os.environ.get('WALLET_ADDRESS')}\n")

    # Initialize monitoring
    monitoring = get_monitoring_service()

    # Register components for health checks with more detailed checks
    monitoring.register_component('api_client', lambda: True)  # This should be replaced with a real check

    # Wallet health check function
    def check_wallet_health():
        wallet_address = os.environ.get('WALLET_ADDRESS')
        if not wallet_address:
            return False
        # In a real implementation, this would check if the wallet has sufficient balance
        # For now, we'll just return True
        return True

    # Trading engine health check function
    def check_trading_engine_health():
        # In a real implementation, this would check if the trading engine is running properly
        # For now, we'll just return True
        return True

    # Register the health check functions
    monitoring.register_component('wallet', check_wallet_health)
    monitoring.register_component('trading_engine', check_trading_engine_health)

    # Start health check server with configuration
    health_server_config = config.get('deployment', {}).get('health_server', {})
    health_server_host = health_server_config.get('host', '0.0.0.0')
    health_server_port = health_server_config.get('port', 8080)

    logger.info(f"Starting health server on {health_server_host}:{health_server_port}")
    health_server_thread = start_health_server(host=health_server_host, port=health_server_port)

    # Set up Telegram alerts if credentials are available
    telegram_bot_token = config.get('monitoring', {}).get('telegram_bot_token')
    if not telegram_bot_token:
        telegram_bot_token = os.environ.get('TELEGRAM_BOT_TOKEN')

    telegram_chat_id = config.get('monitoring', {}).get('telegram_chat_id')
    if not telegram_chat_id:
        telegram_chat_id = os.environ.get('TELEGRAM_CHAT_ID')
    if telegram_bot_token and telegram_chat_id:
        # Set up alert handler with rate limiting (5 minutes between alerts of the same type)
        alert_handler = setup_telegram_alerts(telegram_bot_token, telegram_chat_id, rate_limit_seconds=300)

        # Register alert handlers for different alert types
        monitoring.register_alert_handler('component_unhealthy', alert_handler)
        monitoring.register_alert_handler('low_balance', alert_handler)
        monitoring.register_alert_handler('transaction_error', alert_handler)
        monitoring.register_alert_handler('circuit_breaker_open', alert_handler)
        monitoring.register_alert_handler('system_resources', alert_handler)

        logger.info("Telegram alerts configured with rate limiting")
    else:
        logger.warning("Telegram alerts not configured - missing bot token or chat ID")

    # Start health checks
    monitoring.start_health_checks()
    logger.info("Monitoring and health checks started")

    # Start system resource monitoring if psutil is available
    try:
        import psutil

        # Function to update system resources periodically
        def update_system_resources():
            while True:
                try:
                    monitoring.update_system_resources()

                    # Check for critical resource usage
                    memory = psutil.virtual_memory()
                    if memory.percent > 90:  # More than 90% memory usage
                        monitoring._trigger_alert('system_resources', {
                            'resource': 'memory',
                            'usage': f"{memory.percent}%",
                            'threshold': "90%",
                            'timestamp': datetime.now().isoformat()
                        })

                    cpu_percent = psutil.cpu_percent(interval=0.1)
                    if cpu_percent > 80:  # More than 80% CPU usage
                        monitoring._trigger_alert('system_resources', {
                            'resource': 'cpu',
                            'usage': f"{cpu_percent}%",
                            'threshold': "80%",
                            'timestamp': datetime.now().isoformat()
                        })

                    # Send system metrics alert every 5 minutes
                    if time.time() % 300 < 1:  # Once every 5 minutes
                        try:
                            from phase_4_deployment.utils.trading_alerts import send_system_alert

                            # Get filter chain and signal enricher status
                            components_status = {
                                'Memory Usage': memory.percent < 90,
                                'CPU Usage': cpu_percent < 80
                            }

                            # Create system data
                            system_data = {
                                'signals_generated': 0,
                                'signals_filtered': 0,
                                'components': components_status,
                                'memory_usage': memory.percent,
                                'cpu_usage': cpu_percent,
                                'timestamp': datetime.now().isoformat()
                            }

                            # Send system metrics alert
                            asyncio.run(send_system_alert(system_data))
                            logger.info("Sent system metrics alert")
                        except ImportError:
                            logger.warning("Trading alerts module not available - system metrics not sent")
                        except Exception as e:
                            logger.error(f"Error sending system metrics: {str(e)}")

                    # Check every 60 seconds
                    time.sleep(60)
                except Exception as e:
                    logger.error(f"Error in system resource monitoring: {str(e)}")
                    time.sleep(60)  # Wait before retrying

        # Start system resource monitoring in a separate thread
        import threading
        resource_thread = threading.Thread(target=update_system_resources, daemon=True)
        resource_thread.start()
        logger.info("System resource monitoring started")
    except ImportError:
        logger.warning("psutil not installed, system resource monitoring not available")
        logger.info("To enable system resource monitoring, install psutil: pip install psutil")

    # Check wallet balance
    print("Checking wallet balance...")
    balance_ok = await check_wallet_balance()

    # Update wallet balance in monitoring
    if balance_ok:
        try:
            from rpc_execution.helius_client import HeliusClient

            # Get wallet address from config
            wallet_address = config.get('wallet', {}).get('address')
            if not wallet_address:
                wallet_address = os.environ.get('WALLET_ADDRESS')

            # Get Helius API key from config
            helius_api_key = config.get('apis', {}).get('helius', {}).get('api_key')
            if not helius_api_key:
                helius_api_key = os.environ.get('HELIUS_API_KEY')

            # Get RPC URL from config
            rpc_url = config.get('apis', {}).get('helius', {}).get('rpc_endpoint')

            client = HeliusClient(api_key=helius_api_key, rpc_url=rpc_url)
            balance_data = await client.get_balance(wallet_address)

            if 'balance_sol' in balance_data:
                monitoring.update_wallet_balance(wallet_address, balance_data['balance_sol'])

                # Get minimum balance thresholds from config
                min_balance_warning = config.get('wallet', {}).get('min_balance_warning', 1.0)

                # Trigger alert if balance is low
                if balance_data['balance_sol'] < min_balance_warning:
                    monitoring.update_wallet_balance(wallet_address, balance_data['balance_sol'])
                    monitoring._trigger_alert('low_balance', {
                        'wallet': wallet_address,
                        'balance': balance_data['balance_sol'],
                        'threshold': min_balance_warning,
                        'timestamp': datetime.now().isoformat()
                    })
            await client.close()
        except Exception as e:
            logger.error(f"Error updating wallet balance in monitoring: {str(e)}")

    if not balance_ok:
        print("\nWARNING: Wallet balance check failed. Proceeding anyway.\n")

    print("\nStarting live trading in 5 seconds...")
    print("Press Ctrl+C to stop at any time.\n")

    # Wait 5 seconds before starting
    for i in range(5, 0, -1):
        print(f"Starting in {i} seconds...", end="\r")
        await asyncio.sleep(1)

    print("\nLive trading started!\n")

    # Initialize components if requested
    filter_chain = None
    signal_enricher = None
    rl_data_collector = None

    if args.use_filters:
        try:
            from phase_4_deployment.filters.filter_factory import FilterFactory
            # Load config
            import yaml
            with open(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'config.yaml'), 'r') as f:
                filter_config = yaml.safe_load(f)
            filter_chain = FilterFactory.create_filter_chain(filter_config.get('filters', {}))
            logger.info(f"Initialized filter chain with {len(filter_chain.filters)} filters")
        except Exception as e:
            logger.error(f"Error initializing filter chain: {str(e)}")

    if args.use_signal_enricher:
        try:
            from phase_4_deployment.signal_generator.signal_enricher import SignalEnricher
            # Load config
            import yaml
            with open(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'config.yaml'), 'r') as f:
                enricher_config = yaml.safe_load(f)
            signal_enricher = SignalEnricher(enricher_config.get('signal_enrichment', {}))
            logger.info(f"Initialized signal enricher with algorithm: {signal_enricher.ranking_algorithm}")
        except Exception as e:
            logger.error(f"Error initializing signal enricher: {str(e)}")

    if args.use_rl_data_collector:
        try:
            from phase_4_deployment.rl_agent.data_collector import RLDataCollector
            # Load config
            import yaml
            with open(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'config.yaml'), 'r') as f:
                rl_config = yaml.safe_load(f)
            rl_data_collector = RLDataCollector(rl_config.get('rl_agent', {}))
            logger.info(f"Initialized RL data collector with data collection: {rl_data_collector.data_collection}")
        except Exception as e:
            logger.error(f"Error initializing RL data collector: {str(e)}")

    # Run live trading with components
    await run_live_trading(
        filter_chain=filter_chain,
        signal_enricher=signal_enricher,
        rl_data_collector=rl_data_collector
    )

if __name__ == "__main__":
    asyncio.run(main())
