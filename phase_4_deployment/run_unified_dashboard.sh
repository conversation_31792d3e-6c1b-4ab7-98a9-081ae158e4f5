#!/bin/bash
# Run Unified Dashboard for Synergy7 Trading System
# This script runs the unified dashboard for the Synergy7 Trading System.

# Set colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Print header
function print_header() {
    echo -e "\n${YELLOW}=== $1 ===${NC}\n"
}

# Print success message
function print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

# Print error message
function print_error() {
    echo -e "${RED}✗ $1${NC}"
}

# Set the directory to the script's directory
cd "$(dirname "$0")"

# Load environment variables
if [ -f "../.env" ]; then
    echo "Loading environment variables from ../.env"
    export $(grep -v '^#' ../.env | xargs)
fi

# Parse command line arguments
PORT=8501
HOST="0.0.0.0"
BROWSER=false
HEADLESS=false

while [[ $# -gt 0 ]]; do
    key="$1"
    case $key in
        --port)
            PORT="$2"
            shift
            shift
            ;;
        --host)
            HOST="$2"
            shift
            shift
            ;;
        --browser)
            BROWSER=true
            shift
            ;;
        --headless)
            HEADLESS=true
            shift
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

print_header "Starting Synergy7 Unified Dashboard"

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    print_error "Python 3 is not installed"
    exit 1
fi

# Check if the dashboard script exists
DASHBOARD_SCRIPT="unified_dashboard/run_dashboard.py"
if [ ! -f "$DASHBOARD_SCRIPT" ]; then
    print_error "Dashboard script not found: $DASHBOARD_SCRIPT"
    exit 1
fi

# Make the script executable
chmod +x "$DASHBOARD_SCRIPT"

# Build command
CMD="python3 $DASHBOARD_SCRIPT --port $PORT --host $HOST"

if [ "$BROWSER" = true ]; then
    CMD="$CMD --browser"
fi

if [ "$HEADLESS" = true ]; then
    CMD="$CMD --headless"
fi

# Run the dashboard
print_success "Running Synergy7 Unified Dashboard on $HOST:$PORT"
echo -e "${YELLOW}Command: $CMD${NC}"

# Execute the command
$CMD

# Exit with the command's exit code
exit $?
