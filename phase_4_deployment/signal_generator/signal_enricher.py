#!/usr/bin/env python3
"""
Signal Enricher Module

This module enriches trading signals with additional metadata and ranks them
based on various criteria.
"""

import os
import sys
import json
import time
import logging
import asyncio
from typing import Dict, List, Any, Optional, Tuple, Callable
from datetime import datetime, timedelta

# Add parent directory to path for imports
parent_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('signal_enricher')

class SignalEnricher:
    """
    Enriches trading signals with additional metadata and ranks them.
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize the signal enricher.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config or {}
        self.enabled = self.config.get('enabled', True)
        self.ranking_algorithm = self.config.get('ranking_algorithm', 'composite_score')
        self.metadata_fields = self.config.get('metadata_fields', [
            'momentum_score',
            'liquidity_score',
            'volatility_score',
            'alpha_wallet_score'
        ])
        
        logger.info(f"Initialized SignalEnricher with ranking_algorithm={self.ranking_algorithm}")
    
    def _calculate_composite_score(self, signal: Dict[str, Any]) -> float:
        """
        Calculate a composite score for a signal based on metadata.
        
        Args:
            signal: Signal to score
            
        Returns:
            Composite score (0-1)
        """
        if 'metadata' not in signal:
            return 0.0
        
        metadata = signal['metadata']
        
        # Extract scores from metadata
        scores = {}
        for field in self.metadata_fields:
            # Try to find the score in metadata
            if field in metadata:
                scores[field] = metadata[field]
            else:
                # Look for the score in filter results
                if 'filter_results' in metadata:
                    for result in metadata['filter_results']:
                        if field in result:
                            scores[field] = result[field]
                            break
        
        # If no scores found, return 0
        if not scores:
            return 0.0
        
        # Calculate weighted average of scores
        # Default weights:
        # - momentum_score: 0.4
        # - liquidity_score: 0.3
        # - volatility_score: 0.2
        # - alpha_wallet_score: 0.1
        weights = {
            'momentum_score': 0.4,
            'liquidity_score': 0.3,
            'volatility_score': 0.2,
            'alpha_wallet_score': 0.1
        }
        
        # Override with config weights if provided
        if 'score_weights' in self.config:
            weights.update(self.config['score_weights'])
        
        # Calculate weighted sum
        weighted_sum = 0.0
        total_weight = 0.0
        
        for field, score in scores.items():
            if field in weights:
                weight = weights[field]
                weighted_sum += score * weight
                total_weight += weight
        
        # Return normalized score
        if total_weight > 0:
            return weighted_sum / total_weight
        else:
            return 0.0
    
    def _calculate_momentum_priority(self, signal: Dict[str, Any]) -> float:
        """
        Calculate a priority score based primarily on momentum.
        
        Args:
            signal: Signal to score
            
        Returns:
            Priority score (0-1)
        """
        if 'metadata' not in signal:
            return 0.0
        
        metadata = signal['metadata']
        
        # Extract momentum score
        momentum_score = 0.0
        if 'momentum_score' in metadata:
            momentum_score = metadata['momentum_score']
        elif 'filter_results' in metadata:
            for result in metadata['filter_results']:
                if 'momentum_score' in result:
                    momentum_score = result['momentum_score']
                    break
        
        # Extract confidence
        confidence = signal.get('confidence', 0.5)
        
        # Calculate priority score
        # 80% momentum, 20% confidence
        return 0.8 * momentum_score + 0.2 * confidence
    
    def _calculate_liquidity_priority(self, signal: Dict[str, Any]) -> float:
        """
        Calculate a priority score based primarily on liquidity.
        
        Args:
            signal: Signal to score
            
        Returns:
            Priority score (0-1)
        """
        if 'metadata' not in signal:
            return 0.0
        
        metadata = signal['metadata']
        
        # Extract liquidity score
        liquidity_score = 0.0
        if 'liquidity_score' in metadata:
            liquidity_score = metadata['liquidity_score']
        elif 'filter_results' in metadata:
            for result in metadata['filter_results']:
                if 'liquidity_score' in result:
                    liquidity_score = result['liquidity_score']
                    break
        
        # Extract momentum score
        momentum_score = 0.0
        if 'momentum_score' in metadata:
            momentum_score = metadata['momentum_score']
        elif 'filter_results' in metadata:
            for result in metadata['filter_results']:
                if 'momentum_score' in result:
                    momentum_score = result['momentum_score']
                    break
        
        # Calculate priority score
        # 60% liquidity, 40% momentum
        return 0.6 * liquidity_score + 0.4 * momentum_score
    
    def _calculate_alpha_priority(self, signal: Dict[str, Any]) -> float:
        """
        Calculate a priority score based primarily on alpha wallet activity.
        
        Args:
            signal: Signal to score
            
        Returns:
            Priority score (0-1)
        """
        if 'metadata' not in signal:
            return 0.0
        
        metadata = signal['metadata']
        
        # Extract alpha wallet score
        alpha_score = 0.0
        if 'alpha_wallet_score' in metadata:
            alpha_score = metadata['alpha_wallet_score']
        elif 'filter_results' in metadata:
            for result in metadata['filter_results']:
                if 'alpha_wallet_score' in result:
                    alpha_score = result['alpha_wallet_score']
                    break
        
        # Extract momentum score
        momentum_score = 0.0
        if 'momentum_score' in metadata:
            momentum_score = metadata['momentum_score']
        elif 'filter_results' in metadata:
            for result in metadata['filter_results']:
                if 'momentum_score' in result:
                    momentum_score = result['momentum_score']
                    break
        
        # Calculate priority score
        # 70% alpha, 30% momentum
        return 0.7 * alpha_score + 0.3 * momentum_score
    
    def calculate_priority_score(self, signal: Dict[str, Any]) -> float:
        """
        Calculate a priority score for a signal based on the selected algorithm.
        
        Args:
            signal: Signal to score
            
        Returns:
            Priority score (0-1)
        """
        if self.ranking_algorithm == 'composite_score':
            return self._calculate_composite_score(signal)
        elif self.ranking_algorithm == 'momentum_priority':
            return self._calculate_momentum_priority(signal)
        elif self.ranking_algorithm == 'liquidity_priority':
            return self._calculate_liquidity_priority(signal)
        elif self.ranking_algorithm == 'alpha_priority':
            return self._calculate_alpha_priority(signal)
        else:
            logger.warning(f"Unknown ranking algorithm: {self.ranking_algorithm}, using composite_score")
            return self._calculate_composite_score(signal)
    
    def enrich_signal(self, signal: Dict[str, Any]) -> Dict[str, Any]:
        """
        Enrich a signal with additional metadata.
        
        Args:
            signal: Signal to enrich
            
        Returns:
            Enriched signal
        """
        if not self.enabled:
            return signal
        
        # Create a copy of the signal
        enriched_signal = signal.copy()
        
        # Ensure metadata exists
        if 'metadata' not in enriched_signal:
            enriched_signal['metadata'] = {}
        
        # Calculate priority score
        priority_score = self.calculate_priority_score(enriched_signal)
        
        # Add priority score to metadata
        enriched_signal['metadata']['priority_score'] = priority_score
        
        # Add timestamp if not present
        if 'timestamp' not in enriched_signal:
            enriched_signal['timestamp'] = time.time()
        
        # Add enrichment timestamp
        enriched_signal['metadata']['enriched_at'] = time.time()
        
        return enriched_signal
    
    def enrich_signals(self, signals: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Enrich a list of signals with additional metadata.
        
        Args:
            signals: List of signals to enrich
            
        Returns:
            List of enriched signals
        """
        if not self.enabled:
            return signals
        
        enriched_signals = [self.enrich_signal(signal) for signal in signals]
        
        # Sort by priority score (descending)
        enriched_signals.sort(
            key=lambda s: s.get('metadata', {}).get('priority_score', 0),
            reverse=True
        )
        
        logger.info(f"Enriched {len(signals)} signals")
        return enriched_signals
    
    def save_signals(self, signals: List[Dict[str, Any]], file_path: str) -> None:
        """
        Save signals to a file.
        
        Args:
            signals: List of signals to save
            file_path: Path to save the signals
        """
        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            # Save signals to file
            with open(file_path, 'w') as f:
                json.dump({
                    'timestamp': time.time(),
                    'count': len(signals),
                    'signals': signals
                }, f, indent=2)
            
            logger.info(f"Saved {len(signals)} signals to {file_path}")
        except Exception as e:
            logger.error(f"Error saving signals to {file_path}: {str(e)}")
    
    def load_signals(self, file_path: str) -> List[Dict[str, Any]]:
        """
        Load signals from a file.
        
        Args:
            file_path: Path to load the signals from
            
        Returns:
            List of signals
        """
        try:
            # Check if file exists
            if not os.path.exists(file_path):
                logger.warning(f"Signal file {file_path} not found")
                return []
            
            # Load signals from file
            with open(file_path, 'r') as f:
                data = json.load(f)
            
            signals = data.get('signals', [])
            
            logger.info(f"Loaded {len(signals)} signals from {file_path}")
            return signals
        except Exception as e:
            logger.error(f"Error loading signals from {file_path}: {str(e)}")
            return []
