# Synergy7 Trading System Configuration

# API Configuration
apis:
  helius:
    base_url: "https://api.helius.xyz/v0"
  birdeye:
    base_url: "https://api.birdeye.so/v1"

# RPC Configuration
rpc:
  primary: "https://api.mainnet-beta.solana.com"
  fallback: "https://solana-mainnet.g.alchemy.com/v2/demo"
  jito_url: "https://mainnet.block-engine.jito.wtf/api/v1/transactions"
  jito_enabled: true

# Wallet Configuration
wallet:
  address: "your_wallet_address"
  keypair_path: "keys/wallet_keypair.json"

# Monitoring Configuration
monitoring:
  health_check_interval_ms: 10000
  metrics_interval_ms: 5000
  log_level: "info"
  telegram_alerts: false

# Transaction Execution Configuration
transaction_execution:
  max_retries: 3
  retry_delay_ms: 1000
  timeout_ms: 30000
  skip_preflight: false
  commitment: "confirmed"

# Carbon Core Configuration
carbon_core_config_path: "carbon_core_config.yaml"
carbon_core_binary_path: "bin/carbon_core"
carbon_core:
  enabled: true
  fallback_enabled: true
  metrics_interval_ms: 5000
  market_microstructure:
    enabled: true
    markets:
      - "SOL-USDC"
      - "BTC-USDC"
      - "ETH-USDC"
  statistical_signals:
    enabled: true
    signal_types:
      - "price_momentum"
      - "volume_profile"
  strategy_accuracy_metrics:
    enabled: true
    metrics:
      - "signal_generation_rate"
      - "directional_accuracy"
      - "hit_rate"
      - "win_loss_ratio"
      - "holding_time"
  profit_metrics:
    enabled: true
    metrics:
      - "net_profit"
      - "profit_factor"
      - "return_on_capital"
      - "drawdown"
      - "sharpe_ratio"
  execution_quality_metrics:
    enabled: true
    metrics:
      - "slippage"
      - "fill_rate"
      - "transaction_costs"

# Mode Configuration
mode: "simulation"  # simulation, paper, live

# Filter Configuration
filters:
  enabled: true
  cache_ttl: 60  # seconds
  parallel_execution: true
  alpha_wallet:
    enabled: true
    min_wallet_count: 5
    lookback_period: 24  # hours
  liquidity_guard:
    enabled: true
    min_liquidity_usd: 50000
    order_book_depth: 10
  volatility_screener:
    enabled: true
    max_volatility: 0.05
    wick_threshold: 0.02

# Signal Enrichment
signal_enrichment:
  enabled: true
  ranking_algorithm: "composite_score"
  metadata_fields:
    - "momentum_score"
    - "liquidity_score"
    - "volatility_score"
    - "alpha_wallet_score"

# RL Agent Configuration
rl_agent:
  enabled: false  # Initially disabled, enable after testing
  data_collection: true
  collection_path: "output/rl_data"
  model_path: "models/rl_model"
  training_interval: 86400  # daily in seconds
