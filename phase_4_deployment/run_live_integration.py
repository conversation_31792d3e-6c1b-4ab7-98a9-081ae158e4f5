#!/usr/bin/env python3
"""
Run Live Trading Integration

This script runs the Synergy7 Live Trading Integration.
"""

import os
import sys
import time
import signal
import logging
import argparse

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def signal_handler(sig, frame):
    """
    Signal handler for graceful shutdown.
    """
    logger.info("Received signal to shutdown")
    global running
    running = False

def main():
    """Main function."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Run Synergy7 Live Trading Integration")
    parser.add_argument("--output-dir", help="Directory to write output files")
    
    args = parser.parse_args()
    
    # Get the path to the project root
    project_root = os.path.abspath(os.path.dirname(os.path.abspath(__file__)))
    
    # Add the project root to the Python path
    sys.path.insert(0, project_root)
    
    # Set the PYTHONPATH environment variable
    os.environ["PYTHONPATH"] = project_root + os.pathsep + os.environ.get("PYTHONPATH", "")
    
    # Import the live trading integration
    try:
        from unified_dashboard.live_trading_integration import live_trading_integration
    except ImportError:
        logger.error("Could not import live_trading_integration. Make sure the module is installed.")
        return 1
    
    # Set output directory if specified
    if args.output_dir:
        live_trading_integration.output_dir = os.path.abspath(args.output_dir)
        os.makedirs(live_trading_integration.output_dir, exist_ok=True)
    
    # Register signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Start the live trading integration
    logger.info("Starting Synergy7 Live Trading Integration")
    live_trading_integration.start()
    
    # Run until interrupted
    global running
    running = True
    
    try:
        while running:
            time.sleep(1.0)
    except KeyboardInterrupt:
        logger.info("Interrupted by user")
        running = False
    
    # Stop the live trading integration
    logger.info("Stopping Synergy7 Live Trading Integration")
    live_trading_integration.stop()
    
    logger.info("Synergy7 Live Trading Integration stopped")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
