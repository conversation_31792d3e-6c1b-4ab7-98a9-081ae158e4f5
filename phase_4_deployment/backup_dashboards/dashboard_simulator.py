#!/usr/bin/env python3
"""
Dashboard Simulator for Synergy7 Trading System

This module provides a simulator for the dashboard of the Synergy7 Trading System.
It generates realistic trading data for testing the dashboard.
"""

import os
import json
import time
import random
import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("dashboard_simulator")

class DashboardSimulator:
    """
    Simulator for the Synergy7 Trading System dashboard.
    """

    def __init__(self, config: Dict[str, Any] = None, metrics_source = None):
        """
        Initialize the dashboard simulator.

        Args:
            config: Configuration dictionary
            metrics_source: Source of metrics data (e.g., MonitoringService)
        """
        self.config = config or {}
        self.metrics_source = metrics_source
        self.running = False
        self.update_interval = self.config.get("dashboard", {}).get("update_interval", 1.0)
        self.port = self.config.get("dashboard", {}).get("port", 8050)

        # Initialize simulation parameters
        self.markets = self.config.get("simulation", {}).get("markets", [
            "SOL-USDC", "BTC-USDC", "ETH-USDC", "BONK-USDC", "JUP-USDC"
        ])
        self.strategies = self.config.get("strategies", [
            {"name": "momentum", "enabled": True},
            {"name": "mean_reversion", "enabled": True},
            {"name": "volatility", "enabled": True}
        ])

        # Initialize simulation data
        self.simulation_data = {
            "start_time": None,
            "current_time": None,
            "trades": [],
            "market_data": {},
            "portfolio": {
                "balance": 10000.0,
                "positions": {},
                "pnl": 0.0,
                "equity": 10000.0
            },
            "metrics": {
                "trades": {
                    "total": 0,
                    "successful": 0,
                    "failed": 0
                },
                "performance": {
                    "win_rate": 0.0,
                    "profit_factor": 0.0,
                    "sharpe_ratio": 0.0,
                    "max_drawdown": 0.0
                },
                "system": {
                    "cpu_usage": 0.0,
                    "memory_usage": 0.0,
                    "api_calls": 0,
                    "errors": 0
                }
            }
        }

        # Initialize market data
        for market in self.markets:
            base, quote = market.split("-")
            self.simulation_data["market_data"][market] = {
                "price": self._get_initial_price(base),
                "volume_24h": random.uniform(1000000, 10000000),
                "change_24h": random.uniform(-5.0, 5.0),
                "bid": 0.0,
                "ask": 0.0,
                "spread": 0.0,
                "history": []
            }

        logger.info("Initialized dashboard simulator")

    def _get_initial_price(self, symbol: str) -> float:
        """
        Get initial price for a symbol.

        Args:
            symbol: Symbol name

        Returns:
            Initial price
        """
        if symbol == "SOL":
            return random.uniform(20.0, 30.0)
        elif symbol == "BTC":
            return random.uniform(25000.0, 35000.0)
        elif symbol == "ETH":
            return random.uniform(1500.0, 2500.0)
        elif symbol == "BONK":
            return random.uniform(0.00001, 0.0001)
        elif symbol == "JUP":
            return random.uniform(0.5, 1.5)
        else:
            return random.uniform(1.0, 100.0)

    async def start(self):
        """Start the dashboard simulator."""
        if self.running:
            logger.warning("Dashboard simulator is already running")
            return

        logger.info(f"Starting dashboard simulator on port {self.port}")
        self.running = True

        # Initialize simulation time
        self.simulation_data["start_time"] = datetime.now()
        self.simulation_data["current_time"] = self.simulation_data["start_time"]

        # Start update loop
        asyncio.create_task(self._update_loop())

        logger.info("Dashboard simulator started")

    async def stop(self):
        """Stop the dashboard simulator."""
        if not self.running:
            logger.warning("Dashboard simulator is not running")
            return

        logger.info("Stopping dashboard simulator...")
        self.running = False

        logger.info("Dashboard simulator stopped")

    async def _update_loop(self):
        """Update dashboard data periodically."""
        while self.running:
            try:
                # Update simulation time
                self.simulation_data["current_time"] += timedelta(seconds=self.update_interval)

                # Update market data
                self._update_market_data()

                # Generate trades
                self._generate_trades()

                # Update portfolio
                self._update_portfolio()

                # Update metrics
                self._update_metrics()

                # Save simulation data
                self._save_simulation_data()

                # Wait for next update
                await asyncio.sleep(self.update_interval)
            except Exception as e:
                logger.error(f"Error updating dashboard data: {str(e)}")
                await asyncio.sleep(1.0)

    def _update_market_data(self):
        """Update market data."""
        for market, data in self.simulation_data["market_data"].items():
            # Update price with random walk
            price_change = data["price"] * random.uniform(-0.002, 0.002)
            data["price"] += price_change

            # Update bid/ask
            spread = data["price"] * random.uniform(0.001, 0.003)
            data["bid"] = data["price"] - spread / 2
            data["ask"] = data["price"] + spread / 2
            data["spread"] = spread

            # Update volume
            data["volume_24h"] += data["volume_24h"] * random.uniform(-0.01, 0.01)

            # Update change
            data["change_24h"] += random.uniform(-0.1, 0.1)

            # Add to history
            data["history"].append({
                "timestamp": self.simulation_data["current_time"].isoformat(),
                "price": data["price"],
                "volume": data["volume_24h"] / 24 / 60 * self.update_interval
            })

            # Limit history length
            if len(data["history"]) > 1000:
                data["history"].pop(0)

    def _generate_trades(self):
        """Generate simulated trades."""
        # Determine if we should generate a trade
        if random.random() < 0.1 * self.update_interval:
            # Select random market
            market = random.choice(self.markets)
            market_data = self.simulation_data["market_data"][market]

            # Select random strategy
            strategy = random.choice([s for s in self.strategies if s["enabled"]])

            # Determine action
            if random.random() < 0.5:
                action = "buy"
            else:
                action = "sell"

            # Determine amount
            amount = random.uniform(0.1, 1.0)

            # Determine price
            if action == "buy":
                price = market_data["ask"]
            else:
                price = market_data["bid"]

            # Create trade
            trade = {
                "id": f"trade_{len(self.simulation_data['trades']) + 1}",
                "timestamp": self.simulation_data["current_time"].isoformat(),
                "market": market,
                "strategy": strategy["name"],
                "action": action,
                "amount": amount,
                "price": price,
                "value": amount * price,
                "status": "executed" if random.random() < 0.95 else "failed",
                "fee": amount * price * 0.001
            }

            # Add trade to list
            self.simulation_data["trades"].append(trade)

            # Update portfolio if trade was successful
            if trade["status"] == "executed":
                self._update_portfolio_with_trade(trade)

    def _update_portfolio_with_trade(self, trade: Dict[str, Any]):
        """
        Update portfolio with a trade.

        Args:
            trade: Trade dictionary
        """
        market = trade["market"]
        action = trade["action"]
        amount = trade["amount"]
        price = trade["price"]
        value = trade["value"]
        fee = trade["fee"]

        # Update positions
        if market not in self.simulation_data["portfolio"]["positions"]:
            self.simulation_data["portfolio"]["positions"][market] = {
                "amount": 0.0,
                "value": 0.0,
                "avg_price": 0.0
            }

        position = self.simulation_data["portfolio"]["positions"][market]

        if action == "buy":
            # Update balance
            self.simulation_data["portfolio"]["balance"] -= value + fee

            # Update position
            new_amount = position["amount"] + amount
            new_value = position["value"] + value
            position["avg_price"] = new_value / new_amount if new_amount > 0 else 0.0
            position["amount"] = new_amount
            position["value"] = new_value
        else:  # sell
            # Update balance
            self.simulation_data["portfolio"]["balance"] += value - fee

            # Update position
            position["amount"] -= amount
            position["value"] = position["amount"] * price

    def _update_portfolio(self):
        """Update portfolio values."""
        # Update position values based on current prices
        total_position_value = 0.0

        for market, position in self.simulation_data["portfolio"]["positions"].items():
            if position["amount"] > 0:
                current_price = self.simulation_data["market_data"][market]["price"]
                position["value"] = position["amount"] * current_price
                total_position_value += position["value"]

        # Update equity
        self.simulation_data["portfolio"]["equity"] = self.simulation_data["portfolio"]["balance"] + total_position_value

        # Update PnL
        initial_equity = 10000.0
        self.simulation_data["portfolio"]["pnl"] = self.simulation_data["portfolio"]["equity"] - initial_equity

    def _update_metrics(self):
        """Update system metrics."""
        # Update trade metrics
        successful_trades = len([t for t in self.simulation_data["trades"] if t["status"] == "executed"])
        failed_trades = len(self.simulation_data["trades"]) - successful_trades

        self.simulation_data["metrics"]["trades"]["total"] = len(self.simulation_data["trades"])
        self.simulation_data["metrics"]["trades"]["successful"] = successful_trades
        self.simulation_data["metrics"]["trades"]["failed"] = failed_trades

        # Update performance metrics
        if successful_trades > 0:
            # Calculate win rate
            winning_trades = len([
                t for t in self.simulation_data["trades"]
                if t["status"] == "executed" and
                (
                    (t["action"] == "buy" and self.simulation_data["market_data"][t["market"]]["price"] > t["price"]) or
                    (t["action"] == "sell" and self.simulation_data["market_data"][t["market"]]["price"] < t["price"])
                )
            ])
            self.simulation_data["metrics"]["performance"]["win_rate"] = winning_trades / successful_trades

            # Calculate profit factor
            profits = sum([
                self.simulation_data["market_data"][t["market"]]["price"] * t["amount"] - t["value"]
                for t in self.simulation_data["trades"]
                if t["status"] == "executed" and t["action"] == "buy" and
                self.simulation_data["market_data"][t["market"]]["price"] > t["price"]
            ])
            profits += sum([
                t["value"] - self.simulation_data["market_data"][t["market"]]["price"] * t["amount"]
                for t in self.simulation_data["trades"]
                if t["status"] == "executed" and t["action"] == "sell" and
                self.simulation_data["market_data"][t["market"]]["price"] < t["price"]
            ])

            losses = sum([
                t["value"] - self.simulation_data["market_data"][t["market"]]["price"] * t["amount"]
                for t in self.simulation_data["trades"]
                if t["status"] == "executed" and t["action"] == "buy" and
                self.simulation_data["market_data"][t["market"]]["price"] < t["price"]
            ])
            losses += sum([
                self.simulation_data["market_data"][t["market"]]["price"] * t["amount"] - t["value"]
                for t in self.simulation_data["trades"]
                if t["status"] == "executed" and t["action"] == "sell" and
                self.simulation_data["market_data"][t["market"]]["price"] > t["price"]
            ])

            if losses > 0:
                self.simulation_data["metrics"]["performance"]["profit_factor"] = profits / losses
            else:
                self.simulation_data["metrics"]["performance"]["profit_factor"] = 1.0

        # Update system metrics
        self.simulation_data["metrics"]["system"]["cpu_usage"] = random.uniform(10.0, 30.0)
        self.simulation_data["metrics"]["system"]["memory_usage"] = random.uniform(100.0, 500.0)
        self.simulation_data["metrics"]["system"]["api_calls"] += random.randint(1, 5)
        if random.random() < 0.05:
            self.simulation_data["metrics"]["system"]["errors"] += 1

    def _save_simulation_data(self):
        """Save simulation data to file."""
        try:
            # Create output directory
            os.makedirs("output/dashboard", exist_ok=True)

            # Create a copy of simulation data with serializable values
            serializable_data = self._make_json_serializable(self.simulation_data)

            # Save simulation data to file
            with open("output/dashboard/simulation_data.json", "w") as f:
                json.dump(serializable_data, f, indent=2)
        except Exception as e:
            logger.error(f"Error saving simulation data: {str(e)}")

    def _make_json_serializable(self, obj):
        """
        Convert an object to a JSON serializable format.

        Args:
            obj: Object to convert

        Returns:
            JSON serializable object
        """
        if isinstance(obj, dict):
            return {k: self._make_json_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._make_json_serializable(item) for item in obj]
        elif isinstance(obj, datetime):
            return obj.isoformat()
        elif isinstance(obj, (int, float, str, bool, type(None))):
            return obj
        else:
            return str(obj)

    def get_simulation_data(self) -> Dict[str, Any]:
        """
        Get simulation data.

        Returns:
            Simulation data dictionary
        """
        return self.simulation_data

    async def is_healthy(self) -> bool:
        """
        Check if the dashboard simulator is healthy.

        Returns:
            True if healthy, False otherwise
        """
        return self.running
