#!/usr/bin/env python3
"""
Test Unified Dashboard

This module tests the Synergy7 Unified Dashboard.
"""

import os
import sys
import logging
import unittest
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Get the path to the project root
project_root = os.path.abspath(os.path.join(os.path.dirname(os.path.abspath(__file__)), "../.."))

# Add the project root to the Python path
sys.path.insert(0, project_root)

class TestUnifiedDashboard(unittest.TestCase):
    """Test the Synergy7 Unified Dashboard."""

    def setUp(self):
        """Set up the test."""
        # Get the path to the dashboard directory
        self.dashboard_dir = os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
            "unified_dashboard"
        )
        
        # Get the path to the dashboard script
        self.dashboard_script = os.path.join(self.dashboard_dir, "app.py")
        
        # Get the path to the run script
        self.run_script = os.path.join(self.dashboard_dir, "run_dashboard.py")

    def test_dashboard_files_exist(self):
        """Test that the dashboard files exist."""
        # Check if the dashboard directory exists
        self.assertTrue(os.path.exists(self.dashboard_dir), f"Dashboard directory not found: {self.dashboard_dir}")
        
        # Check if the dashboard script exists
        self.assertTrue(os.path.exists(self.dashboard_script), f"Dashboard script not found: {self.dashboard_script}")
        
        # Check if the run script exists
        self.assertTrue(os.path.exists(self.run_script), f"Run script not found: {self.run_script}")

    def test_component_files_exist(self):
        """Test that the component files exist."""
        # Get the path to the components directory
        components_dir = os.path.join(self.dashboard_dir, "components")
        
        # Check if the components directory exists
        self.assertTrue(os.path.exists(components_dir), f"Components directory not found: {components_dir}")
        
        # Check if the component files exist
        component_files = [
            "trading_metrics.py",
            "system_metrics.py",
            "market_data.py"
        ]
        
        for component_file in component_files:
            component_path = os.path.join(components_dir, component_file)
            self.assertTrue(os.path.exists(component_path), f"Component file not found: {component_path}")

    def test_data_service_exists(self):
        """Test that the data service exists."""
        # Get the path to the data service
        data_service_path = os.path.join(self.dashboard_dir, "data_service.py")
        
        # Check if the data service exists
        self.assertTrue(os.path.exists(data_service_path), f"Data service not found: {data_service_path}")

    def test_streamlit_installed(self):
        """Test that Streamlit is installed."""
        try:
            import streamlit
            logger.info(f"Streamlit is installed (version: {streamlit.__version__})")
        except ImportError:
            self.fail("Streamlit is not installed")

    def test_dashboard_imports(self):
        """Test that the dashboard imports are valid."""
        try:
            # Run the script with Python's compile to check for syntax errors
            with open(self.dashboard_script, 'r') as f:
                content = f.read()
            
            compile(content, self.dashboard_script, 'exec')
            logger.info("Dashboard imports and syntax are valid")
        except Exception as e:
            self.fail(f"Error in dashboard imports or syntax: {str(e)}")

    def test_run_script_imports(self):
        """Test that the run script imports are valid."""
        try:
            # Run the script with Python's compile to check for syntax errors
            with open(self.run_script, 'r') as f:
                content = f.read()
            
            compile(content, self.run_script, 'exec')
            logger.info("Run script imports and syntax are valid")
        except Exception as e:
            self.fail(f"Error in run script imports or syntax: {str(e)}")

def main():
    """Main function to run the tests."""
    logger.info("Starting Unified Dashboard tests")
    
    # Run the tests
    unittest.main()

if __name__ == "__main__":
    main()
