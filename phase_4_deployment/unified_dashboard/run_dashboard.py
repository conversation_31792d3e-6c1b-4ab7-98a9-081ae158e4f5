#!/usr/bin/env python3
"""
Run Unified Dashboard

This script runs the Synergy7 Unified Dashboard.
"""

import os
import sys
import subprocess
import logging
import argparse

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """Main function."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Run Synergy7 Unified Dashboard")
    parser.add_argument("--port", type=int, default=8501, help="Port to run Streamlit on")
    parser.add_argument("--host", default="0.0.0.0", help="Host to run Streamlit on")
    parser.add_argument("--browser", action="store_true", help="Open browser automatically")
    parser.add_argument("--headless", action="store_true", help="Run in headless mode")

    args = parser.parse_args()

    # Get the path to the project root
    project_root = os.path.abspath(os.path.join(os.path.dirname(os.path.abspath(__file__)), "../.."))

    # Add the project root to the Python path
    sys.path.insert(0, project_root)

    # Set the PYTHONPATH environment variable
    os.environ["PYTHONPATH"] = project_root + os.pathsep + os.environ.get("PYTHONPATH", "")

    # Get the path to the dashboard script
    dashboard_path = os.path.join(
        os.path.dirname(os.path.abspath(__file__)),
        "app.py",
    )

    # Check if the dashboard script exists
    if not os.path.exists(dashboard_path):
        logger.error(f"Dashboard script not found: {dashboard_path}")
        return 1

    # Run Streamlit
    logger.info(f"Running Synergy7 Unified Dashboard on {args.host}:{args.port}")
    logger.info(f"Project root: {project_root}")
    logger.info(f"Python path: {sys.path}")

    try:
        # Install Streamlit if not already installed
        try:
            import streamlit
            logger.info(f"Streamlit is already installed (version: {streamlit.__version__})")
        except ImportError:
            logger.info("Installing Streamlit...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", "streamlit"])

        # Run Streamlit with the updated environment
        env = os.environ.copy()
        env["PYTHONPATH"] = project_root + os.pathsep + env.get("PYTHONPATH", "")

        logger.info(f"Running Streamlit with PYTHONPATH: {env['PYTHONPATH']}")

        # Build command
        cmd = [
            "streamlit",
            "run",
            dashboard_path,
            "--server.port",
            str(args.port),
            "--server.address",
            args.host,
        ]

        # Add headless mode if specified
        if args.headless:
            cmd.extend(["--server.headless", "true"])

        # Add browser option if specified
        if not args.browser:
            cmd.extend(["--server.enableCORS", "false", "--server.enableXsrfProtection", "false"])

        # Run Streamlit
        subprocess.run(cmd, env=env)
    except Exception as e:
        logger.error(f"Error running Synergy7 Unified Dashboard: {str(e)}")
        return 1

    return 0

if __name__ == "__main__":
    sys.exit(main())
