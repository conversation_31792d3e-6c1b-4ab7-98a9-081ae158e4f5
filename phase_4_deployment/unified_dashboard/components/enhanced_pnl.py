#!/usr/bin/env python3
"""
Enhanced PnL Component

This module provides advanced profit and loss visualizations for the Synergy7 Unified Dashboard.
"""

import pandas as pd
import numpy as np
import streamlit as st
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple

def render_enhanced_pnl(data: Dict[str, Any]):
    """
    Render enhanced profit and loss metrics with advanced visualizations.

    Args:
        data: Dashboard data
    """
    st.header("Enhanced Profit & Loss Analytics")

    # Create tabs for different profit metrics
    pnl_tabs = st.tabs([
        "PnL Overview",
        "Strategy Attribution",
        "Risk-Adjusted Metrics",
        "Drawdown Analysis",
        "Token Performance"
    ])

    with pnl_tabs[0]:  # PnL Overview
        render_pnl_overview(data)

    with pnl_tabs[1]:  # Strategy Attribution
        render_strategy_attribution(data)

    with pnl_tabs[2]:  # Risk-Adjusted Metrics
        render_risk_adjusted_metrics(data)

    with pnl_tabs[3]:  # Drawdown Analysis
        render_drawdown_analysis(data)

    with pnl_tabs[4]:  # Token Performance
        render_token_performance(data)

def render_pnl_overview(data: Dict[str, Any]):
    """
    Render PnL overview with multi-timeframe analysis.

    Args:
        data: Dashboard data
    """
    st.subheader("Profit & Loss Overview")

    # Process transaction data
    tx_data, pnl_metrics = process_transaction_data(data)
    if tx_data.empty:
        st.info("No transaction data available for PnL analysis")
        return

    # Display key metrics
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric(
            "Total PnL (SOL)",
            f"{pnl_metrics['total_pnl_sol']:.4f}",
            delta=f"{pnl_metrics['daily_pnl_sol']:.4f} (24h)",
            delta_color="normal" if pnl_metrics['daily_pnl_sol'] >= 0 else "inverse"
        )

    with col2:
        st.metric(
            "Total PnL (USD)",
            f"${pnl_metrics['total_pnl_usd']:.2f}",
            delta=f"${pnl_metrics['daily_pnl_usd']:.2f} (24h)",
            delta_color="normal" if pnl_metrics['daily_pnl_usd'] >= 0 else "inverse"
        )

    with col3:
        st.metric(
            "ROI",
            f"{pnl_metrics['roi']:.2%}",
            delta=f"{pnl_metrics['daily_roi']:.2%} (24h)",
            delta_color="normal" if pnl_metrics['daily_roi'] >= 0 else "inverse"
        )

    with col4:
        st.metric(
            "Win Rate",
            f"{pnl_metrics['win_rate']:.2%}",
            delta=f"{pnl_metrics['win_rate'] - 0.5:.2%} vs 50%",
            delta_color="normal" if pnl_metrics['win_rate'] >= 0.5 else "inverse"
        )

    # Timeframe selection for PnL chart
    timeframe = st.selectbox(
        "Select Timeframe",
        ["1D", "1W", "1M", "3M", "YTD", "All"],
        index=2
    )

    # Filter data based on selected timeframe
    filtered_data = filter_by_timeframe(tx_data, timeframe)

    # Create cumulative PnL chart
    fig = create_cumulative_pnl_chart(filtered_data, pnl_metrics['sol_price_usd'])
    st.plotly_chart(fig, use_container_width=True)

    # Create PnL distribution chart
    col1, col2 = st.columns(2)

    with col1:
        fig = create_pnl_distribution_chart(filtered_data)
        st.plotly_chart(fig, use_container_width=True)

    with col2:
        fig = create_pnl_by_day_chart(filtered_data)
        st.plotly_chart(fig, use_container_width=True)

def render_strategy_attribution(data: Dict[str, Any]):
    """
    Render strategy attribution analysis.

    Args:
        data: Dashboard data
    """
    st.subheader("Strategy Attribution Analysis")

    # Process strategy data
    strategy_data, strategy_metrics = process_strategy_data(data)
    if not strategy_data:
        st.info("No strategy data available for attribution analysis")
        return

    # Display strategy metrics
    col1, col2, col3 = st.columns(3)

    with col1:
        st.metric(
            "Best Strategy",
            strategy_metrics['best_strategy_name'],
            delta=f"Score: {strategy_metrics['best_strategy_score']:.2f}",
            delta_color="normal"
        )

    with col2:
        st.metric(
            "Most Active Strategy",
            strategy_metrics['most_active_strategy_name'],
            delta=f"{strategy_metrics['most_active_strategy_trades']} trades",
            delta_color="normal"
        )

    with col3:
        st.metric(
            "Highest Win Rate",
            strategy_metrics['highest_winrate_strategy_name'],
            delta=f"{strategy_metrics['highest_winrate_strategy_rate']:.2%}",
            delta_color="normal"
        )

    # Create strategy attribution waterfall chart
    fig = create_strategy_attribution_chart(strategy_data)
    st.plotly_chart(fig, use_container_width=True)

    # Create strategy correlation heatmap
    fig = create_strategy_correlation_chart(strategy_data)
    st.plotly_chart(fig, use_container_width=True)

def render_risk_adjusted_metrics(data: Dict[str, Any]):
    """
    Render risk-adjusted performance metrics.

    Args:
        data: Dashboard data
    """
    st.subheader("Risk-Adjusted Performance Metrics")

    # Process transaction data for risk metrics
    tx_data, risk_metrics = process_risk_metrics(data)
    if tx_data.empty:
        st.info("No transaction data available for risk analysis")
        return

    # Display risk metrics
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric(
            "Sharpe Ratio",
            f"{risk_metrics['sharpe_ratio']:.2f}",
            delta=f"{risk_metrics['sharpe_ratio'] - 1:.2f} vs 1.0",
            delta_color="normal" if risk_metrics['sharpe_ratio'] >= 1 else "inverse"
        )

    with col2:
        st.metric(
            "Sortino Ratio",
            f"{risk_metrics['sortino_ratio']:.2f}",
            delta=f"{risk_metrics['sortino_ratio'] - 1:.2f} vs 1.0",
            delta_color="normal" if risk_metrics['sortino_ratio'] >= 1 else "inverse"
        )

    with col3:
        st.metric(
            "Calmar Ratio",
            f"{risk_metrics['calmar_ratio']:.2f}",
            delta=f"{risk_metrics['calmar_ratio'] - 0.5:.2f} vs 0.5",
            delta_color="normal" if risk_metrics['calmar_ratio'] >= 0.5 else "inverse"
        )

    with col4:
        st.metric(
            "Volatility",
            f"{risk_metrics['volatility']:.2%}",
            delta=f"{risk_metrics['volatility'] - 0.1:.2%} vs 10%",
            delta_color="inverse" if risk_metrics['volatility'] >= 0.1 else "normal"
        )

    # Create risk metrics time series chart
    fig = create_risk_metrics_chart(tx_data, risk_metrics)
    st.plotly_chart(fig, use_container_width=True)

    # Create risk-return scatter plot
    fig = create_risk_return_chart(data)
    st.plotly_chart(fig, use_container_width=True)

def render_drawdown_analysis(data: Dict[str, Any]):
    """
    Render drawdown analysis.

    Args:
        data: Dashboard data
    """
    st.subheader("Drawdown Analysis")

    # Process transaction data for drawdown analysis
    tx_data, drawdown_metrics = process_drawdown_data(data)
    if tx_data.empty:
        st.info("No transaction data available for drawdown analysis")
        return

    # Display drawdown metrics
    col1, col2, col3 = st.columns(3)

    with col1:
        st.metric(
            "Maximum Drawdown",
            f"{drawdown_metrics['max_drawdown']:.2%}",
            delta=f"{drawdown_metrics['max_drawdown'] - 0.15:.2%} vs 15%",
            delta_color="inverse" if drawdown_metrics['max_drawdown'] >= 0.15 else "normal"
        )

    with col2:
        st.metric(
            "Average Drawdown",
            f"{drawdown_metrics['avg_drawdown']:.2%}",
            delta=f"{drawdown_metrics['avg_drawdown'] - 0.05:.2%} vs 5%",
            delta_color="inverse" if drawdown_metrics['avg_drawdown'] >= 0.05 else "normal"
        )

    with col3:
        st.metric(
            "Average Recovery Time",
            f"{drawdown_metrics['avg_recovery_time']:.1f} days",
            delta=f"{drawdown_metrics['avg_recovery_time'] - 3:.1f} vs 3 days",
            delta_color="inverse" if drawdown_metrics['avg_recovery_time'] >= 3 else "normal"
        )

    # Create underwater chart
    fig = create_underwater_chart(tx_data, drawdown_metrics)
    st.plotly_chart(fig, use_container_width=True)

    # Create drawdown distribution chart
    fig = create_drawdown_distribution_chart(drawdown_metrics)
    st.plotly_chart(fig, use_container_width=True)

def render_token_performance(data: Dict[str, Any]):
    """
    Render token performance analysis.

    Args:
        data: Dashboard data
    """
    st.subheader("Token Performance Analysis")

    # Process transaction data for token analysis
    token_data, token_metrics = process_token_data(data)
    if not token_data:
        st.info("No token data available for performance analysis")
        return

    # Display token metrics
    col1, col2, col3 = st.columns(3)

    with col1:
        st.metric(
            "Best Performing Token",
            token_metrics['best_token'],
            delta=f"{token_metrics['best_token_return']:.2%}",
            delta_color="normal"
        )

    with col2:
        st.metric(
            "Most Traded Token",
            token_metrics['most_traded_token'],
            delta=f"{token_metrics['most_traded_token_count']} trades",
            delta_color="normal"
        )

    with col3:
        st.metric(
            "Highest Volume Token",
            token_metrics['highest_volume_token'],
            delta=f"${token_metrics['highest_volume_token_amount']:.2f}",
            delta_color="normal"
        )

    # Create token performance chart
    fig = create_token_performance_chart(token_data)
    st.plotly_chart(fig, use_container_width=True)

    # Create token allocation sunburst chart
    fig = create_token_allocation_chart(token_data)
    st.plotly_chart(fig, use_container_width=True)

def filter_by_timeframe(df: pd.DataFrame, timeframe: str) -> pd.DataFrame:
    """
    Filter DataFrame by selected timeframe.

    Args:
        df: DataFrame with datetime column
        timeframe: Selected timeframe (1D, 1W, 1M, 3M, YTD, All)

    Returns:
        Filtered DataFrame
    """
    now = datetime.now()

    if timeframe == "1D":
        start_date = now - timedelta(days=1)
    elif timeframe == "1W":
        start_date = now - timedelta(weeks=1)
    elif timeframe == "1M":
        start_date = now - timedelta(days=30)
    elif timeframe == "3M":
        start_date = now - timedelta(days=90)
    elif timeframe == "YTD":
        start_date = datetime(now.year, 1, 1)
    else:  # All
        return df

    return df[df['datetime'] >= start_date]

def create_cumulative_pnl_chart(df: pd.DataFrame, sol_price_usd: float) -> go.Figure:
    """
    Create a cumulative PnL chart.

    Args:
        df: Transaction DataFrame
        sol_price_usd: SOL price in USD

    Returns:
        Plotly figure
    """
    if df.empty:
        # Create empty figure with message
        fig = go.Figure()
        fig.add_annotation(
            text="No transaction data available",
            xref="paper", yref="paper",
            x=0.5, y=0.5,
            showarrow=False
        )
        return fig

    # Create a copy of the DataFrame
    df_copy = df.copy()

    # Calculate cumulative PnL
    df_copy['pnl'] = df_copy.apply(
        lambda row: row['amount'] * row['price'] * (-1 if row['type'] == 'buy' else 1),
        axis=1
    )
    df_copy['cumulative_pnl'] = df_copy['pnl'].cumsum()
    df_copy['cumulative_pnl_usd'] = df_copy['cumulative_pnl'] * sol_price_usd

    # Create figure with secondary y-axis
    fig = make_subplots(specs=[[{"secondary_y": True}]])

    # Add traces
    fig.add_trace(
        go.Scatter(
            x=df_copy['datetime'],
            y=df_copy['cumulative_pnl'],
            mode='lines',
            name='Cumulative PnL (SOL)',
            line=dict(color='blue', width=2)
        ),
        secondary_y=False
    )

    fig.add_trace(
        go.Scatter(
            x=df_copy['datetime'],
            y=df_copy['cumulative_pnl_usd'],
            mode='lines',
            name='Cumulative PnL (USD)',
            line=dict(color='green', width=2, dash='dash')
        ),
        secondary_y=True
    )

    # Add buy/sell markers
    buys = df_copy[df_copy['type'] == 'buy']
    sells = df_copy[df_copy['type'] == 'sell']

    fig.add_trace(
        go.Scatter(
            x=buys['datetime'],
            y=buys['cumulative_pnl'],
            mode='markers',
            name='Buy',
            marker=dict(color='red', size=8, symbol='circle'),
            hovertemplate='%{text}<extra></extra>',
            text=[f"Buy {row['amount']} {row['token']} at {row['price']}" for _, row in buys.iterrows()]
        ),
        secondary_y=False
    )

    fig.add_trace(
        go.Scatter(
            x=sells['datetime'],
            y=sells['cumulative_pnl'],
            mode='markers',
            name='Sell',
            marker=dict(color='green', size=8, symbol='circle'),
            hovertemplate='%{text}<extra></extra>',
            text=[f"Sell {row['amount']} {row['token']} at {row['price']}" for _, row in sells.iterrows()]
        ),
        secondary_y=False
    )

    # Add range selector
    fig.update_layout(
        title="Cumulative Profit & Loss",
        xaxis=dict(
            rangeselector=dict(
                buttons=list([
                    dict(count=1, label="1d", step="day", stepmode="backward"),
                    dict(count=7, label="1w", step="day", stepmode="backward"),
                    dict(count=1, label="1m", step="month", stepmode="backward"),
                    dict(count=3, label="3m", step="month", stepmode="backward"),
                    dict(step="all")
                ])
            ),
            rangeslider=dict(visible=True),
            type="date"
        ),
        yaxis=dict(title="PnL (SOL)"),
        yaxis2=dict(title="PnL (USD)", overlaying="y", side="right"),
        hovermode="x unified",
        legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1)
    )

    return fig

def create_pnl_distribution_chart(df: pd.DataFrame) -> go.Figure:
    """
    Create a PnL distribution chart.

    Args:
        df: Transaction DataFrame

    Returns:
        Plotly figure
    """
    if df.empty:
        # Create empty figure with message
        fig = go.Figure()
        fig.add_annotation(
            text="No transaction data available",
            xref="paper", yref="paper",
            x=0.5, y=0.5,
            showarrow=False
        )
        return fig

    # Calculate PnL for each transaction
    df_copy = df.copy()
    df_copy['pnl'] = df_copy.apply(
        lambda row: row['amount'] * row['price'] * (-1 if row['type'] == 'buy' else 1),
        axis=1
    )

    # Create histogram
    fig = go.Figure()
    fig.add_trace(go.Histogram(
        x=df_copy['pnl'],
        nbinsx=20,
        marker_color='blue',
        opacity=0.7,
        name='PnL Distribution'
    ))

    # Add vertical line at zero
    fig.add_shape(
        type="line",
        x0=0, y0=0,
        x1=0, y1=1,
        yref="paper",
        line=dict(color="red", width=2, dash="dash")
    )

    fig.update_layout(
        title="PnL Distribution",
        xaxis_title="PnL (SOL)",
        yaxis_title="Frequency",
        bargap=0.1
    )

    return fig

def create_pnl_by_day_chart(df: pd.DataFrame) -> go.Figure:
    """
    Create a PnL by day chart.

    Args:
        df: Transaction DataFrame

    Returns:
        Plotly figure
    """
    if df.empty:
        # Create empty figure with message
        fig = go.Figure()
        fig.add_annotation(
            text="No transaction data available",
            xref="paper", yref="paper",
            x=0.5, y=0.5,
            showarrow=False
        )
        return fig

    # Calculate PnL for each transaction
    df_copy = df.copy()
    df_copy['pnl'] = df_copy.apply(
        lambda row: row['amount'] * row['price'] * (-1 if row['type'] == 'buy' else 1),
        axis=1
    )

    # Group by day
    df_copy['date'] = df_copy['datetime'].dt.date
    daily_pnl = df_copy.groupby('date')['pnl'].sum().reset_index()

    # Create bar chart
    fig = go.Figure()
    fig.add_trace(go.Bar(
        x=daily_pnl['date'],
        y=daily_pnl['pnl'],
        marker_color=['green' if pnl >= 0 else 'red' for pnl in daily_pnl['pnl']],
        name='Daily PnL'
    ))

    fig.update_layout(
        title="Daily PnL",
        xaxis_title="Date",
        yaxis_title="PnL (SOL)",
        hovermode="x unified"
    )

    return fig

def create_underwater_chart(df: pd.DataFrame, drawdown_metrics: Dict[str, Any]) -> go.Figure:
    """
    Create an underwater chart showing drawdown over time.

    Args:
        df: DataFrame with drawdown data
        drawdown_metrics: Dictionary of drawdown metrics

    Returns:
        Plotly figure
    """
    if df.empty:
        # Create empty figure with message
        fig = go.Figure()
        fig.add_annotation(
            text="No drawdown data available",
            xref="paper", yref="paper",
            x=0.5, y=0.5,
            showarrow=False
        )
        return fig

    # Create underwater chart
    fig = go.Figure()

    # Add drawdown area
    fig.add_trace(go.Scatter(
        x=df['date'],
        y=df['drawdown'] * 100,  # Convert to percentage
        fill='tozeroy',
        mode='lines',
        line=dict(color='rgba(231, 76, 60, 0.8)', width=1),
        fillcolor='rgba(231, 76, 60, 0.3)',
        name='Drawdown'
    ))

    # Add horizontal line at zero
    fig.add_shape(
        type="line",
        x0=df['date'].min(),
        y0=0,
        x1=df['date'].max(),
        y1=0,
        line=dict(color="black", width=1, dash="dash")
    )

    # Add annotations for major drawdown periods
    if 'drawdown_periods' in drawdown_metrics and drawdown_metrics['drawdown_periods']:
        # Sort drawdown periods by max_drawdown (descending)
        sorted_periods = sorted(drawdown_metrics['drawdown_periods'],
                               key=lambda x: x['max_drawdown'],
                               reverse=True)

        # Add annotations for top 3 drawdown periods
        for i, period in enumerate(sorted_periods[:3]):
            # Find the date of maximum drawdown within this period
            period_data = df[(df['date'] >= period['start_date']) &
                            (df['date'] <= period['end_date'])]
            if not period_data.empty:
                max_dd_date = period_data.loc[period_data['drawdown'].idxmin(), 'date']
                max_dd_value = period_data['drawdown'].min() * 100

                fig.add_annotation(
                    x=max_dd_date,
                    y=max_dd_value,
                    text=f"{max_dd_value:.1f}%",
                    showarrow=True,
                    arrowhead=1,
                    arrowsize=1,
                    arrowwidth=2,
                    arrowcolor="red",
                    ax=0,
                    ay=30
                )

    # Update layout
    fig.update_layout(
        title="Drawdown Analysis (Underwater Chart)",
        xaxis_title="Date",
        yaxis_title="Drawdown (%)",
        yaxis=dict(
            tickformat=".1f",
            ticksuffix="%",
            range=[min(df['drawdown'] * 100) * 1.1, 5]  # Add some padding below the minimum
        ),
        hovermode="x unified"
    )

    return fig

def create_drawdown_distribution_chart(drawdown_metrics: Dict[str, Any]) -> go.Figure:
    """
    Create a drawdown distribution chart.

    Args:
        drawdown_metrics: Dictionary of drawdown metrics

    Returns:
        Plotly figure
    """
    if 'drawdown_periods' not in drawdown_metrics or not drawdown_metrics['drawdown_periods']:
        # Create empty figure with message
        fig = go.Figure()
        fig.add_annotation(
            text="No drawdown periods available",
            xref="paper", yref="paper",
            x=0.5, y=0.5,
            showarrow=False
        )
        return fig

    # Extract drawdown data
    drawdown_periods = drawdown_metrics['drawdown_periods']

    # Create figure with two subplots
    fig = make_subplots(
        rows=1, cols=2,
        subplot_titles=("Drawdown Magnitude Distribution", "Recovery Time Distribution"),
        specs=[[{"type": "bar"}, {"type": "bar"}]]
    )

    # Drawdown magnitude distribution
    drawdown_values = [period['max_drawdown'] * 100 for period in drawdown_periods]

    # Create bins for drawdown magnitude
    bins = [0, 1, 2, 5, 10, 20, 100]
    bin_labels = ['0-1%', '1-2%', '2-5%', '5-10%', '10-20%', '>20%']

    # Count drawdowns in each bin
    bin_counts = [0] * len(bin_labels)
    for dd in drawdown_values:
        for i, upper in enumerate(bins[1:]):
            if dd <= upper:
                bin_counts[i] += 1
                break

    # Add drawdown magnitude histogram
    fig.add_trace(
        go.Bar(
            x=bin_labels,
            y=bin_counts,
            marker_color='rgba(231, 76, 60, 0.7)',
            name='Drawdown Magnitude'
        ),
        row=1, col=1
    )

    # Recovery time distribution (only for completed drawdowns)
    completed_periods = [period for period in drawdown_periods
                        if 'ongoing' not in period or not period['ongoing']]

    if completed_periods:
        recovery_times = [period['duration'] for period in completed_periods]

        # Create bins for recovery time
        time_bins = [0, 1, 3, 7, 14, 30, 90, 365]
        time_bin_labels = ['1d', '1-3d', '3-7d', '1-2w', '2-4w', '1-3m', '>3m']

        # Count recovery times in each bin
        time_bin_counts = [0] * len(time_bin_labels)
        for rt in recovery_times:
            for i, upper in enumerate(time_bins[1:]):
                if rt <= upper:
                    time_bin_counts[i] += 1
                    break

        # Add recovery time histogram
        fig.add_trace(
            go.Bar(
                x=time_bin_labels,
                y=time_bin_counts,
                marker_color='rgba(52, 152, 219, 0.7)',
                name='Recovery Time'
            ),
            row=1, col=2
        )

    # Update layout
    fig.update_layout(
        title="Drawdown Distribution Analysis",
        height=400,
        showlegend=False,
        hovermode="closest"
    )

    # Update x and y axis labels
    fig.update_xaxes(title_text="Drawdown Magnitude", row=1, col=1)
    fig.update_yaxes(title_text="Frequency", row=1, col=1)
    fig.update_xaxes(title_text="Recovery Time", row=1, col=2)
    fig.update_yaxes(title_text="Frequency", row=1, col=2)

    return fig

def process_token_data(data: Dict[str, Any]) -> Tuple[Dict[str, Any], Dict[str, Any]]:
    """
    Process transaction data for token performance analysis.

    Args:
        data: Dashboard data

    Returns:
        Tuple containing processed token data and metrics
    """
    # Default token metrics
    token_metrics = {
        'best_token': 'N/A',
        'best_token_return': 0.0,
        'worst_token': 'N/A',
        'worst_token_return': 0.0,
        'most_traded_token': 'N/A',
        'most_traded_token_count': 0,
        'highest_volume_token': 'N/A',
        'highest_volume_token_amount': 0.0
    }

    # Get transaction data
    tx_df, _ = process_transaction_data(data)
    if tx_df.empty:
        return {}, token_metrics

    # Group by token
    token_data = {}

    # Get unique tokens
    tokens = tx_df['token'].unique()

    for token in tokens:
        # Filter transactions for this token
        token_txs = tx_df[tx_df['token'] == token]

        # Calculate token metrics
        buy_txs = token_txs[token_txs['type'] == 'buy']
        sell_txs = token_txs[token_txs['type'] == 'sell']

        # Calculate total bought and sold
        total_bought = buy_txs['amount'].sum()
        total_sold = sell_txs['amount'].sum()

        # Calculate total value bought and sold
        total_bought_value = (buy_txs['amount'] * buy_txs['price']).sum()
        total_sold_value = (sell_txs['amount'] * sell_txs['price']).sum()

        # Calculate average buy and sell prices
        avg_buy_price = total_bought_value / total_bought if total_bought > 0 else 0
        avg_sell_price = total_sold_value / total_sold if total_sold > 0 else 0

        # Calculate return
        token_return = (avg_sell_price / avg_buy_price - 1) if avg_buy_price > 0 and avg_sell_price > 0 else 0

        # Calculate volume
        volume = total_bought_value + total_sold_value

        # Store token data
        token_data[token] = {
            'total_bought': total_bought,
            'total_sold': total_sold,
            'total_bought_value': total_bought_value,
            'total_sold_value': total_sold_value,
            'avg_buy_price': avg_buy_price,
            'avg_sell_price': avg_sell_price,
            'return': token_return,
            'volume': volume,
            'trade_count': len(token_txs),
            'current_position': total_bought - total_sold,
            'current_value': (total_bought - total_sold) * token_txs.iloc[-1]['price'] if len(token_txs) > 0 else 0
        }

        # Update best token
        if token_return > token_metrics['best_token_return']:
            token_metrics['best_token'] = token
            token_metrics['best_token_return'] = token_return

        # Update worst token
        if token_return < token_metrics['worst_token_return']:
            token_metrics['worst_token'] = token
            token_metrics['worst_token_return'] = token_return

        # Update most traded token
        if len(token_txs) > token_metrics['most_traded_token_count']:
            token_metrics['most_traded_token'] = token
            token_metrics['most_traded_token_count'] = len(token_txs)

        # Update highest volume token
        if volume > token_metrics['highest_volume_token_amount']:
            token_metrics['highest_volume_token'] = token
            token_metrics['highest_volume_token_amount'] = volume

    return token_data, token_metrics

def create_token_performance_chart(token_data: Dict[str, Any]) -> go.Figure:
    """
    Create a token performance chart.

    Args:
        token_data: Dictionary of token data

    Returns:
        Plotly figure
    """
    if not token_data:
        # Create empty figure with message
        fig = go.Figure()
        fig.add_annotation(
            text="No token data available",
            xref="paper", yref="paper",
            x=0.5, y=0.5,
            showarrow=False
        )
        return fig

    # Extract token returns
    tokens = []
    returns = []
    volumes = []
    positions = []

    for token, data in token_data.items():
        tokens.append(token)
        returns.append(data['return'] * 100)  # Convert to percentage
        volumes.append(data['volume'])
        positions.append(data['current_position'])

    # Create figure
    fig = go.Figure()

    # Add bar chart for returns
    fig.add_trace(go.Bar(
        x=tokens,
        y=returns,
        marker_color=['green' if r >= 0 else 'red' for r in returns],
        name='Return (%)',
        text=[f"{r:.1f}%" for r in returns],
        textposition='auto'
    ))

    # Update layout
    fig.update_layout(
        title="Token Performance Comparison",
        xaxis_title="Token",
        yaxis_title="Return (%)",
        hovermode="closest",
        height=400
    )

    return fig

def create_token_allocation_chart(token_data: Dict[str, Any]) -> go.Figure:
    """
    Create a token allocation sunburst chart.

    Args:
        token_data: Dictionary of token data

    Returns:
        Plotly figure
    """
    if not token_data:
        # Create empty figure with message
        fig = go.Figure()
        fig.add_annotation(
            text="No token data available",
            xref="paper", yref="paper",
            x=0.5, y=0.5,
            showarrow=False
        )
        return fig

    # Extract token positions and values
    labels = ['Portfolio']
    parents = ['']
    values = [0]

    total_value = 0
    for token, data in token_data.items():
        if data['current_position'] > 0:
            labels.append(token)
            parents.append('Portfolio')
            values.append(abs(data['current_value']))
            total_value += abs(data['current_value'])

    # Update portfolio total
    values[0] = total_value

    # Create sunburst chart
    fig = go.Figure(go.Sunburst(
        labels=labels,
        parents=parents,
        values=values,
        branchvalues="total",
        hovertemplate='<b>%{label}</b><br>Value: $%{value:.2f}<br>Allocation: %{percentRoot:.1%}<extra></extra>',
        marker=dict(
            colors=['#636EFA', '#EF553B', '#00CC96', '#AB63FA', '#FFA15A', '#19D3F3', '#FF6692', '#B6E880', '#FF97FF', '#FECB52']
        )
    ))

    # Update layout
    fig.update_layout(
        title="Current Portfolio Allocation",
        height=500,
        margin=dict(t=30, l=0, r=0, b=0)
    )

    return fig

def create_strategy_attribution_chart(strategy_data: Dict[str, Any]) -> go.Figure:
    """
    Create a strategy attribution waterfall chart.

    Args:
        strategy_data: Dictionary of strategy data

    Returns:
        Plotly figure
    """
    if not strategy_data:
        # Create empty figure with message
        fig = go.Figure()
        fig.add_annotation(
            text="No strategy data available",
            xref="paper", yref="paper",
            x=0.5, y=0.5,
            showarrow=False
        )
        return fig

    # Extract strategy PnL data
    strategies = []
    pnls = []

    for strategy_id, data in strategy_data.items():
        strategies.append(data['name'])
        pnls.append(data['total_pnl'])

    # Sort by PnL (descending)
    sorted_indices = sorted(range(len(pnls)), key=lambda i: pnls[i], reverse=True)
    strategies = [strategies[i] for i in sorted_indices]
    pnls = [pnls[i] for i in sorted_indices]

    # Create waterfall chart
    fig = go.Figure(go.Waterfall(
        name="Strategy Attribution",
        orientation="v",
        measure=["relative"] * len(strategies) + ["total"],
        x=strategies + ["Total"],
        textposition="outside",
        text=[f"{pnl:.2f}" for pnl in pnls] + [f"{sum(pnls):.2f}"],
        y=pnls + [sum(pnls)],
        connector={"line": {"color": "rgb(63, 63, 63)"}},
        increasing={"marker": {"color": "green"}},
        decreasing={"marker": {"color": "red"}},
        totals={"marker": {"color": "blue"}}
    ))

    # Update layout
    fig.update_layout(
        title="Strategy Attribution Analysis",
        xaxis_title="Strategy",
        yaxis_title="PnL Contribution",
        showlegend=False
    )

    return fig

def create_strategy_correlation_chart(strategy_data: Dict[str, Any]) -> go.Figure:
    """
    Create a strategy correlation heatmap.

    Args:
        strategy_data: Dictionary of strategy data

    Returns:
        Plotly figure
    """
    if not strategy_data or len(strategy_data) < 2:
        # Create empty figure with message
        fig = go.Figure()
        fig.add_annotation(
            text="Insufficient strategy data for correlation analysis",
            xref="paper", yref="paper",
            x=0.5, y=0.5,
            showarrow=False
        )
        return fig

    # Create a DataFrame with daily PnL for each strategy
    strategy_daily_pnl = {}
    all_dates = set()

    for strategy_id, data in strategy_data.items():
        if 'trades' in data:
            # Group trades by date
            daily_pnl = {}
            for trade in data['trades']:
                if 'timestamp' in trade and 'profit_loss' in trade:
                    date = datetime.fromtimestamp(trade['timestamp']).date()
                    all_dates.add(date)
                    if date in daily_pnl:
                        daily_pnl[date] += trade['profit_loss']
                    else:
                        daily_pnl[date] = trade['profit_loss']

            strategy_daily_pnl[data['name']] = daily_pnl

    # Create DataFrame with all dates and strategies
    df = pd.DataFrame(index=sorted(all_dates), columns=list(strategy_daily_pnl.keys()))

    # Fill DataFrame with PnL values
    for strategy, daily_pnl in strategy_daily_pnl.items():
        for date, pnl in daily_pnl.items():
            df.at[date, strategy] = pnl

    # Fill NaN values with 0
    df = df.fillna(0)

    # Calculate correlation matrix
    corr_matrix = df.corr()

    # Create heatmap
    fig = go.Figure(data=go.Heatmap(
        z=corr_matrix.values,
        x=corr_matrix.columns,
        y=corr_matrix.index,
        colorscale='RdBu_r',
        zmin=-1,
        zmax=1,
        colorbar=dict(title="Correlation"),
        hovertemplate='%{x} vs %{y}<br>Correlation: %{z:.2f}<extra></extra>'
    ))

    # Update layout
    fig.update_layout(
        title="Strategy Correlation Matrix",
        height=500,
        xaxis=dict(tickangle=-45),
        yaxis=dict(tickangle=0)
    )

    return fig

def create_risk_metrics_chart(df: pd.DataFrame, risk_metrics: Dict[str, Any]) -> go.Figure:
    """
    Create a risk metrics time series chart.

    Args:
        df: Transaction DataFrame
        risk_metrics: Dictionary of risk metrics

    Returns:
        Plotly figure
    """
    if df.empty:
        # Create empty figure with message
        fig = go.Figure()
        fig.add_annotation(
            text="No transaction data available",
            xref="paper", yref="paper",
            x=0.5, y=0.5,
            showarrow=False
        )
        return fig

    # Calculate rolling risk metrics
    window = 30  # 30-day rolling window

    # Ensure df has 'date' and 'pnl' columns
    if 'date' not in df.columns or 'pnl' not in df.columns:
        df['date'] = df['datetime'].dt.date if 'datetime' in df.columns else pd.to_datetime(df.index).date
        df['pnl'] = df.apply(
            lambda row: row['amount'] * row['price'] * (-1 if row['type'] == 'buy' else 1),
            axis=1
        ) if 'amount' in df.columns and 'price' in df.columns and 'type' in df.columns else 0

    # Group by date
    daily_pnl = df.groupby('date')['pnl'].sum().reset_index()

    # Calculate daily returns
    daily_pnl['daily_return'] = daily_pnl['pnl'].pct_change().fillna(0)

    # Calculate rolling metrics
    daily_pnl['rolling_return'] = daily_pnl['daily_return'].rolling(window=window).mean() * 252  # Annualized
    daily_pnl['rolling_volatility'] = daily_pnl['daily_return'].rolling(window=window).std() * np.sqrt(252)  # Annualized
    daily_pnl['rolling_sharpe'] = daily_pnl['rolling_return'] / daily_pnl['rolling_volatility']

    # Calculate rolling drawdown
    daily_pnl['cumulative_return'] = (1 + daily_pnl['daily_return']).cumprod()
    daily_pnl['rolling_peak'] = daily_pnl['cumulative_return'].rolling(window=window, min_periods=1).max()
    daily_pnl['rolling_drawdown'] = (daily_pnl['cumulative_return'] - daily_pnl['rolling_peak']) / daily_pnl['rolling_peak']

    # Create figure with secondary y-axis
    fig = make_subplots(specs=[[{"secondary_y": True}]])

    # Add Sharpe ratio trace
    fig.add_trace(
        go.Scatter(
            x=daily_pnl['date'],
            y=daily_pnl['rolling_sharpe'],
            mode='lines',
            name='Rolling Sharpe Ratio',
            line=dict(color='blue', width=2)
        ),
        secondary_y=False
    )

    # Add volatility trace
    fig.add_trace(
        go.Scatter(
            x=daily_pnl['date'],
            y=daily_pnl['rolling_volatility'] * 100,  # Convert to percentage
            mode='lines',
            name='Rolling Volatility (%)',
            line=dict(color='red', width=2, dash='dash')
        ),
        secondary_y=True
    )

    # Add drawdown trace
    fig.add_trace(
        go.Scatter(
            x=daily_pnl['date'],
            y=daily_pnl['rolling_drawdown'] * 100,  # Convert to percentage
            mode='lines',
            name='Rolling Drawdown (%)',
            line=dict(color='orange', width=2, dash='dot')
        ),
        secondary_y=True
    )

    # Update layout
    fig.update_layout(
        title="Rolling Risk Metrics (30-Day Window)",
        xaxis_title="Date",
        legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1)
    )

    # Update y-axes
    fig.update_yaxes(title_text="Sharpe Ratio", secondary_y=False)
    fig.update_yaxes(title_text="Volatility / Drawdown (%)", secondary_y=True)

    return fig

def create_risk_return_chart(data: Dict[str, Any]) -> go.Figure:
    """
    Create a risk-return scatter plot.

    Args:
        data: Dashboard data

    Returns:
        Plotly figure
    """
    # Check if strategy metrics exist
    if 'strategy_metrics' not in data or 'strategies' not in data['strategy_metrics']:
        # Create empty figure with message
        fig = go.Figure()
        fig.add_annotation(
            text="No strategy data available",
            xref="paper", yref="paper",
            x=0.5, y=0.5,
            showarrow=False
        )
        return fig

    strategies = data['strategy_metrics']['strategies']

    # Extract risk-return data
    strategy_names = []
    returns = []
    risks = []
    sharpe_ratios = []
    trade_counts = []

    for strategy_id, strategy_info in strategies.items():
        # Skip if no trades
        if 'trades' not in strategy_info or not strategy_info['trades']:
            continue

        # Calculate return (use composite score as a proxy)
        strategy_return = strategy_info.get('composite_score', 0.0)

        # Calculate risk (use max_drawdown as a proxy)
        strategy_risk = strategy_info.get('max_drawdown', 0.1)

        # Calculate Sharpe ratio
        sharpe_ratio = strategy_info.get('sharpe_ratio', 1.0)

        # Count trades
        trade_count = len(strategy_info['trades'])

        # Store data
        strategy_names.append(strategy_info.get('name', strategy_id))
        returns.append(strategy_return)
        risks.append(strategy_risk)
        sharpe_ratios.append(sharpe_ratio)
        trade_counts.append(trade_count)

    # Create scatter plot
    fig = go.Figure()

    # Add scatter trace
    fig.add_trace(go.Scatter(
        x=risks,
        y=returns,
        mode='markers',
        marker=dict(
            size=[count / 2 + 10 for count in trade_counts],  # Size based on trade count
            color=sharpe_ratios,  # Color based on Sharpe ratio
            colorscale='Viridis',
            colorbar=dict(title="Sharpe Ratio"),
            showscale=True
        ),
        text=strategy_names,
        hovertemplate='<b>%{text}</b><br>Return: %{y:.2f}<br>Risk: %{x:.2f}<br>Sharpe: %{marker.color:.2f}<br>Trades: %{marker.size:.0f}<extra></extra>'
    ))

    # Add reference lines
    fig.add_shape(
        type="line",
        x0=0, y0=0,
        x1=max(risks) * 1.1, y1=0,
        line=dict(color="gray", width=1, dash="dash")
    )

    fig.add_shape(
        type="line",
        x0=0, y0=0,
        x1=0, y1=max(returns) * 1.1,
        line=dict(color="gray", width=1, dash="dash")
    )

    # Update layout
    fig.update_layout(
        title="Risk-Return Analysis by Strategy",
        xaxis_title="Risk (Max Drawdown)",
        yaxis_title="Return (Composite Score)",
        height=500,
        hovermode="closest"
    )

    return fig

def process_strategy_data(data: Dict[str, Any]) -> Tuple[Dict[str, Any], Dict[str, Any]]:
    """
    Process strategy data for attribution analysis.

    Args:
        data: Dashboard data

    Returns:
        Tuple containing processed strategy data and metrics
    """
    # Default metrics
    strategy_metrics = {
        'best_strategy_name': 'N/A',
        'best_strategy_score': 0.0,
        'most_active_strategy_name': 'N/A',
        'most_active_strategy_trades': 0,
        'highest_winrate_strategy_name': 'N/A',
        'highest_winrate_strategy_rate': 0.0
    }

    # Check if strategy metrics exist
    if 'strategy_metrics' not in data or 'strategies' not in data['strategy_metrics']:
        return {}, strategy_metrics

    strategies = data['strategy_metrics']['strategies']

    # Process strategy data
    strategy_data = {}
    best_score = 0.0
    most_trades = 0
    highest_winrate = 0.0

    for strategy_id, strategy_info in strategies.items():
        # Skip if no trades
        if 'trades' not in strategy_info or not strategy_info['trades']:
            continue

        # Calculate strategy metrics
        trades = strategy_info['trades']
        total_pnl = sum(trade.get('profit_loss', 0) for trade in trades)
        win_count = sum(1 for trade in trades if trade.get('profit_loss', 0) > 0)
        win_rate = win_count / len(trades) if trades else 0

        # Store strategy data
        strategy_data[strategy_id] = {
            'name': strategy_info.get('name', strategy_id),
            'total_pnl': total_pnl,
            'trade_count': len(trades),
            'win_rate': win_rate,
            'composite_score': strategy_info.get('composite_score', 0.0),
            'trades': trades
        }

        # Update best strategy
        if strategy_info.get('composite_score', 0.0) > best_score:
            best_score = strategy_info.get('composite_score', 0.0)
            strategy_metrics['best_strategy_name'] = strategy_info.get('name', strategy_id)
            strategy_metrics['best_strategy_score'] = best_score

        # Update most active strategy
        if len(trades) > most_trades:
            most_trades = len(trades)
            strategy_metrics['most_active_strategy_name'] = strategy_info.get('name', strategy_id)
            strategy_metrics['most_active_strategy_trades'] = most_trades

        # Update highest winrate strategy
        if win_rate > highest_winrate:
            highest_winrate = win_rate
            strategy_metrics['highest_winrate_strategy_name'] = strategy_info.get('name', strategy_id)
            strategy_metrics['highest_winrate_strategy_rate'] = highest_winrate

    return strategy_data, strategy_metrics

def process_transaction_data(data: Dict[str, Any]) -> Tuple[pd.DataFrame, Dict[str, Any]]:
    """
    Process transaction data for PnL analysis.

    Args:
        data: Dashboard data

    Returns:
        Tuple containing processed transaction DataFrame and PnL metrics
    """
    # Default metrics
    pnl_metrics = {
        'total_pnl_sol': 0.0,
        'total_pnl_usd': 0.0,
        'daily_pnl_sol': 0.0,
        'daily_pnl_usd': 0.0,
        'roi': 0.0,
        'daily_roi': 0.0,
        'win_rate': 0.0,
        'sol_price_usd': 25.0  # Default SOL price
    }

    # Check if transaction history exists
    if 'tx_history' not in data or 'transactions' not in data['tx_history'] or not data['tx_history']['transactions']:
        return pd.DataFrame(), pnl_metrics

    # Convert to DataFrame
    tx_df = pd.DataFrame(data['tx_history']['transactions'])

    # Convert timestamp to datetime
    tx_df['datetime'] = pd.to_datetime(tx_df['timestamp'], unit='s')

    # Sort by datetime
    tx_df = tx_df.sort_values('datetime')

    # Calculate PnL in SOL and USD
    buy_df = tx_df[tx_df['type'] == 'buy']
    sell_df = tx_df[tx_df['type'] == 'sell']

    # Calculate total PnL
    total_buy_sol = buy_df['amount'].sum()
    total_sell_sol = sell_df['amount'].sum()
    total_buy_value = (buy_df['amount'] * buy_df['price']).sum()
    total_sell_value = (sell_df['amount'] * sell_df['price']).sum()

    # Calculate PnL
    pnl_sol = total_sell_value - total_buy_value

    # Get SOL price (use the most recent transaction price as an approximation)
    sol_price_usd = 25.0  # Default
    sol_txs = tx_df[tx_df['token'] == 'SOL']
    if not sol_txs.empty:
        sol_price_usd = sol_txs.iloc[-1]['price']

    # Calculate PnL in USD
    pnl_usd = pnl_sol * sol_price_usd

    # Calculate daily PnL
    one_day_ago = datetime.now() - timedelta(days=1)
    recent_tx_df = tx_df[tx_df['datetime'] > one_day_ago]

    daily_buy_df = recent_tx_df[recent_tx_df['type'] == 'buy']
    daily_sell_df = recent_tx_df[recent_tx_df['type'] == 'sell']

    daily_buy_value = (daily_buy_df['amount'] * daily_buy_df['price']).sum()
    daily_sell_value = (daily_sell_df['amount'] * daily_sell_df['price']).sum()

    daily_pnl_sol = daily_sell_value - daily_buy_value
    daily_pnl_usd = daily_pnl_sol * sol_price_usd

    # Calculate ROI
    initial_investment = total_buy_value if total_buy_value > 0 else 1.0
    roi = pnl_sol / initial_investment

    daily_investment = daily_buy_value if daily_buy_value > 0 else 1.0
    daily_roi = daily_pnl_sol / daily_investment

    # Calculate win rate
    if 'strategy_metrics' in data and 'strategies' in data['strategy_metrics']:
        strategies = data['strategy_metrics']['strategies']
        total_trades = 0
        winning_trades = 0

        for strategy_id, strategy_data in strategies.items():
            if 'trades' in strategy_data:
                for trade in strategy_data['trades']:
                    total_trades += 1
                    if trade.get('profit_loss', 0) > 0:
                        winning_trades += 1

        win_rate = winning_trades / total_trades if total_trades > 0 else 0.0
    else:
        win_rate = 0.0

    # Update metrics
    pnl_metrics.update({
        'total_pnl_sol': pnl_sol,
        'total_pnl_usd': pnl_usd,
        'daily_pnl_sol': daily_pnl_sol,
        'daily_pnl_usd': daily_pnl_usd,
        'roi': roi,
        'daily_roi': daily_roi,
        'win_rate': win_rate,
        'sol_price_usd': sol_price_usd
    })

    return tx_df, pnl_metrics

def process_risk_metrics(data: Dict[str, Any]) -> Tuple[pd.DataFrame, Dict[str, Any]]:
    """
    Process transaction data for risk metrics analysis.

    Args:
        data: Dashboard data

    Returns:
        Tuple containing processed transaction DataFrame and risk metrics
    """
    # Default risk metrics
    risk_metrics = {
        'sharpe_ratio': 0.0,
        'sortino_ratio': 0.0,
        'calmar_ratio': 0.0,
        'volatility': 0.0,
        'max_drawdown': 0.0,
        'value_at_risk': 0.0,
        'expected_shortfall': 0.0,
        'risk_free_rate': 0.02  # 2% annual risk-free rate
    }

    # Get transaction data
    tx_df, _ = process_transaction_data(data)
    if tx_df.empty:
        return pd.DataFrame(), risk_metrics

    # Calculate daily returns
    tx_df['pnl'] = tx_df.apply(
        lambda row: row['amount'] * row['price'] * (-1 if row['type'] == 'buy' else 1),
        axis=1
    )
    tx_df['date'] = tx_df['datetime'].dt.date
    daily_pnl = tx_df.groupby('date')['pnl'].sum().reset_index()

    # Calculate cumulative PnL
    daily_pnl['cumulative_pnl'] = daily_pnl['pnl'].cumsum()

    # Calculate daily returns
    daily_pnl['daily_return'] = daily_pnl['pnl'].pct_change().fillna(0)

    # Calculate volatility (annualized)
    volatility = daily_pnl['daily_return'].std() * np.sqrt(252)

    # Calculate Sharpe ratio (annualized)
    excess_returns = daily_pnl['daily_return'] - risk_metrics['risk_free_rate'] / 252
    sharpe_ratio = excess_returns.mean() / excess_returns.std() * np.sqrt(252) if excess_returns.std() > 0 else 0

    # Calculate Sortino ratio (annualized)
    downside_returns = daily_pnl['daily_return'][daily_pnl['daily_return'] < 0]
    downside_deviation = downside_returns.std() * np.sqrt(252)
    sortino_ratio = excess_returns.mean() / downside_deviation * np.sqrt(252) if downside_deviation > 0 else 0

    # Calculate maximum drawdown
    daily_pnl['peak'] = daily_pnl['cumulative_pnl'].cummax()
    daily_pnl['drawdown'] = (daily_pnl['cumulative_pnl'] - daily_pnl['peak']) / daily_pnl['peak']
    max_drawdown = daily_pnl['drawdown'].min() * -1 if len(daily_pnl) > 0 else 0

    # Calculate Calmar ratio (annualized return / maximum drawdown)
    annual_return = daily_pnl['daily_return'].mean() * 252
    calmar_ratio = annual_return / max_drawdown if max_drawdown > 0 else 0

    # Calculate Value at Risk (VaR) at 95% confidence level
    value_at_risk = np.percentile(daily_pnl['pnl'], 5) * -1 if len(daily_pnl) > 0 else 0

    # Calculate Expected Shortfall (ES) at 95% confidence level
    expected_shortfall = daily_pnl['pnl'][daily_pnl['pnl'] <= -value_at_risk].mean() * -1 if value_at_risk > 0 else 0

    # Update risk metrics
    risk_metrics.update({
        'sharpe_ratio': sharpe_ratio,
        'sortino_ratio': sortino_ratio,
        'calmar_ratio': calmar_ratio,
        'volatility': volatility,
        'max_drawdown': max_drawdown,
        'value_at_risk': value_at_risk,
        'expected_shortfall': expected_shortfall
    })

    return tx_df, risk_metrics

def process_drawdown_data(data: Dict[str, Any]) -> Tuple[pd.DataFrame, Dict[str, Any]]:
    """
    Process transaction data for drawdown analysis.

    Args:
        data: Dashboard data

    Returns:
        Tuple containing processed transaction DataFrame and drawdown metrics
    """
    # Default drawdown metrics
    drawdown_metrics = {
        'max_drawdown': 0.0,
        'avg_drawdown': 0.0,
        'max_drawdown_duration': 0,
        'avg_recovery_time': 0.0,
        'drawdown_periods': [],
        'current_drawdown': 0.0
    }

    # Get transaction data
    tx_df, _ = process_transaction_data(data)
    if tx_df.empty:
        return pd.DataFrame(), drawdown_metrics

    # Calculate daily PnL
    tx_df['pnl'] = tx_df.apply(
        lambda row: row['amount'] * row['price'] * (-1 if row['type'] == 'buy' else 1),
        axis=1
    )
    tx_df['date'] = tx_df['datetime'].dt.date
    daily_pnl = tx_df.groupby('date')['pnl'].sum().reset_index()

    # Calculate cumulative PnL
    daily_pnl['cumulative_pnl'] = daily_pnl['pnl'].cumsum()

    # Calculate drawdown
    daily_pnl['peak'] = daily_pnl['cumulative_pnl'].cummax()
    daily_pnl['drawdown'] = (daily_pnl['cumulative_pnl'] - daily_pnl['peak']) / daily_pnl['peak']
    daily_pnl['drawdown_pct'] = daily_pnl['drawdown'] * 100

    # Calculate maximum drawdown
    max_drawdown = daily_pnl['drawdown'].min() * -1 if len(daily_pnl) > 0 else 0

    # Calculate average drawdown (only consider negative drawdowns)
    negative_drawdowns = daily_pnl[daily_pnl['drawdown'] < 0]['drawdown']
    avg_drawdown = negative_drawdowns.mean() * -1 if len(negative_drawdowns) > 0 else 0

    # Calculate current drawdown
    current_drawdown = daily_pnl['drawdown'].iloc[-1] * -1 if len(daily_pnl) > 0 else 0

    # Identify drawdown periods
    in_drawdown = False
    drawdown_start = None
    drawdown_periods = []

    for i, row in daily_pnl.iterrows():
        if not in_drawdown and row['drawdown'] < 0:
            # Start of a drawdown period
            in_drawdown = True
            drawdown_start = row['date']
        elif in_drawdown and row['drawdown'] == 0:
            # End of a drawdown period
            in_drawdown = False
            drawdown_periods.append({
                'start_date': drawdown_start,
                'end_date': row['date'],
                'duration': (row['date'] - drawdown_start).days,
                'max_drawdown': daily_pnl[(daily_pnl['date'] >= drawdown_start) &
                                         (daily_pnl['date'] <= row['date'])]['drawdown'].min() * -1
            })

    # If still in drawdown at the end of the data
    if in_drawdown:
        drawdown_periods.append({
            'start_date': drawdown_start,
            'end_date': daily_pnl['date'].iloc[-1],
            'duration': (daily_pnl['date'].iloc[-1] - drawdown_start).days,
            'max_drawdown': daily_pnl[daily_pnl['date'] >= drawdown_start]['drawdown'].min() * -1,
            'ongoing': True
        })

    # Calculate maximum drawdown duration
    max_drawdown_duration = max([period['duration'] for period in drawdown_periods]) if drawdown_periods else 0

    # Calculate average recovery time (excluding ongoing drawdowns)
    completed_periods = [period for period in drawdown_periods if 'ongoing' not in period or not period['ongoing']]
    avg_recovery_time = sum([period['duration'] for period in completed_periods]) / len(completed_periods) if completed_periods else 0

    # Update drawdown metrics
    drawdown_metrics.update({
        'max_drawdown': max_drawdown,
        'avg_drawdown': avg_drawdown,
        'max_drawdown_duration': max_drawdown_duration,
        'avg_recovery_time': avg_recovery_time,
        'drawdown_periods': drawdown_periods,
        'current_drawdown': current_drawdown
    })

    return daily_pnl, drawdown_metrics
