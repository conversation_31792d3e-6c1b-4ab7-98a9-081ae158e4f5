#!/usr/bin/env python3
"""
System Metrics Component

This module provides components for displaying system metrics in the Synergy7 Unified Dashboard.
"""

import pandas as pd
import streamlit as st
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

def render_system_metrics(data: Dict[str, Any]):
    """
    Render system metrics components.

    Args:
        data: Dashboard data
    """
    st.header("System Monitoring")

    # Create tabs for different system metrics
    tab1, tab2, tab3, tab4 = st.tabs([
        "API Performance",
        "System Resources",
        "Stream Data",
        "Wallet Balance"
    ])

    with tab1:
        render_api_performance(data)

    with tab2:
        render_system_resources(data)

    with tab3:
        render_stream_data(data)
        
    with tab4:
        render_wallet_balance(data)

def render_api_performance(data: Dict[str, Any]):
    """
    Render API performance metrics.

    Args:
        data: Dashboard data
    """
    st.subheader("API Performance")

    # Get system metrics
    system_metrics = data.get('system_metrics', {})
    
    # Check if API metrics exist
    if 'api_requests' in system_metrics:
        api_requests = system_metrics['api_requests']
        
        # Create DataFrame for API requests
        request_data = []
        for key, value in api_requests.items():
            try:
                # Parse the key as JSON to get labels
                import json
                labels = json.loads(key)
                
                row = labels.copy()
                row['value'] = value
                request_data.append(row)
            except:
                # For metrics without labels
                if not isinstance(value, dict):
                    request_data.append({'api': key, 'value': value})
        
        if request_data:
            api_df = pd.DataFrame(request_data)
            
            # Group by API and status
            if 'api' in api_df.columns and 'status' in api_df.columns:
                api_summary = api_df.groupby(["api", "status"])["value"].sum().reset_index()
                
                # Create a bar chart
                fig = px.bar(
                    api_summary,
                    x="api",
                    y="value",
                    color="status",
                    title="API Requests by Status",
                    labels={"api": "API", "value": "Count", "status": "Status"},
                )
                st.plotly_chart(fig, use_container_width=True)
    
    # Check if API latency metrics exist
    if 'api_response_time' in system_metrics:
        api_response_time = system_metrics['api_response_time']
        
        # Create DataFrame for API response time
        response_data = []
        for key, value in api_response_time.items():
            try:
                # Parse the key as JSON to get labels
                import json
                labels = json.loads(key)
                
                # For histogram metrics
                if isinstance(value, dict) and 'count' in value and 'sum' in value:
                    row = labels.copy()
                    row['count'] = value['count']
                    row['sum'] = value['sum']
                    row['avg'] = value['sum'] / value['count'] if value['count'] > 0 else 0
                    response_data.append(row)
            except:
                # For metrics without labels
                pass
        
        if response_data:
            api_latency_df = pd.DataFrame(response_data)
            
            if 'api' in api_latency_df.columns and 'avg' in api_latency_df.columns:
                # Create a bar chart
                fig = px.bar(
                    api_latency_df,
                    x="api",
                    y="avg",
                    title="Average API Latency",
                    labels={"api": "API", "avg": "Average Latency (s)"},
                )
                st.plotly_chart(fig, use_container_width=True)
    
    # If no API metrics found
    if 'api_requests' not in system_metrics and 'api_response_time' not in system_metrics:
        st.info("No API metrics found")
        
        # Display placeholder data
        st.subheader("API Request Counts (Placeholder)")
        
        # Create placeholder data
        api_data = {
            'API': ['Helius', 'Birdeye', 'Jupiter', 'Jito', 'QuickNode'],
            'Success': [95, 87, 92, 78, 90],
            'Failure': [5, 13, 8, 22, 10]
        }
        
        # Create DataFrame
        api_df = pd.DataFrame({
            'API': api_data['API'] * 2,
            'Status': ['Success'] * 5 + ['Failure'] * 5,
            'Count': api_data['Success'] + api_data['Failure']
        })
        
        # Create bar chart
        fig = px.bar(
            api_df,
            x='API',
            y='Count',
            color='Status',
            title='API Requests by Status',
            labels={'API': 'API', 'Count': 'Count', 'Status': 'Status'}
        )
        st.plotly_chart(fig, use_container_width=True)
        
        # Display placeholder latency data
        st.subheader("API Latency (Placeholder)")
        
        # Create placeholder data
        latency_data = {
            'API': ['Helius', 'Birdeye', 'Jupiter', 'Jito', 'QuickNode'],
            'Latency': [0.25, 0.42, 0.18, 0.31, 0.22]
        }
        
        # Create DataFrame
        latency_df = pd.DataFrame(latency_data)
        
        # Create bar chart
        fig = px.bar(
            latency_df,
            x='API',
            y='Latency',
            title='Average API Latency',
            labels={'API': 'API', 'Latency': 'Average Latency (s)'}
        )
        st.plotly_chart(fig, use_container_width=True)

def render_system_resources(data: Dict[str, Any]):
    """
    Render system resources metrics.

    Args:
        data: Dashboard data
    """
    st.subheader("System Resources")

    # Get system metrics
    system_metrics = data.get('system_metrics', {})
    
    # Create columns for metrics
    col1, col2, col3 = st.columns(3)
    
    # CPU usage
    cpu_usage = system_metrics.get('system_cpu_usage', 25.5)  # Default placeholder value
    with col1:
        st.metric(
            "CPU Usage",
            f"{cpu_usage:.2f}%",
            delta=None,
            delta_color="normal",
        )
    
    # Memory usage
    memory_usage = system_metrics.get('system_memory_usage', 2.5 * 1024 * 1024 * 1024)  # Default placeholder value
    memory_usage_gb = memory_usage / (1024 * 1024 * 1024)  # Convert to GB
    with col2:
        st.metric(
            "Memory Usage",
            f"{memory_usage_gb:.2f} GB",
            delta=None,
            delta_color="normal",
        )
    
    # Disk usage
    disk_usage = system_metrics.get('system_disk_usage', 25.0 * 1024 * 1024 * 1024)  # Default placeholder value
    disk_usage_gb = disk_usage / (1024 * 1024 * 1024)  # Convert to GB
    with col3:
        st.metric(
            "Disk Usage",
            f"{disk_usage_gb:.2f} GB",
            delta=None,
            delta_color="normal",
        )
    
    # Create gauge charts
    col1, col2, col3 = st.columns(3)
    
    with col1:
        # CPU gauge
        fig = go.Figure(go.Indicator(
            mode="gauge+number",
            value=cpu_usage,
            domain={"x": [0, 1], "y": [0, 1]},
            title={"text": "CPU Usage"},
            gauge={
                "axis": {"range": [0, 100]},
                "bar": {"color": "darkblue"},
                "steps": [
                    {"range": [0, 50], "color": "lightgreen"},
                    {"range": [50, 80], "color": "yellow"},
                    {"range": [80, 100], "color": "red"},
                ],
                "threshold": {
                    "line": {"color": "red", "width": 4},
                    "thickness": 0.75,
                    "value": 90,
                },
            },
        ))
        st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        # Memory gauge
        fig = go.Figure(go.Indicator(
            mode="gauge+number",
            value=memory_usage_gb,
            domain={"x": [0, 1], "y": [0, 1]},
            title={"text": "Memory Usage (GB)"},
            gauge={
                "axis": {"range": [0, 16]},  # Assuming 16GB total memory
                "bar": {"color": "darkblue"},
                "steps": [
                    {"range": [0, 8], "color": "lightgreen"},
                    {"range": [8, 12], "color": "yellow"},
                    {"range": [12, 16], "color": "red"},
                ],
                "threshold": {
                    "line": {"color": "red", "width": 4},
                    "thickness": 0.75,
                    "value": 14,
                },
            },
        ))
        st.plotly_chart(fig, use_container_width=True)
    
    with col3:
        # Disk gauge
        fig = go.Figure(go.Indicator(
            mode="gauge+number",
            value=disk_usage_gb,
            domain={"x": [0, 1], "y": [0, 1]},
            title={"text": "Disk Usage (GB)"},
            gauge={
                "axis": {"range": [0, 100]},  # Assuming 100GB total disk
                "bar": {"color": "darkblue"},
                "steps": [
                    {"range": [0, 50], "color": "lightgreen"},
                    {"range": [50, 80], "color": "yellow"},
                    {"range": [80, 100], "color": "red"},
                ],
                "threshold": {
                    "line": {"color": "red", "width": 4},
                    "thickness": 0.75,
                    "value": 90,
                },
            },
        ))
        st.plotly_chart(fig, use_container_width=True)

def render_stream_data(data: Dict[str, Any]):
    """
    Render stream data metrics.

    Args:
        data: Dashboard data
    """
    st.subheader("Stream Data")

    # Get system metrics
    system_metrics = data.get('system_metrics', {})
    
    # Check if stream data metrics exist
    if 'stream_data_messages' in system_metrics:
        stream_data_messages = system_metrics['stream_data_messages']
        
        # Create DataFrame for stream data messages
        stream_data = []
        for key, value in stream_data_messages.items():
            try:
                # Parse the key as JSON to get labels
                import json
                labels = json.loads(key)
                
                row = labels.copy()
                row['value'] = value
                stream_data.append(row)
            except:
                # For metrics without labels
                if not isinstance(value, dict):
                    stream_data.append({'source': key, 'value': value})
        
        if stream_data:
            stream_data_df = pd.DataFrame(stream_data)
            
            # Group by source and status
            if 'source' in stream_data_df.columns and 'status' in stream_data_df.columns:
                stream_data_summary = stream_data_df.groupby(["source", "status"])["value"].sum().reset_index()
                
                # Create a bar chart
                fig = px.bar(
                    stream_data_summary,
                    x="source",
                    y="value",
                    color="status",
                    title="Stream Data Messages by Source and Status",
                    labels={"source": "Source", "value": "Count", "status": "Status"},
                )
                st.plotly_chart(fig, use_container_width=True)
    
    # Check if Lil' Jito metrics exist
    if 'liljito_bundles' in system_metrics:
        liljito_bundles = system_metrics['liljito_bundles']
        
        # Create DataFrame for Lil' Jito bundles
        bundle_data = []
        for key, value in liljito_bundles.items():
            try:
                # Parse the key as JSON to get labels
                import json
                labels = json.loads(key)
                
                row = labels.copy()
                row['value'] = value
                bundle_data.append(row)
            except:
                # For metrics without labels
                if not isinstance(value, dict):
                    bundle_data.append({'status': key, 'value': value})
        
        if bundle_data:
            liljito_bundle_df = pd.DataFrame(bundle_data)
            
            # Group by status
            if 'status' in liljito_bundle_df.columns:
                liljito_bundle_summary = liljito_bundle_df.groupby(["status"])["value"].sum().reset_index()
                
                # Create a pie chart
                fig = px.pie(
                    liljito_bundle_summary,
                    values="value",
                    names="status",
                    title="Lil' Jito Bundles by Status",
                )
                st.plotly_chart(fig, use_container_width=True)
    
    # If no stream data metrics found
    if 'stream_data_messages' not in system_metrics and 'liljito_bundles' not in system_metrics:
        st.info("No stream data metrics found")
        
        # Display placeholder data
        st.subheader("Stream Data Messages (Placeholder)")
        
        # Create placeholder data
        stream_data = {
            'Source': ['Helius', 'Jito', 'Lil Jito', 'Birdeye', 'PumpFun'],
            'Success': [120, 85, 95, 75, 45],
            'Error': [5, 10, 8, 12, 7]
        }
        
        # Create DataFrame
        stream_df = pd.DataFrame({
            'Source': stream_data['Source'] * 2,
            'Status': ['Success'] * 5 + ['Error'] * 5,
            'Count': stream_data['Success'] + stream_data['Error']
        })
        
        # Create bar chart
        fig = px.bar(
            stream_df,
            x='Source',
            y='Count',
            color='Status',
            title='Stream Data Messages by Source and Status',
            labels={'Source': 'Source', 'Count': 'Count', 'Status': 'Status'}
        )
        st.plotly_chart(fig, use_container_width=True)
        
        # Display placeholder Lil' Jito bundle data
        st.subheader("Lil' Jito Bundles (Placeholder)")
        
        # Create placeholder data
        bundle_data = {
            'Status': ['Confirmed', 'Pending', 'Failed', 'Timeout'],
            'Count': [75, 15, 8, 2]
        }
        
        # Create DataFrame
        bundle_df = pd.DataFrame(bundle_data)
        
        # Create pie chart
        fig = px.pie(
            bundle_df,
            values='Count',
            names='Status',
            title="Lil' Jito Bundles by Status"
        )
        st.plotly_chart(fig, use_container_width=True)

def render_wallet_balance(data: Dict[str, Any]):
    """
    Render wallet balance metrics.

    Args:
        data: Dashboard data
    """
    st.subheader("Wallet Balance")

    # Get system metrics
    system_metrics = data.get('system_metrics', {})
    
    # Check if wallet balance metrics exist
    if 'wallet_balance' in system_metrics:
        wallet_balance = system_metrics['wallet_balance']
        
        # Create DataFrame for wallet balance
        balance_data = []
        for key, value in wallet_balance.items():
            try:
                # Parse the key as JSON to get labels
                import json
                labels = json.loads(key)
                
                row = labels.copy()
                row['value'] = value
                balance_data.append(row)
            except:
                # For metrics without labels
                if not isinstance(value, dict):
                    balance_data.append({'wallet': key, 'value': value})
        
        if balance_data:
            wallet_df = pd.DataFrame(balance_data)
            
            # Display wallet balances
            if 'wallet' in wallet_df.columns and 'value' in wallet_df.columns:
                # Create columns for metrics
                col1, col2 = st.columns(2)
                
                # SOL balance (assuming all balances are in SOL)
                sol_balance = wallet_df["value"].sum()
                col1.metric(
                    "Total SOL Balance",
                    f"{sol_balance:.4f} SOL",
                    delta=None,
                    delta_color="normal",
                )
                
                # SOL price (placeholder)
                sol_price_usd = 25.10  # This would be fetched from an API in production
                col2.metric(
                    "Total USD Value",
                    f"${sol_balance * sol_price_usd:.2f}",
                    delta=None,
                    delta_color="normal",
                )
                
                # Wallet balance chart
                fig = px.bar(
                    wallet_df,
                    x="wallet",
                    y="value",
                    title="Wallet Balances",
                    labels={"wallet": "Wallet", "value": "Balance (SOL)"},
                )
                st.plotly_chart(fig, use_container_width=True)
    
    # If no wallet balance metrics found
    if 'wallet_balance' not in system_metrics:
        st.info("No wallet balance metrics found")
        
        # Display placeholder data
        st.subheader("Wallet Balances (Placeholder)")
        
        # Create placeholder data
        wallet_data = {
            'Wallet': ['Trading Wallet', 'Reserve Wallet', 'Fee Wallet', 'Cold Storage'],
            'Balance': [12.5, 25.0, 1.5, 50.0]
        }
        
        # Create DataFrame
        wallet_df = pd.DataFrame(wallet_data)
        
        # Create columns for metrics
        col1, col2 = st.columns(2)
        
        # SOL balance
        sol_balance = sum(wallet_data['Balance'])
        col1.metric(
            "Total SOL Balance",
            f"{sol_balance:.4f} SOL",
            delta=None,
            delta_color="normal",
        )
        
        # SOL price (placeholder)
        sol_price_usd = 25.10  # This would be fetched from an API in production
        col2.metric(
            "Total USD Value",
            f"${sol_balance * sol_price_usd:.2f}",
            delta=None,
            delta_color="normal",
        )
        
        # Wallet balance chart
        fig = px.bar(
            wallet_df,
            x="Wallet",
            y="Balance",
            title="Wallet Balances",
            labels={"Wallet": "Wallet", "Balance": "Balance (SOL)"},
        )
        st.plotly_chart(fig, use_container_width=True)
