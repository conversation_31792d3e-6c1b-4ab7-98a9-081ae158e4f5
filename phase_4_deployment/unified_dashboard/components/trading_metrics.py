#!/usr/bin/env python3
"""
Trading Metrics Component

This module provides components for displaying trading metrics in the Synergy7 Unified Dashboard.
"""

import pandas as pd
import streamlit as st
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

def render_trading_metrics(data: Dict[str, Any]):
    """
    Render trading metrics components.

    Args:
        data: Dashboard data
    """
    st.header("Trading Metrics")

    # Create tabs for different trading metrics
    tab1, tab2, tab3 = st.tabs([
        "PnL Metrics",
        "Strategy Performance",
        "Transaction History"
    ])

    with tab1:
        render_pnl_metrics(data)

    with tab2:
        render_strategy_performance(data)

    with tab3:
        render_transaction_history(data)

def render_pnl_metrics(data: Dict[str, Any]):
    """
    Render profit and loss metrics.

    Args:
        data: Dashboard data
    """
    st.subheader("Profit and Loss Metrics")

    # Create tabs for different profit metrics
    profit_tabs = st.tabs(["Net Profit", "Profit Factor", "Return on Capital", "Drawdown", "Sharpe Ratio"])

    with profit_tabs[0]:  # Net Profit
        st.subheader("Net Profit")

        # Create columns for metrics
        cols = st.columns(3)

        # Get SOL price (placeholder)
        sol_price_usd = 25.10  # This would be fetched from an API in production

        # Get profit data (placeholder)
        net_profit_sol = 125.42
        daily_profit_sol = 12.54
        profit_per_million = 1254

        with cols[0]:
            st.metric(
                "Net Profit (SOL)",
                f"{net_profit_sol:.2f}",
                delta="15.23",
                delta_color="normal"
            )

        with cols[1]:
            st.metric(
                "Profit per $1M Traded",
                f"${profit_per_million:,.0f}",
                delta="$152",
                delta_color="normal"
            )

        with cols[2]:
            st.metric(
                "Daily Profit (SOL)",
                f"{daily_profit_sol:.2f}",
                delta="1.52",
                delta_color="normal"
            )

        # Add USD metrics
        st.markdown(f"### USD Values (SOL Price: ${sol_price_usd:.2f})")
        usd_cols = st.columns(3)

        with usd_cols[0]:
            st.metric(
                "Net Profit (USD)",
                f"${net_profit_sol * sol_price_usd:,.2f}",
                delta=f"${15.23 * sol_price_usd:.2f}",
                delta_color="normal"
            )

        with usd_cols[1]:
            st.metric(
                "Profit per $1M Traded (USD)",
                f"${profit_per_million:,.0f}",
                delta="$152",
                delta_color="normal"
            )

        with usd_cols[2]:
            st.metric(
                "Daily Profit (USD)",
                f"${daily_profit_sol * sol_price_usd:,.2f}",
                delta=f"${1.52 * sol_price_usd:.2f}",
                delta_color="normal"
            )

        # Create a placeholder for the chart
        chart_placeholder = st.empty()

        # Create a dummy chart for net profit
        fig = go.Figure()
        fig.add_trace(go.Scatter(
            x=[datetime.now() - timedelta(days=i) for i in range(30, 0, -1)],
            y=pd.Series(pd.Series(range(30)).cumsum() * 4.2 + 100).values,
            mode="lines",
            name="Cumulative Profit (SOL)"
        ))
        
        # Add USD line on secondary y-axis
        fig.add_trace(go.Scatter(
            x=[datetime.now() - timedelta(days=i) for i in range(30, 0, -1)],
            y=pd.Series(pd.Series(range(30)).cumsum() * 4.2 + 100).values * sol_price_usd,
            mode="lines",
            name="Cumulative Profit (USD)",
            yaxis="y2"
        ))
        
        fig.update_layout(
            title="Cumulative Profit (Last 30 Days)",
            xaxis_title="Date",
            yaxis_title="Profit (SOL)",
            yaxis2=dict(
                title="Profit (USD)",
                overlaying="y",
                side="right"
            ),
            height=400
        )

        chart_placeholder.plotly_chart(fig, use_container_width=True)

def render_strategy_performance(data: Dict[str, Any]):
    """
    Render strategy performance metrics.

    Args:
        data: Dashboard data
    """
    st.subheader("Strategy Performance")

    # Check if strategy profiles exist
    if 'strategy_profiles' in data and not data['strategy_profiles'].empty:
        # Sort by composite score (descending)
        top_strategies = data['strategy_profiles'].sort_values('composite_score', ascending=False).head(5)

        # Display top strategies
        st.dataframe(top_strategies[['strategy_id', 'sharpe_ratio', 'win_rate', 'composite_score']])

        # Plot strategy performance
        fig = px.bar(
            top_strategies,
            x='strategy_id',
            y='composite_score',
            color='sharpe_ratio',
            title='Top Strategy Performance',
            labels={'composite_score': 'Composite Score', 'strategy_id': 'Strategy ID', 'sharpe_ratio': 'Sharpe Ratio'}
        )
        st.plotly_chart(fig, use_container_width=True)
        
        # Strategy details
        st.subheader("Strategy Details")
        
        # Select strategy to view
        selected_strategy = st.selectbox(
            "Select Strategy",
            top_strategies['strategy_id'].tolist()
        )
        
        # Display selected strategy details
        if selected_strategy:
            selected_row = top_strategies[top_strategies['strategy_id'] == selected_strategy].iloc[0]
            
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.metric("Sharpe Ratio", f"{selected_row['sharpe_ratio']:.2f}")
                st.metric("Win Rate", f"{selected_row['win_rate']:.2%}")
                
            with col2:
                st.metric("Composite Score", f"{selected_row['composite_score']:.2f}")
                st.metric("Max Drawdown", f"{selected_row.get('max_drawdown', 0):.2%}")
                
            with col3:
                st.metric("Profit Factor", f"{selected_row.get('profit_factor', 0):.2f}")
                st.metric("Avg Trade", f"{selected_row.get('avg_trade', 0):.4f}")
    else:
        st.info("No strategy profiles found")

def render_transaction_history(data: Dict[str, Any]):
    """
    Render transaction history.

    Args:
        data: Dashboard data
    """
    st.subheader("Transaction History")

    if 'tx_history' in data and 'transactions' in data['tx_history'] and data['tx_history']['transactions']:
        # Convert to DataFrame for easier display
        tx_df = pd.DataFrame(data['tx_history']['transactions'])

        # Convert timestamp to datetime
        tx_df['datetime'] = pd.to_datetime(tx_df['timestamp'], unit='s')

        # Sort by timestamp (descending)
        tx_df = tx_df.sort_values('datetime', ascending=False)

        # Display transactions
        st.dataframe(tx_df)

        # Transaction status summary
        st.subheader("Transaction Status Summary")

        # Count transactions by status
        status_counts = tx_df['status'].value_counts().reset_index()
        status_counts.columns = ['Status', 'Count']

        # Create pie chart
        fig = px.pie(
            status_counts,
            values='Count',
            names='Status',
            title='Transaction Status Distribution'
        )
        st.plotly_chart(fig, use_container_width=True)
    else:
        st.info("No transaction history found")
