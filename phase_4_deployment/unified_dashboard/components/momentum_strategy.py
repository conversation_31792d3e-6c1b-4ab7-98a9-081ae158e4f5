#!/usr/bin/env python3
"""
Momentum Strategy Dashboard Component

This module provides specialized visualizations and metrics for the momentum strategy
in the Synergy7 Unified Dashboard.
"""

import os
import sys
import json
import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta

import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots

# Add parent directory to path for imports
parent_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

# Configure logging
logger = logging.getLogger(__name__)

def render_momentum_strategy(data: Dict[str, Any]) -> None:
    """
    Render momentum strategy dashboard component.
    
    Args:
        data: Dashboard data
    """
    st.header("Momentum Strategy")
    
    # Create tabs for different views
    tabs = st.tabs([
        "Strategy Overview", 
        "Signal Analysis", 
        "Performance Metrics",
        "Parameter Optimization"
    ])
    
    with tabs[0]:  # Strategy Overview
        render_strategy_overview(data)
    
    with tabs[1]:  # Signal Analysis
        render_signal_analysis(data)
    
    with tabs[2]:  # Performance Metrics
        render_performance_metrics(data)
    
    with tabs[3]:  # Parameter Optimization
        render_parameter_optimization(data)

def render_strategy_overview(data: Dict[str, Any]) -> None:
    """
    Render momentum strategy overview.
    
    Args:
        data: Dashboard data
    """
    st.subheader("Momentum Strategy Overview")
    
    # Get strategy data
    strategy_data = get_momentum_strategy_data(data)
    
    if not strategy_data:
        st.info("No momentum strategy data available")
        return
    
    # Display strategy parameters
    st.markdown("### Current Parameters")
    
    params = strategy_data.get("parameters", {})
    
    # Create columns for parameters
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            "Window Size",
            params.get("window_size", 5)
        )
    
    with col2:
        st.metric(
            "Threshold",
            f"{params.get('threshold', 0.005):.3f}"
        )
    
    with col3:
        st.metric(
            "Smoothing Factor",
            f"{params.get('smoothing_factor', 0.1):.1f}"
        )
    
    with col4:
        st.metric(
            "Max Value",
            f"{params.get('max_value', 0.05):.2f}"
        )
    
    # Display strategy status
    st.markdown("### Strategy Status")
    
    status = strategy_data.get("status", {})
    
    # Create columns for status
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric(
            "Active Positions",
            status.get("active_positions", 0)
        )
    
    with col2:
        st.metric(
            "Current Signal",
            f"{status.get('current_signal', 0):.3f}",
            delta=f"{status.get('signal_change', 0):.3f}",
            delta_color="normal" if status.get("signal_change", 0) >= 0 else "inverse"
        )
    
    with col3:
        signal_strength = abs(status.get("current_signal", 0))
        threshold = params.get("threshold", 0.005)
        
        # Determine signal status
        if signal_strength < threshold * 0.5:
            signal_status = "Neutral"
            color = "gray"
        elif signal_strength < threshold:
            signal_status = "Weak"
            color = "blue"
        elif signal_strength < threshold * 2:
            signal_status = "Moderate"
            color = "orange"
        else:
            signal_status = "Strong"
            color = "green" if status.get("current_signal", 0) > 0 else "red"
        
        st.markdown(f"<h3 style='text-align: center; color: {color};'>{signal_status}</h3>", unsafe_allow_html=True)
    
    # Display recent trades
    st.markdown("### Recent Trades")
    
    trades = strategy_data.get("trades", [])
    
    if not trades:
        st.info("No recent trades available")
        return
    
    # Convert to DataFrame
    trades_df = pd.DataFrame(trades)
    
    # Sort by timestamp (descending)
    if "timestamp" in trades_df.columns:
        trades_df["datetime"] = pd.to_datetime(trades_df["timestamp"], unit="s")
        trades_df = trades_df.sort_values("datetime", ascending=False)
    
    # Display recent trades
    st.dataframe(
        trades_df[["datetime", "type", "market", "price", "amount", "profit_loss"]].head(5),
        use_container_width=True
    )
    
    # Create trade history chart
    if "datetime" in trades_df.columns and "profit_loss" in trades_df.columns:
        fig = go.Figure()
        
        # Add profit/loss bars
        fig.add_trace(
            go.Bar(
                x=trades_df["datetime"],
                y=trades_df["profit_loss"],
                marker_color=["green" if pl >= 0 else "red" for pl in trades_df["profit_loss"]],
                name="Profit/Loss"
            )
        )
        
        # Add cumulative profit line
        trades_df["cumulative_profit"] = trades_df["profit_loss"].cumsum()
        
        fig.add_trace(
            go.Scatter(
                x=trades_df["datetime"],
                y=trades_df["cumulative_profit"],
                mode="lines",
                name="Cumulative Profit",
                line=dict(color="blue", width=2)
            )
        )
        
        fig.update_layout(
            title="Trade History",
            xaxis_title="Date",
            yaxis_title="Profit/Loss",
            hovermode="x unified",
            legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1)
        )
        
        st.plotly_chart(fig, use_container_width=True)

def render_signal_analysis(data: Dict[str, Any]) -> None:
    """
    Render momentum signal analysis.
    
    Args:
        data: Dashboard data
    """
    st.subheader("Momentum Signal Analysis")
    
    # Get strategy data
    strategy_data = get_momentum_strategy_data(data)
    
    if not strategy_data:
        st.info("No momentum strategy data available")
        return
    
    # Get signal data
    signal_data = strategy_data.get("signal_data", [])
    
    if not signal_data:
        st.info("No signal data available")
        return
    
    # Convert to DataFrame
    signal_df = pd.DataFrame(signal_data)
    
    # Ensure datetime column
    if "timestamp" in signal_df.columns:
        signal_df["datetime"] = pd.to_datetime(signal_df["timestamp"], unit="s")
        signal_df = signal_df.sort_values("datetime")
    
    # Create signal chart
    if "datetime" in signal_df.columns and "signal" in signal_df.columns:
        # Get threshold
        threshold = strategy_data.get("parameters", {}).get("threshold", 0.005)
        
        # Create figure with price and signal
        fig = make_subplots(
            rows=2, cols=1,
            shared_xaxes=True,
            vertical_spacing=0.1,
            subplot_titles=("Price", "Momentum Signal"),
            row_heights=[0.7, 0.3]
        )
        
        # Add price trace
        if "price" in signal_df.columns:
            fig.add_trace(
                go.Scatter(
                    x=signal_df["datetime"],
                    y=signal_df["price"],
                    mode="lines",
                    name="Price",
                    line=dict(color="blue", width=2)
                ),
                row=1, col=1
            )
        
        # Add signal trace
        fig.add_trace(
            go.Scatter(
                x=signal_df["datetime"],
                y=signal_df["signal"],
                mode="lines",
                name="Momentum Signal",
                line=dict(color="purple", width=2)
            ),
            row=2, col=1
        )
        
        # Add threshold lines
        fig.add_shape(
            type="line",
            x0=signal_df["datetime"].min(),
            y0=threshold,
            x1=signal_df["datetime"].max(),
            y1=threshold,
            line=dict(color="green", width=1, dash="dash"),
            row=2, col=1
        )
        
        fig.add_shape(
            type="line",
            x0=signal_df["datetime"].min(),
            y0=-threshold,
            x1=signal_df["datetime"].max(),
            y1=-threshold,
            line=dict(color="red", width=1, dash="dash"),
            row=2, col=1
        )
        
        # Add buy/sell markers
        if "position" in signal_df.columns:
            # Find where position changes from 0 to 1 (buy) or 1 to 0 (sell)
            signal_df["position_change"] = signal_df["position"].diff()
            
            buys = signal_df[signal_df["position_change"] == 1]
            sells = signal_df[signal_df["position_change"] == -1]
            
            if not buys.empty and "price" in buys.columns:
                fig.add_trace(
                    go.Scatter(
                        x=buys["datetime"],
                        y=buys["price"],
                        mode="markers",
                        name="Buy Signal",
                        marker=dict(color="green", size=10, symbol="triangle-up")
                    ),
                    row=1, col=1
                )
            
            if not sells.empty and "price" in sells.columns:
                fig.add_trace(
                    go.Scatter(
                        x=sells["datetime"],
                        y=sells["price"],
                        mode="markers",
                        name="Sell Signal",
                        marker=dict(color="red", size=10, symbol="triangle-down")
                    ),
                    row=1, col=1
                )
        
        fig.update_layout(
            height=600,
            hovermode="x unified",
            legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1)
        )
        
        st.plotly_chart(fig, use_container_width=True)
        
        # Signal distribution
        st.subheader("Signal Distribution")
        
        # Create histogram
        fig = go.Figure()
        
        fig.add_trace(
            go.Histogram(
                x=signal_df["signal"],
                nbinsx=50,
                marker_color="blue",
                opacity=0.7
            )
        )
        
        # Add threshold lines
        fig.add_shape(
            type="line",
            x0=threshold,
            y0=0,
            x1=threshold,
            y1=1,
            yref="paper",
            line=dict(color="green", width=2, dash="dash")
        )
        
        fig.add_shape(
            type="line",
            x0=-threshold,
            y0=0,
            x1=-threshold,
            y1=1,
            yref="paper",
            line=dict(color="red", width=2, dash="dash")
        )
        
        fig.update_layout(
            title="Momentum Signal Distribution",
            xaxis_title="Signal Value",
            yaxis_title="Frequency",
            bargap=0.1
        )
        
        st.plotly_chart(fig, use_container_width=True)

def render_performance_metrics(data: Dict[str, Any]) -> None:
    """
    Render momentum strategy performance metrics.
    
    Args:
        data: Dashboard data
    """
    st.subheader("Performance Metrics")
    
    # Get strategy data
    strategy_data = get_momentum_strategy_data(data)
    
    if not strategy_data:
        st.info("No momentum strategy data available")
        return
    
    # Get performance metrics
    metrics = strategy_data.get("performance", {})
    
    if not metrics:
        st.info("No performance metrics available")
        return
    
    # Display key metrics
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            "Win Rate",
            f"{metrics.get('win_rate', 0):.2%}",
            delta=f"{metrics.get('win_rate', 0) - 0.5:.2%} vs 50%",
            delta_color="normal" if metrics.get("win_rate", 0) >= 0.5 else "inverse"
        )
    
    with col2:
        st.metric(
            "Profit Factor",
            f"{metrics.get('profit_factor', 0):.2f}",
            delta=f"{metrics.get('profit_factor', 0) - 1:.2f} vs 1.0",
            delta_color="normal" if metrics.get("profit_factor", 0) >= 1 else "inverse"
        )
    
    with col3:
        st.metric(
            "Sharpe Ratio",
            f"{metrics.get('sharpe_ratio', 0):.2f}",
            delta=f"{metrics.get('sharpe_ratio', 0) - 1:.2f} vs 1.0",
            delta_color="normal" if metrics.get("sharpe_ratio", 0) >= 1 else "inverse"
        )
    
    with col4:
        st.metric(
            "Max Drawdown",
            f"{metrics.get('max_drawdown', 0):.2%}",
            delta=f"{metrics.get('max_drawdown', 0) - 0.1:.2%} vs 10%",
            delta_color="inverse" if metrics.get("max_drawdown", 0) >= 0.1 else "normal"
        )
    
    # Display additional metrics
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            "Total Return",
            f"{metrics.get('total_return', 0):.2%}"
        )
    
    with col2:
        st.metric(
            "Avg Trade Return",
            f"{metrics.get('avg_trade_return', 0):.2%}"
        )
    
    with col3:
        st.metric(
            "Number of Trades",
            metrics.get("num_trades", 0)
        )
    
    with col4:
        st.metric(
            "Avg Trade Duration",
            f"{metrics.get('avg_trade_duration', 0):.1f} hrs"
        )
    
    # Display equity curve
    equity_data = strategy_data.get("equity_curve", [])
    
    if equity_data:
        # Convert to DataFrame
        equity_df = pd.DataFrame(equity_data)
        
        # Ensure datetime column
        if "timestamp" in equity_df.columns:
            equity_df["datetime"] = pd.to_datetime(equity_df["timestamp"], unit="s")
            equity_df = equity_df.sort_values("datetime")
        
        # Create equity curve chart
        if "datetime" in equity_df.columns and "equity" in equity_df.columns:
            fig = go.Figure()
            
            fig.add_trace(
                go.Scatter(
                    x=equity_df["datetime"],
                    y=equity_df["equity"],
                    mode="lines",
                    name="Equity",
                    line=dict(color="blue", width=2)
                )
            )
            
            # Add drawdown
            if "drawdown" in equity_df.columns:
                fig.add_trace(
                    go.Scatter(
                        x=equity_df["datetime"],
                        y=equity_df["drawdown"] * 100,  # Convert to percentage
                        mode="lines",
                        name="Drawdown (%)",
                        line=dict(color="red", width=1),
                        yaxis="y2"
                    )
                )
                
                fig.update_layout(
                    yaxis2=dict(
                        title="Drawdown (%)",
                        overlaying="y",
                        side="right",
                        range=[max(min(equity_df["drawdown"]) * 100 * 1.5, -50), 5]  # Limit y-axis range
                    )
                )
            
            fig.update_layout(
                title="Equity Curve",
                xaxis_title="Date",
                yaxis_title="Equity",
                hovermode="x unified",
                legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1)
            )
            
            st.plotly_chart(fig, use_container_width=True)

def render_parameter_optimization(data: Dict[str, Any]) -> None:
    """
    Render momentum strategy parameter optimization.
    
    Args:
        data: Dashboard data
    """
    st.subheader("Parameter Optimization")
    
    # Get strategy data
    strategy_data = get_momentum_strategy_data(data)
    
    if not strategy_data:
        st.info("No momentum strategy data available")
        return
    
    # Get optimization results
    optimization = strategy_data.get("optimization", {})
    
    if not optimization:
        st.info("No optimization results available")
        return
    
    # Display optimization results
    st.markdown("### Optimization Results")
    
    # Display best parameters
    best_params = optimization.get("best_params", {})
    
    if best_params:
        st.markdown("#### Best Parameters")
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric(
                "Window Size",
                best_params.get("window_size", 5)
            )
        
        with col2:
            st.metric(
                "Threshold",
                f"{best_params.get('threshold', 0.005):.3f}"
            )
        
        with col3:
            st.metric(
                "Smoothing Factor",
                f"{best_params.get('smoothing_factor', 0.1):.1f}"
            )
        
        with col4:
            st.metric(
                "Max Value",
                f"{best_params.get('max_value', 0.05):.2f}"
            )
    
    # Display parameter distribution
    param_results = optimization.get("all_results", [])
    
    if param_results:
        # Convert to DataFrame
        results_df = pd.DataFrame(param_results)
        
        # Create parameter distribution charts
        st.markdown("#### Parameter Distributions")
        
        col1, col2 = st.columns(2)
        
        with col1:
            # Window size vs. Sharpe ratio
            if "window_size" in results_df.columns and "sharpe_ratio" in results_df.columns:
                fig = px.scatter(
                    results_df,
                    x="window_size",
                    y="sharpe_ratio",
                    color="sharpe_ratio",
                    size="num_trades",
                    hover_data=["threshold", "smoothing_factor", "max_value", "win_rate"],
                    title="Window Size vs. Sharpe Ratio"
                )
                
                st.plotly_chart(fig, use_container_width=True)
        
        with col2:
            # Threshold vs. Sharpe ratio
            if "threshold" in results_df.columns and "sharpe_ratio" in results_df.columns:
                fig = px.scatter(
                    results_df,
                    x="threshold",
                    y="sharpe_ratio",
                    color="sharpe_ratio",
                    size="num_trades",
                    hover_data=["window_size", "smoothing_factor", "max_value", "win_rate"],
                    title="Threshold vs. Sharpe Ratio"
                )
                
                st.plotly_chart(fig, use_container_width=True)
        
        # Create 3D visualization
        if all(param in results_df.columns for param in ["window_size", "threshold", "sharpe_ratio"]):
            fig = px.scatter_3d(
                results_df,
                x="window_size",
                y="threshold",
                z="sharpe_ratio",
                color="sharpe_ratio",
                size="num_trades",
                hover_data=["smoothing_factor", "max_value", "win_rate"],
                title="Parameter Space Visualization"
            )
            
            fig.update_layout(height=600)
            
            st.plotly_chart(fig, use_container_width=True)

def get_momentum_strategy_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Extract momentum strategy data from dashboard data.
    
    Args:
        data: Dashboard data
        
    Returns:
        Momentum strategy data
    """
    # Check if strategy data exists
    if "strategy_data" not in data:
        return {}
    
    strategy_data = data["strategy_data"]
    
    # Find momentum strategy
    for strategy_id, strategy_info in strategy_data.items():
        if strategy_info.get("type", "").lower() == "momentum":
            return strategy_info
    
    # Check for specific momentum strategies
    for strategy_id, strategy_info in strategy_data.items():
        if "momentum" in strategy_id.lower():
            return strategy_info
    
    return {}
