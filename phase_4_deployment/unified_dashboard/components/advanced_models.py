#!/usr/bin/env python3
"""
Advanced Models Component

This module provides a component for displaying advanced trading models including
Market Microstructure, Statistical Signal Processing, and RL Execution metrics.
"""

import pandas as pd
import streamlit as st
import plotly.express as px
import plotly.graph_objects as go
import time
import threading
import json
from datetime import datetime
from typing import Dict, Any

# Import Carbon Core client for real-time updates
import sys
import os

# Add the parent directory to the Python path
parent_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

# Import data service
import sys
import os

# Add the parent directory to the Python path
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

from data_service import data_service
from core.carbon_core_fallback import (
    fallback_get_market_microstructure,
    fallback_get_statistical_signals,
    fallback_get_rl_execution_metrics,
    fallback_get_system_metrics
)

# Global variables for real-time updates
carbon_core_client = None
carbon_core_data = {}
update_thread = None
stop_update_thread = False

def update_carbon_core_data():
    """Update Carbon Core data in a background thread."""
    global carbon_core_data, stop_update_thread

    while not stop_update_thread:
        try:
            # Use fallback functions to generate sample data
            system_metrics = fallback_get_system_metrics({})
            rl_execution = fallback_get_rl_execution_metrics({})

            # Create market microstructure data
            market_microstructure = {
                'markets': {
                    'SOL-USDC': fallback_get_market_microstructure({'market': 'SOL-USDC'}),
                    'JTO-USDC': fallback_get_market_microstructure({'market': 'JTO-USDC'}),
                    'BONK-USDC': fallback_get_market_microstructure({'market': 'BONK-USDC'})
                }
            }

            # Create statistical signals data
            statistical_signals = {
                'signals': {
                    'price_momentum': fallback_get_statistical_signals({'signal_type': 'price_momentum'}),
                    'volume_profile': fallback_get_statistical_signals({'signal_type': 'volume_profile'}),
                    'order_flow_imbalance': fallback_get_statistical_signals({'signal_type': 'order_flow_imbalance'}),
                    'volatility_regime': fallback_get_statistical_signals({'signal_type': 'volatility_regime'})
                }
            }

            # Update global data
            carbon_core_data = {
                'timestamp': datetime.now().isoformat(),
                'system_metrics': system_metrics,
                'rl_execution': rl_execution,
                'market_microstructure': market_microstructure,
                'statistical_signals': statistical_signals
            }

            # Save to file for persistence
            # Use the standard output directory from the data service
            output_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'output')
            os.makedirs(output_dir, exist_ok=True)

            with open(os.path.join(output_dir, 'carbon_core_metrics.json'), 'w') as f:
                json.dump(carbon_core_data, f, indent=2)

        except Exception as e:
            print(f"Error updating Carbon Core data: {e}")

        # Sleep for 2 seconds before next update
        time.sleep(2)

def start_update_thread():
    """Start the update thread if not already running."""
    global update_thread, stop_update_thread

    if update_thread is None or not update_thread.is_alive():
        stop_update_thread = False
        update_thread = threading.Thread(target=update_carbon_core_data)
        update_thread.daemon = True
        update_thread.start()

def stop_update_thread_func():
    """Stop the update thread."""
    global stop_update_thread
    stop_update_thread = True

def render_advanced_models(data: Dict[str, Any]):
    """
    Render advanced models section showing Market Microstructure,
    Statistical Signal Processing, and RL Execution metrics.

    Args:
        data: Dashboard data from the unified dashboard
    """
    st.header("Advanced Trading Models")

    # Start the update thread for real-time updates
    start_update_thread()

    # Add a checkbox for real-time updates
    real_time_updates = st.checkbox("Enable Real-Time Updates", value=True)

    # Try to load carbon core data from the data service
    carbon_core_metrics = data_service.load_carbon_core_metrics()

    if real_time_updates:
        # Use the latest data from the update thread
        if carbon_core_data:
            data = {'carbon_core': carbon_core_data}
    elif carbon_core_metrics:
        # Use data from the data service
        data = {'carbon_core': carbon_core_metrics}

    # Check if carbon core data exists
    if 'carbon_core' not in data or not data['carbon_core']:
        st.info("Carbon Core data not available. Make sure the Carbon Core integration is enabled in the configuration.")
        return

    # Display last update time
    if 'timestamp' in data['carbon_core']:
        try:
            last_update = datetime.fromisoformat(data['carbon_core']['timestamp'])
            st.caption(f"Last updated: {last_update.strftime('%Y-%m-%d %H:%M:%S')}")
        except (ValueError, TypeError):
            st.caption(f"Last updated: {data['carbon_core']['timestamp']}")

    # Create tabs for different advanced models
    tab1, tab2, tab3, tab4 = st.tabs([
        "Market Microstructure",
        "Statistical Signal Processing",
        "RL Execution",
        "System Metrics"
    ])

    with tab1:
        render_market_microstructure(data)

    with tab2:
        render_statistical_signal_processing(data)

    with tab3:
        render_rl_execution(data)

    with tab4:
        render_carbon_system_metrics(data)

    # Add auto-refresh functionality
    if real_time_updates:
        st.empty()
        time.sleep(2)  # Wait for 2 seconds
        st.experimental_rerun()  # Rerun the app to refresh the data

def render_market_microstructure(data: Dict[str, Any]):
    """
    Render market microstructure metrics.

    Args:
        data: Dashboard data
    """
    st.subheader("Market Microstructure Analysis")

    if 'carbon_core' in data and 'market_microstructure' in data['carbon_core']:
        microstructure = data['carbon_core']['market_microstructure']

        # Market selection
        if 'markets' in microstructure:
            markets = list(microstructure['markets'].keys())
            selected_market = st.selectbox("Select Market", markets, key="market_select")

            if selected_market and selected_market in microstructure['markets']:
                market_data = microstructure['markets'][selected_market]

                # Display market microstructure metrics with delta indicators
                col1, col2, col3 = st.columns(3)

                # Calculate deltas (simulated for now)
                effective_spread = market_data.get('effective_spread', 0)
                price_impact = market_data.get('price_impact', 0)
                order_flow_imbalance = market_data.get('order_flow_imbalance', 0)
                liquidity_score = market_data.get('liquidity_score', 0)
                volatility = market_data.get('volatility', 0)
                market_efficiency = market_data.get('market_efficiency', 0)

                # Generate random deltas for demonstration
                import random
                effective_spread_delta = random.uniform(-0.0005, 0.0005)
                price_impact_delta = random.uniform(-0.0005, 0.0005)
                order_flow_imbalance_delta = random.uniform(-0.05, 0.05)
                liquidity_score_delta = random.uniform(-0.05, 0.05)
                volatility_delta = random.uniform(-0.002, 0.002)
                market_efficiency_delta = random.uniform(-0.05, 0.05)

                with col1:
                    st.metric("Effective Spread", f"{effective_spread:.4f}",
                              delta=f"{effective_spread_delta:.4f}")
                    st.metric("Price Impact", f"{price_impact:.4f}",
                              delta=f"{price_impact_delta:.4f}")

                with col2:
                    st.metric("Order Flow Imbalance", f"{order_flow_imbalance:.4f}",
                              delta=f"{order_flow_imbalance_delta:.4f}")
                    st.metric("Liquidity Score", f"{liquidity_score:.2f}",
                              delta=f"{liquidity_score_delta:.2f}")

                with col3:
                    st.metric("Volatility", f"{volatility:.4f}",
                              delta=f"{volatility_delta:.4f}")
                    st.metric("Market Efficiency", f"{market_efficiency:.2f}",
                              delta=f"{market_efficiency_delta:.2f}")

                # Add visualization options
                viz_options = st.radio(
                    "Select Visualization",
                    ["Order Book", "Market Impact", "Combined View"],
                    horizontal=True,
                    key="market_viz_options"
                )

                # Display order book visualization if available
                if 'order_book' in market_data and (viz_options == "Order Book" or viz_options == "Combined View"):
                    if viz_options == "Order Book":
                        st.subheader("Order Book Visualization")

                    order_book = market_data['order_book']

                    # Create order book visualization
                    fig = go.Figure()

                    # Add bids
                    if 'bids' in order_book:
                        bids_df = pd.DataFrame(order_book['bids'])
                        fig.add_trace(go.Bar(
                            x=bids_df['price'],
                            y=bids_df['size'],
                            name='Bids',
                            marker_color='green',
                            hovertemplate='Price: %{x:.4f}<br>Size: %{y:.2f}<extra></extra>'
                        ))

                    # Add asks
                    if 'asks' in order_book:
                        asks_df = pd.DataFrame(order_book['asks'])
                        fig.add_trace(go.Bar(
                            x=asks_df['price'],
                            y=asks_df['size'],
                            name='Asks',
                            marker_color='red',
                            hovertemplate='Price: %{x:.4f}<br>Size: %{y:.2f}<extra></extra>'
                        ))

                    # Add mid price line
                    if 'bids' in order_book and 'asks' in order_book and len(order_book['bids']) > 0 and len(order_book['asks']) > 0:
                        best_bid = max([b['price'] for b in order_book['bids']])
                        best_ask = min([a['price'] for a in order_book['asks']])
                        mid_price = (best_bid + best_ask) / 2

                        fig.add_shape(
                            type="line",
                            x0=mid_price,
                            y0=0,
                            x1=mid_price,
                            y1=max([b['size'] for b in order_book['bids']] + [a['size'] for a in order_book['asks']]) * 1.1,
                            line=dict(color="blue", width=2, dash="dash"),
                        )

                        fig.add_annotation(
                            x=mid_price,
                            y=0,
                            text=f"Mid: {mid_price:.4f}",
                            showarrow=True,
                            arrowhead=1,
                            ax=0,
                            ay=-40
                        )

                    fig.update_layout(
                        title='Order Book',
                        xaxis_title='Price',
                        yaxis_title='Size',
                        barmode='group',
                        hovermode='closest',
                        legend=dict(
                            orientation="h",
                            yanchor="bottom",
                            y=1.02,
                            xanchor="right",
                            x=1
                        ),
                        margin=dict(l=20, r=20, t=40, b=20),
                    )

                    # Add range slider
                    fig.update_layout(
                        xaxis=dict(
                            rangeslider=dict(visible=True),
                            type="linear"
                        )
                    )

                    st.plotly_chart(fig, use_container_width=True)

                # Display market impact analysis if available
                if 'market_impact' in market_data and (viz_options == "Market Impact" or viz_options == "Combined View"):
                    if viz_options == "Market Impact":
                        st.subheader("Market Impact Analysis")

                    impact_data = market_data['market_impact']

                    # Create impact visualization
                    fig = px.line(
                        x=impact_data.get('trade_sizes', []),
                        y=impact_data.get('price_impacts', []),
                        labels={'x': 'Trade Size', 'y': 'Price Impact (%)'},
                        title='Market Impact by Trade Size'
                    )

                    # Add markers
                    fig.update_traces(mode='lines+markers', marker=dict(size=8))

                    # Add annotations for key points
                    if len(impact_data.get('trade_sizes', [])) > 0:
                        max_impact_idx = impact_data.get('price_impacts', []).index(max(impact_data.get('price_impacts', [0])))
                        fig.add_annotation(
                            x=impact_data.get('trade_sizes', [])[max_impact_idx],
                            y=impact_data.get('price_impacts', [])[max_impact_idx],
                            text="Maximum Impact",
                            showarrow=True,
                            arrowhead=1,
                            ax=0,
                            ay=-40
                        )

                    # Add optimal trade size region
                    if len(impact_data.get('trade_sizes', [])) > 2:
                        # Find the "elbow" point where impact starts to increase more rapidly
                        elbow_idx = len(impact_data.get('trade_sizes', [])) // 3

                        fig.add_shape(
                            type="rect",
                            x0=0,
                            y0=0,
                            x1=impact_data.get('trade_sizes', [])[elbow_idx],
                            y1=impact_data.get('price_impacts', [])[elbow_idx],
                            fillcolor="green",
                            opacity=0.2,
                            line_width=0,
                        )

                        fig.add_annotation(
                            x=impact_data.get('trade_sizes', [])[elbow_idx] / 2,
                            y=impact_data.get('price_impacts', [])[elbow_idx] / 2,
                            text="Optimal Trade Size Region",
                            showarrow=False,
                            font=dict(color="black")
                        )

                    fig.update_layout(
                        hovermode='closest',
                        legend=dict(
                            orientation="h",
                            yanchor="bottom",
                            y=1.02,
                            xanchor="right",
                            x=1
                        ),
                        margin=dict(l=20, r=20, t=40, b=20),
                    )

                    # Add interactive elements
                    trade_size_slider = st.slider(
                        "Simulate Trade Size",
                        min_value=min(impact_data.get('trade_sizes', [1000])),
                        max_value=max(impact_data.get('trade_sizes', [1000000])),
                        value=min(impact_data.get('trade_sizes', [1000])) + (max(impact_data.get('trade_sizes', [1000000])) - min(impact_data.get('trade_sizes', [1000]))) // 4,
                        key="trade_size_slider"
                    )

                    # Interpolate price impact for selected trade size
                    if len(impact_data.get('trade_sizes', [])) > 1:
                        # Simple linear interpolation
                        trade_sizes = impact_data.get('trade_sizes', [])
                        price_impacts = impact_data.get('price_impacts', [])

                        # Find the two closest points for interpolation
                        idx_below = 0
                        idx_above = len(trade_sizes) - 1

                        for i in range(len(trade_sizes)):
                            if trade_sizes[i] <= trade_size_slider:
                                idx_below = i
                            if trade_sizes[i] >= trade_size_slider and i < idx_above:
                                idx_above = i

                        # Linear interpolation formula
                        if idx_below == idx_above:
                            interpolated_impact = price_impacts[idx_below]
                        else:
                            x0, x1 = trade_sizes[idx_below], trade_sizes[idx_above]
                            y0, y1 = price_impacts[idx_below], price_impacts[idx_above]
                            interpolated_impact = y0 + (y1 - y0) * (trade_size_slider - x0) / (x1 - x0)

                        st.metric(
                            "Estimated Price Impact",
                            f"{interpolated_impact:.4f}%",
                            delta=f"{interpolated_impact - impact_data.get('price_impacts', [0])[0]:.4f}%"
                        )

                        # Add vertical line for selected trade size
                        fig.add_shape(
                            type="line",
                            x0=trade_size_slider,
                            y0=0,
                            x1=trade_size_slider,
                            y1=interpolated_impact,
                            line=dict(color="blue", width=2, dash="dash"),
                        )

                        fig.add_annotation(
                            x=trade_size_slider,
                            y=interpolated_impact,
                            text=f"Selected: {interpolated_impact:.4f}%",
                            showarrow=True,
                            arrowhead=1,
                            ax=0,
                            ay=-40
                        )

                    st.plotly_chart(fig, use_container_width=True)

                # Add market insights
                with st.expander("Market Insights"):
                    st.markdown(f"""
                    ### {selected_market} Market Analysis

                    **Market Efficiency**: {market_efficiency:.2f}/1.0
                    - {'High efficiency indicates a well-functioning market with minimal arbitrage opportunities.' if market_efficiency > 0.7 else 'Lower efficiency may indicate potential arbitrage opportunities.'}

                    **Liquidity**: {liquidity_score:.2f}/1.0
                    - {'High liquidity allows for larger trades with minimal price impact.' if liquidity_score > 0.7 else 'Lower liquidity may require more careful trade sizing to avoid excessive slippage.'}

                    **Volatility**: {volatility:.4f}
                    - {'Current volatility is relatively low, suggesting stable price action.' if volatility < 0.03 else 'Higher volatility indicates potential for larger price swings.'}

                    **Order Flow Imbalance**: {order_flow_imbalance:.4f}
                    - {'Order flow is relatively balanced between buyers and sellers.' if -0.1 < order_flow_imbalance < 0.1 else 'Significant imbalance suggests potential directional price movement.'}
                    """)
    else:
        st.info("Market microstructure data not available")

def render_statistical_signal_processing(data: Dict[str, Any]):
    """
    Render statistical signal processing metrics.

    Args:
        data: Dashboard data
    """
    st.subheader("Statistical Signal Processing")

    if 'carbon_core' in data and 'statistical_signals' in data['carbon_core']:
        signals = data['carbon_core']['statistical_signals']

        # Signal selection
        if 'signals' in signals:
            signal_types = list(signals['signals'].keys())
            selected_signal = st.selectbox("Select Signal Type", signal_types)

            if selected_signal and selected_signal in signals['signals']:
                signal_data = signals['signals'][selected_signal]

                # Display signal metrics
                col1, col2, col3 = st.columns(3)

                with col1:
                    st.metric("Signal Strength", f"{signal_data.get('strength', 0):.2f}")
                    st.metric("Signal Quality", f"{signal_data.get('quality', 0):.2f}")

                with col2:
                    st.metric("Signal-to-Noise Ratio", f"{signal_data.get('snr', 0):.2f}")
                    st.metric("Confidence", f"{signal_data.get('confidence', 0):.2%}")

                with col3:
                    st.metric("Latency (ms)", f"{signal_data.get('latency_ms', 0):.1f}")
                    st.metric("Prediction Horizon", f"{signal_data.get('prediction_horizon', 0)}")

                # Display signal visualization if available
                if 'time_series' in signal_data:
                    st.subheader("Signal Visualization")

                    time_series = signal_data['time_series']

                    # Create time series visualization
                    fig = go.Figure()

                    # Add raw signal
                    if 'timestamps' in time_series and 'raw_values' in time_series:
                        fig.add_trace(go.Scatter(
                            x=time_series['timestamps'],
                            y=time_series['raw_values'],
                            mode='lines',
                            name='Raw Signal'
                        ))

                    # Add filtered signal
                    if 'timestamps' in time_series and 'filtered_values' in time_series:
                        fig.add_trace(go.Scatter(
                            x=time_series['timestamps'],
                            y=time_series['filtered_values'],
                            mode='lines',
                            name='Filtered Signal'
                        ))

                    # Add prediction
                    if 'timestamps' in time_series and 'predictions' in time_series:
                        fig.add_trace(go.Scatter(
                            x=time_series['timestamps'],
                            y=time_series['predictions'],
                            mode='lines',
                            name='Prediction',
                            line=dict(dash='dash')
                        ))

                    fig.update_layout(
                        title='Signal Time Series',
                        xaxis_title='Time',
                        yaxis_title='Value'
                    )

                    st.plotly_chart(fig)
    else:
        st.info("Statistical signal processing data not available")

def render_rl_execution(data: Dict[str, Any]):
    """
    Render reinforcement learning execution metrics.

    Args:
        data: Dashboard data
    """
    st.subheader("Reinforcement Learning Execution")

    if 'carbon_core' in data and 'rl_execution' in data['carbon_core']:
        rl_data = data['carbon_core']['rl_execution']

        # Display RL metrics
        col1, col2, col3 = st.columns(3)

        with col1:
            st.metric("Execution Quality", f"{rl_data.get('execution_quality', 0):.2f}")
            st.metric("Reward", f"{rl_data.get('reward', 0):.2f}")

        with col2:
            st.metric("Slippage Reduction", f"{rl_data.get('slippage_reduction', 0):.2%}")
            st.metric("Policy Confidence", f"{rl_data.get('policy_confidence', 0):.2f}")

        with col3:
            st.metric("Actions Taken", f"{rl_data.get('actions_taken', 0)}")
            st.metric("Learning Rate", f"{rl_data.get('learning_rate', 0):.4f}")

        # Display RL visualization if available
        if 'execution_history' in rl_data:
            st.subheader("Execution History")

            history = rl_data['execution_history']

            # Convert to DataFrame for easier display
            if history:
                history_df = pd.DataFrame(history)

                # Display execution history
                st.dataframe(history_df)

                # Create visualization
                if 'timestamps' in history_df.columns and 'rewards' in history_df.columns:
                    fig = px.line(
                        history_df,
                        x='timestamps',
                        y='rewards',
                        title='Execution Rewards Over Time'
                    )

                    st.plotly_chart(fig)

        # Display policy visualization if available
        if 'policy_visualization' in rl_data:
            st.subheader("Policy Visualization")

            policy = rl_data['policy_visualization']

            # Create policy visualization
            if 'states' in policy and 'actions' in policy and 'values' in policy:
                fig = px.scatter_3d(
                    x=policy['states'],
                    y=policy['actions'],
                    z=policy['values'],
                    color=policy['values'],
                    title='RL Policy Visualization'
                )

                st.plotly_chart(fig)
    else:
        st.info("RL execution data not available")

def render_carbon_system_metrics(data: Dict[str, Any]):
    """
    Render Carbon Core system metrics.

    Args:
        data: Dashboard data
    """
    st.subheader("Carbon Core System Metrics")

    if 'carbon_core' in data and 'system_metrics' in data['carbon_core']:
        carbon_metrics = data['carbon_core']['system_metrics']

        # Display system metrics
        col1, col2, col3 = st.columns(3)

        with col1:
            st.metric("CPU Usage", f"{carbon_metrics.get('cpu_usage', 0):.1f}%")
            st.metric("Memory Usage", f"{carbon_metrics.get('memory_usage_mb', 0):.1f} MB")

        with col2:
            st.metric("Processing Latency", f"{carbon_metrics.get('processing_latency_ms', 0):.2f} ms")
            st.metric("Queue Size", f"{carbon_metrics.get('queue_size', 0)}")

        with col3:
            st.metric("Uptime", f"{carbon_metrics.get('uptime_seconds', 0) // 3600} hours")
            st.metric("Throughput", f"{carbon_metrics.get('throughput', 0):.1f} ops/sec")

        # Display performance history if available
        if 'performance_history' in carbon_metrics:
            st.subheader("Performance History")

            history = carbon_metrics['performance_history']

            # Create performance visualization
            if 'timestamps' in history and 'latencies' in history:
                fig = go.Figure()

                fig.add_trace(go.Scatter(
                    x=history['timestamps'],
                    y=history['latencies'],
                    mode='lines',
                    name='Processing Latency'
                ))

                fig.update_layout(
                    title='Processing Latency Over Time',
                    xaxis_title='Time',
                    yaxis_title='Latency (ms)'
                )

                st.plotly_chart(fig)
    else:
        st.info("Carbon Core system metrics not available")
