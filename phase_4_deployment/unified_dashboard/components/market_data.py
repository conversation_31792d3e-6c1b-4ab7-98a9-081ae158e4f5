#!/usr/bin/env python3
"""
Market Data Component

This module provides components for displaying market data in the Synergy7 Unified Dashboard.
"""

import pandas as pd
import streamlit as st
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

def render_market_data(data: Dict[str, Any]):
    """
    Render market data components.

    Args:
        data: Dashboard data
    """
    st.header("Market Data")

    # Create tabs for different market data
    tab1, tab2, tab3 = st.tabs([
        "Token Scanner",
        "Whale Watcher",
        "Market Microstructure"
    ])

    with tab1:
        render_token_scanner(data)

    with tab2:
        render_whale_watcher(data)

    with tab3:
        render_market_microstructure(data)

def render_token_scanner(data: Dict[str, Any]):
    """
    Render token scanner component.

    Args:
        data: Dashboard data
    """
    st.subheader("Token Scanner")

    if 'token_opportunities' in data and 'opportunities' in data['token_opportunities'] and data['token_opportunities']['opportunities']:
        # Convert to DataFrame for easier display
        opportunities = data['token_opportunities']['opportunities']

        # Display opportunities
        st.subheader(f"Found {len(opportunities)} Token Opportunities")

        for i, opp in enumerate(opportunities):
            with st.expander(f"{opp.get('symbol', 'Unknown')} - {opp.get('name', 'Unknown Token')}"):
                col1, col2 = st.columns(2)

                with col1:
                    st.write(f"**Address:** {opp.get('address', 'N/A')}")
                    st.write(f"**Price:** ${opp.get('price', 0):.6f}")
                    st.write(f"**24h Volume:** ${opp.get('volume', 0):,.2f}")

                with col2:
                    st.write(f"**Market Cap:** ${opp.get('marketCap', 0):,.2f}")
                    st.write(f"**Holders:** {opp.get('holders', 0):,}")
                    st.write(f"**Created:** {opp.get('createdAt', 'Unknown')}")

                # Display token details
                if 'details' in opp:
                    st.subheader("Token Details")
                    st.json(opp['details'])
    else:
        st.info("No token opportunities found")
        
        # Display placeholder data
        st.subheader("Sample Token Opportunities (Placeholder)")
        
        # Create placeholder data
        token_data = [
            {
                "symbol": "SAMPLE1",
                "name": "Sample Token 1",
                "address": "7nVTXRrLfSeNxJyYGiQNrh4jNjqPJhcxwQQNjJpkJYb1",
                "price": 0.000125,
                "volume": 125000,
                "marketCap": 1250000,
                "holders": 1250,
                "createdAt": "2023-05-15T12:30:45Z"
            },
            {
                "symbol": "SAMPLE2",
                "name": "Sample Token 2",
                "address": "8nVTXRrLfSeNxJyYGiQNrh4jNjqPJhcxwQQNjJpkJYb2",
                "price": 0.000325,
                "volume": 325000,
                "marketCap": 3250000,
                "holders": 2500,
                "createdAt": "2023-05-16T10:15:30Z"
            },
            {
                "symbol": "SAMPLE3",
                "name": "Sample Token 3",
                "address": "9nVTXRrLfSeNxJyYGiQNrh4jNjqPJhcxwQQNjJpkJYb3",
                "price": 0.000075,
                "volume": 75000,
                "marketCap": 750000,
                "holders": 950,
                "createdAt": "2023-05-17T08:45:15Z"
            }
        ]
        
        # Display opportunities
        st.subheader(f"Found {len(token_data)} Sample Token Opportunities")
        
        for i, opp in enumerate(token_data):
            with st.expander(f"{opp['symbol']} - {opp['name']}"):
                col1, col2 = st.columns(2)
                
                with col1:
                    st.write(f"**Address:** {opp['address']}")
                    st.write(f"**Price:** ${opp['price']:.6f}")
                    st.write(f"**24h Volume:** ${opp['volume']:,.2f}")
                
                with col2:
                    st.write(f"**Market Cap:** ${opp['marketCap']:,.2f}")
                    st.write(f"**Holders:** {opp['holders']:,}")
                    st.write(f"**Created:** {opp['createdAt']}")

def render_whale_watcher(data: Dict[str, Any]):
    """
    Render whale watcher component.

    Args:
        data: Dashboard data
    """
    st.subheader("Whale Watcher")

    if 'whale_opportunities' in data and 'opportunities' in data['whale_opportunities'] and data['whale_opportunities']['opportunities']:
        # Convert to DataFrame for easier display
        opportunities = data['whale_opportunities']['opportunities']

        # Display opportunities
        st.subheader(f"Found {len(opportunities)} Whale Activities")

        for i, opp in enumerate(opportunities):
            with st.expander(f"Token: {opp.get('token_address', 'Unknown')} - {opp.get('whale_transaction_count', 0)} transactions"):
                st.write(f"**Whale Transaction Count:** {opp.get('whale_transaction_count', 0)}")

                # Display activity
                if 'activity' in opp and opp['activity']:
                    st.subheader("Recent Activity")

                    # Convert to DataFrame for easier display
                    activity_df = pd.DataFrame(opp['activity'])

                    # Display activity
                    st.dataframe(activity_df)
    else:
        st.info("No whale activities found")
        
        # Display placeholder data
        st.subheader("Sample Whale Activities (Placeholder)")
        
        # Create placeholder data
        whale_data = [
            {
                "token_address": "SOL",
                "token_name": "Solana",
                "whale_transaction_count": 15,
                "activity": [
                    {"timestamp": "2023-05-15T12:30:45Z", "type": "buy", "amount": 10000, "value_usd": 250000},
                    {"timestamp": "2023-05-15T14:45:30Z", "type": "buy", "amount": 5000, "value_usd": 125000},
                    {"timestamp": "2023-05-16T09:15:00Z", "type": "sell", "amount": 3000, "value_usd": 75000}
                ]
            },
            {
                "token_address": "JTO",
                "token_name": "Jito",
                "whale_transaction_count": 8,
                "activity": [
                    {"timestamp": "2023-05-16T10:30:45Z", "type": "buy", "amount": 50000, "value_usd": 100000},
                    {"timestamp": "2023-05-16T16:45:30Z", "type": "sell", "amount": 20000, "value_usd": 40000}
                ]
            }
        ]
        
        # Display opportunities
        st.subheader(f"Found {len(whale_data)} Sample Whale Activities")
        
        for i, opp in enumerate(whale_data):
            with st.expander(f"Token: {opp['token_name']} ({opp['token_address']}) - {opp['whale_transaction_count']} transactions"):
                st.write(f"**Whale Transaction Count:** {opp['whale_transaction_count']}")
                
                # Display activity
                if 'activity' in opp and opp['activity']:
                    st.subheader("Recent Activity")
                    
                    # Convert to DataFrame for easier display
                    activity_df = pd.DataFrame(opp['activity'])
                    
                    # Display activity
                    st.dataframe(activity_df)

def render_market_microstructure(data: Dict[str, Any]):
    """
    Render market microstructure component.

    Args:
        data: Dashboard data
    """
    st.subheader("Market Microstructure")

    # Check if Carbon Core data exists
    if 'carbon_core' in data and 'market_microstructure' in data['carbon_core']:
        microstructure = data['carbon_core']['market_microstructure']

        # Market selection
        if 'markets' in microstructure:
            markets = list(microstructure['markets'].keys())
            selected_market = st.selectbox("Select Market", markets)

            if selected_market and selected_market in microstructure['markets']:
                market_data = microstructure['markets'][selected_market]

                # Display market microstructure metrics
                col1, col2, col3 = st.columns(3)

                with col1:
                    st.metric("Effective Spread", f"{market_data.get('effective_spread', 0):.4f}")
                    st.metric("Price Impact", f"{market_data.get('price_impact', 0):.4f}")

                with col2:
                    st.metric("Order Flow Imbalance", f"{market_data.get('order_flow_imbalance', 0):.4f}")
                    st.metric("Liquidity Score", f"{market_data.get('liquidity_score', 0):.2f}")

                with col3:
                    st.metric("Volatility", f"{market_data.get('volatility', 0):.4f}")
                    st.metric("Market Efficiency", f"{market_data.get('market_efficiency', 0):.2f}")

                # Display order book visualization if available
                if 'order_book' in market_data:
                    st.subheader("Order Book Visualization")

                    order_book = market_data['order_book']

                    # Create order book visualization
                    fig = go.Figure()

                    # Add bids
                    if 'bids' in order_book:
                        bids_df = pd.DataFrame(order_book['bids'])
                        fig.add_trace(go.Bar(
                            x=bids_df['price'],
                            y=bids_df['size'],
                            name='Bids',
                            marker_color='green',
                            hovertemplate='Price: %{x:.4f}<br>Size: %{y:.2f}<extra></extra>'
                        ))

                    # Add asks
                    if 'asks' in order_book:
                        asks_df = pd.DataFrame(order_book['asks'])
                        fig.add_trace(go.Bar(
                            x=asks_df['price'],
                            y=asks_df['size'],
                            name='Asks',
                            marker_color='red',
                            hovertemplate='Price: %{x:.4f}<br>Size: %{y:.2f}<extra></extra>'
                        ))

                    fig.update_layout(
                        title='Order Book',
                        xaxis_title='Price',
                        yaxis_title='Size',
                        barmode='group',
                        hovermode='closest'
                    )

                    st.plotly_chart(fig, use_container_width=True)
    else:
        st.info("No market microstructure data available")
        
        # Display placeholder data
        st.subheader("Sample Market Microstructure (Placeholder)")
        
        # Create placeholder data
        markets = ["SOL-USDC", "JTO-USDC", "BONK-USDC"]
        selected_market = st.selectbox("Select Market", markets)
        
        # Display market microstructure metrics
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("Effective Spread", "0.0025")
            st.metric("Price Impact", "0.0018")
        
        with col2:
            st.metric("Order Flow Imbalance", "0.1250")
            st.metric("Liquidity Score", "0.85")
        
        with col3:
            st.metric("Volatility", "0.0325")
            st.metric("Market Efficiency", "0.92")
        
        # Create placeholder order book visualization
        st.subheader("Order Book Visualization (Placeholder)")
        
        # Create placeholder data
        import numpy as np
        
        if selected_market == "SOL-USDC":
            base_price = 25.0
        elif selected_market == "JTO-USDC":
            base_price = 2.0
        else:  # BONK-USDC
            base_price = 0.00001
        
        # Create bid and ask prices
        bid_prices = [base_price * (1 - 0.001 * i) for i in range(10)]
        ask_prices = [base_price * (1 + 0.001 * i) for i in range(10)]
        
        # Create bid and ask sizes
        bid_sizes = [np.random.uniform(10, 100) for _ in range(10)]
        ask_sizes = [np.random.uniform(10, 100) for _ in range(10)]
        
        # Create DataFrames
        bids_df = pd.DataFrame({"price": bid_prices, "size": bid_sizes})
        asks_df = pd.DataFrame({"price": ask_prices, "size": ask_sizes})
        
        # Create order book visualization
        fig = go.Figure()
        
        # Add bids
        fig.add_trace(go.Bar(
            x=bids_df['price'],
            y=bids_df['size'],
            name='Bids',
            marker_color='green',
            hovertemplate='Price: %{x:.4f}<br>Size: %{y:.2f}<extra></extra>'
        ))
        
        # Add asks
        fig.add_trace(go.Bar(
            x=asks_df['price'],
            y=asks_df['size'],
            name='Asks',
            marker_color='red',
            hovertemplate='Price: %{x:.4f}<br>Size: %{y:.2f}<extra></extra>'
        ))
        
        fig.update_layout(
            title='Order Book',
            xaxis_title='Price',
            yaxis_title='Size',
            barmode='group',
            hovermode='closest'
        )
        
        st.plotly_chart(fig, use_container_width=True)
