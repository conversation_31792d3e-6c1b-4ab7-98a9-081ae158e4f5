#!/usr/bin/env python3
"""
Dashboard Simulation Script

This script generates realistic trading data for testing the Synergy7 Unified Dashboard.
It simulates a 1-minute trading session with realistic market data, trades, and metrics.
"""

import os
import sys
import json
import time
import random
import logging
import argparse
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("simulation")

class DashboardSimulator:
    """
    Simulator for generating realistic trading data for the Synergy7 Unified Dashboard.
    """

    def __init__(self, output_dir: str, duration_seconds: int = 60, update_interval: float = 1.0):
        """
        Initialize the simulator.

        Args:
            output_dir: Directory to write output files
            duration_seconds: Duration of the simulation in seconds
            update_interval: Interval between updates in seconds
        """
        self.output_dir = output_dir
        self.duration_seconds = duration_seconds
        self.update_interval = update_interval
        
        # Ensure output directory exists
        os.makedirs(self.output_dir, exist_ok=True)
        
        # Initialize simulation state
        self.start_time = datetime.now()
        self.current_time = self.start_time
        self.end_time = self.start_time + timedelta(seconds=duration_seconds)
        
        # Initialize metrics
        self.metrics = {
            "system_cpu_usage": 15.0,
            "system_memory_usage": 1.5 * 1024 * 1024 * 1024,  # 1.5 GB
            "system_disk_usage": 10.0 * 1024 * 1024 * 1024,  # 10 GB
            "api_requests": {},
            "api_response_time": {},
            "stream_data_messages": {},
            "liljito_bundles": {},
            "wallet_balance": {}
        }
        
        # Initialize transaction history
        self.tx_history = {
            "transactions": []
        }
        
        # Initialize enriched signals
        self.enriched_signals = {
            "signals": []
        }
        
        # Initialize token opportunities
        self.token_opportunities = {
            "opportunities": []
        }
        
        # Initialize whale opportunities
        self.whale_opportunities = {
            "opportunities": []
        }
        
        # Initialize strategy metrics
        self.strategy_metrics = {
            "strategies": {}
        }
        
        # Initialize Carbon Core metrics
        self.carbon_core_metrics = {
            "market_microstructure": {
                "markets": {}
            },
            "statistical_signals": {},
            "rl_execution": {}
        }
        
        # Initialize wallet balance
        self.wallet_balance = 100.0  # SOL
        self.initial_wallet_balance = self.wallet_balance
        
        # Initialize tokens
        self.tokens = [
            {"symbol": "SOL", "name": "Solana", "price": 25.0, "price_change_24h": 0.05},
            {"symbol": "JTO", "name": "Jito", "price": 2.0, "price_change_24h": 0.02},
            {"symbol": "BONK", "name": "Bonk", "price": 0.00001, "price_change_24h": 0.1},
            {"symbol": "PYTH", "name": "Pyth", "price": 0.5, "price_change_24h": -0.03},
            {"symbol": "ORCA", "name": "Orca", "price": 1.2, "price_change_24h": 0.01}
        ]
        
        # Initialize strategies
        self.strategies = [
            {"id": "momentum_1", "name": "Momentum Strategy 1", "win_rate": 0.65, "sharpe_ratio": 1.8, "composite_score": 0.75},
            {"id": "mean_reversion_1", "name": "Mean Reversion 1", "win_rate": 0.55, "sharpe_ratio": 1.5, "composite_score": 0.65},
            {"id": "breakout_1", "name": "Breakout Strategy 1", "win_rate": 0.60, "sharpe_ratio": 1.7, "composite_score": 0.70},
            {"id": "whale_follower_1", "name": "Whale Follower 1", "win_rate": 0.70, "sharpe_ratio": 2.0, "composite_score": 0.80},
            {"id": "liquidity_1", "name": "Liquidity Strategy 1", "win_rate": 0.58, "sharpe_ratio": 1.6, "composite_score": 0.68}
        ]
        
        logger.info(f"Initialized simulator with duration: {duration_seconds} seconds")
        logger.info(f"Output directory: {output_dir}")

    def run(self):
        """
        Run the simulation.
        """
        logger.info(f"Starting simulation at {self.start_time}")
        
        # Initialize metrics
        self._initialize_metrics()
        
        # Run simulation loop
        iteration = 0
        while self.current_time < self.end_time:
            iteration += 1
            logger.info(f"Simulation iteration {iteration} at {self.current_time}")
            
            # Update simulation state
            self._update_simulation_state()
            
            # Generate metrics
            self._generate_metrics()
            
            # Write output files
            self._write_output_files()
            
            # Sleep for update interval
            time.sleep(self.update_interval)
            
            # Update current time
            self.current_time = datetime.now()
        
        logger.info(f"Simulation completed at {self.current_time}")
        logger.info(f"Total iterations: {iteration}")
        logger.info(f"Final wallet balance: {self.wallet_balance:.4f} SOL")
        logger.info(f"Profit/Loss: {self.wallet_balance - self.initial_wallet_balance:.4f} SOL")

    def _initialize_metrics(self):
        """
        Initialize metrics with realistic values.
        """
        # Initialize API requests
        for api in ["Helius", "Birdeye", "Jupiter", "Jito", "QuickNode"]:
            self.metrics["api_requests"][json.dumps({"api": api, "status": "success"})] = random.randint(80, 100)
            self.metrics["api_requests"][json.dumps({"api": api, "status": "error"})] = random.randint(1, 10)
            
            # Initialize API response time
            self.metrics["api_response_time"][json.dumps({"api": api})] = {
                "count": random.randint(80, 100),
                "sum": random.uniform(10, 30)
            }
        
        # Initialize stream data messages
        for source in ["Helius", "Jito", "Lil Jito", "Birdeye", "PumpFun"]:
            self.metrics["stream_data_messages"][json.dumps({"source": source, "status": "success"})] = random.randint(80, 100)
            self.metrics["stream_data_messages"][json.dumps({"source": source, "status": "error"})] = random.randint(1, 10)
        
        # Initialize Lil' Jito bundles
        for status in ["Confirmed", "Pending", "Failed", "Timeout"]:
            self.metrics["liljito_bundles"][json.dumps({"status": status})] = random.randint(1, 50)
        
        # Initialize wallet balance
        self.metrics["wallet_balance"][json.dumps({"wallet": "Trading Wallet"})] = self.wallet_balance * 0.7
        self.metrics["wallet_balance"][json.dumps({"wallet": "Reserve Wallet"})] = self.wallet_balance * 0.2
        self.metrics["wallet_balance"][json.dumps({"wallet": "Fee Wallet"})] = self.wallet_balance * 0.1
        
        # Initialize Carbon Core metrics
        for token in self.tokens:
            market = f"{token['symbol']}-USDC"
            self.carbon_core_metrics["market_microstructure"]["markets"][market] = {
                "effective_spread": random.uniform(0.001, 0.005),
                "price_impact": random.uniform(0.001, 0.003),
                "order_flow_imbalance": random.uniform(-0.2, 0.2),
                "liquidity_score": random.uniform(0.7, 0.95),
                "volatility": random.uniform(0.01, 0.05),
                "market_efficiency": random.uniform(0.8, 0.95),
                "order_book": {
                    "bids": [{"price": token["price"] * (1 - 0.001 * i), "size": random.uniform(10, 100)} for i in range(10)],
                    "asks": [{"price": token["price"] * (1 + 0.001 * i), "size": random.uniform(10, 100)} for i in range(10)]
                }
            }
        
        # Initialize strategy metrics
        for strategy in self.strategies:
            self.strategy_metrics["strategies"][strategy["id"]] = {
                "name": strategy["name"],
                "win_rate": strategy["win_rate"],
                "sharpe_ratio": strategy["sharpe_ratio"],
                "composite_score": strategy["composite_score"],
                "profit_factor": random.uniform(1.2, 2.0),
                "max_drawdown": random.uniform(0.05, 0.15),
                "avg_trade": random.uniform(0.01, 0.05),
                "trades": []
            }
        
        # Initialize token opportunities
        for i in range(3):
            token = random.choice(self.tokens)
            self.token_opportunities["opportunities"].append({
                "symbol": token["symbol"],
                "name": token["name"],
                "address": f"{random.randint(1, 9)}nVTXRrLfSeNxJyYGiQNrh4jNjqPJhcxwQQNjJpkJYb{random.randint(1, 9)}",
                "price": token["price"],
                "volume": random.uniform(50000, 500000),
                "marketCap": token["price"] * random.uniform(1000000, 10000000),
                "holders": random.randint(500, 5000),
                "createdAt": (datetime.now() - timedelta(days=random.randint(1, 30))).isoformat()
            })
        
        # Initialize whale opportunities
        for i in range(2):
            token = random.choice(self.tokens)
            activity = []
            for j in range(random.randint(2, 5)):
                activity.append({
                    "timestamp": (datetime.now() - timedelta(hours=random.randint(1, 24))).isoformat(),
                    "type": random.choice(["buy", "sell"]),
                    "amount": random.uniform(1000, 10000),
                    "value_usd": random.uniform(10000, 100000)
                })
            
            self.whale_opportunities["opportunities"].append({
                "token_address": token["symbol"],
                "token_name": token["name"],
                "whale_transaction_count": random.randint(5, 20),
                "activity": activity
            })

    def _update_simulation_state(self):
        """
        Update the simulation state for the current iteration.
        """
        # Update token prices
        for token in self.tokens:
            # Add some random price movement
            price_change = token["price"] * random.uniform(-0.005, 0.005)
            token["price"] += price_change
            
            # Update 24h price change
            token["price_change_24h"] += random.uniform(-0.01, 0.01)
            token["price_change_24h"] = max(-0.2, min(0.2, token["price_change_24h"]))
        
        # Generate a new transaction with 30% probability
        if random.random() < 0.3:
            self._generate_transaction()
        
        # Generate a new signal with 20% probability
        if random.random() < 0.2:
            self._generate_signal()
        
        # Update system metrics
        self.metrics["system_cpu_usage"] += random.uniform(-2.0, 2.0)
        self.metrics["system_cpu_usage"] = max(5.0, min(95.0, self.metrics["system_cpu_usage"]))
        
        self.metrics["system_memory_usage"] += random.uniform(-0.1, 0.1) * 1024 * 1024 * 1024
        self.metrics["system_memory_usage"] = max(0.5 * 1024 * 1024 * 1024, min(4.0 * 1024 * 1024 * 1024, self.metrics["system_memory_usage"]))
        
        self.metrics["system_disk_usage"] += random.uniform(-0.1, 0.1) * 1024 * 1024 * 1024
        self.metrics["system_disk_usage"] = max(5.0 * 1024 * 1024 * 1024, min(20.0 * 1024 * 1024 * 1024, self.metrics["system_disk_usage"]))

    def _generate_transaction(self):
        """
        Generate a realistic transaction.
        """
        # Select a random token
        token = random.choice(self.tokens)
        
        # Select a random strategy
        strategy = random.choice(self.strategies)
        
        # Determine if buy or sell
        is_buy = random.random() < 0.5
        
        # Determine transaction amount
        amount_sol = random.uniform(0.1, 2.0)
        
        # Determine transaction status
        status = random.choices(
            ["confirmed", "pending", "failed", "timeout"],
            weights=[0.85, 0.05, 0.08, 0.02],
            k=1
        )[0]
        
        # Create transaction
        tx = {
            "signature": f"{random.randint(1, 9)}nVTXRrLfSeNxJyYGiQNrh4jNjqPJhcxwQQNjJpkJYb{random.randint(1, 9)}",
            "timestamp": int(self.current_time.timestamp()),
            "datetime": self.current_time.isoformat(),
            "token": token["symbol"],
            "price": token["price"],
            "amount": amount_sol,
            "value_usd": amount_sol * token["price"],
            "type": "buy" if is_buy else "sell",
            "strategy_id": strategy["id"],
            "status": status
        }
        
        # Add to transaction history
        self.tx_history["transactions"].append(tx)
        
        # Update wallet balance if transaction is confirmed
        if status == "confirmed":
            if is_buy:
                self.wallet_balance -= amount_sol
            else:
                self.wallet_balance += amount_sol
            
            # Update wallet balance metrics
            self.metrics["wallet_balance"][json.dumps({"wallet": "Trading Wallet"})] = self.wallet_balance * 0.7
            self.metrics["wallet_balance"][json.dumps({"wallet": "Reserve Wallet"})] = self.wallet_balance * 0.2
            self.metrics["wallet_balance"][json.dumps({"wallet": "Fee Wallet"})] = self.wallet_balance * 0.1
            
            # Add to strategy trades
            self.strategy_metrics["strategies"][strategy["id"]]["trades"].append({
                "timestamp": int(self.current_time.timestamp()),
                "token": token["symbol"],
                "price": token["price"],
                "amount": amount_sol,
                "type": "buy" if is_buy else "sell",
                "profit_loss": -amount_sol if is_buy else amount_sol
            })
        
        logger.info(f"Generated transaction: {tx['type']} {tx['amount']:.4f} {tx['token']} at ${tx['price']:.4f} (Status: {tx['status']})")

    def _generate_signal(self):
        """
        Generate a realistic trading signal.
        """
        # Select a random token
        token = random.choice(self.tokens)
        
        # Select a random strategy
        strategy = random.choice(self.strategies)
        
        # Determine signal type
        signal_type = random.choice(["buy", "sell"])
        
        # Determine signal strength
        signal_strength = random.uniform(0.5, 1.0)
        
        # Create signal
        signal = {
            "id": f"signal_{len(self.enriched_signals['signals']) + 1}",
            "timestamp": int(self.current_time.timestamp()),
            "datetime": self.current_time.isoformat(),
            "token": token["symbol"],
            "price": token["price"],
            "type": signal_type,
            "strength": signal_strength,
            "strategy_id": strategy["id"],
            "expiry": int((self.current_time + timedelta(minutes=random.randint(5, 30))).timestamp())
        }
        
        # Add to enriched signals
        self.enriched_signals["signals"].append(signal)
        
        logger.info(f"Generated signal: {signal['type']} {signal['token']} at ${signal['price']:.4f} (Strength: {signal['strength']:.2f})")

    def _generate_metrics(self):
        """
        Generate realistic metrics for the current iteration.
        """
        # Update API requests
        for api in ["Helius", "Birdeye", "Jupiter", "Jito", "QuickNode"]:
            success_key = json.dumps({"api": api, "status": "success"})
            error_key = json.dumps({"api": api, "status": "error"})
            
            # Add some random requests
            self.metrics["api_requests"][success_key] += random.randint(1, 5)
            if random.random() < 0.2:  # 20% chance of error
                self.metrics["api_requests"][error_key] += 1
            
            # Update API response time
            response_time_key = json.dumps({"api": api})
            self.metrics["api_response_time"][response_time_key]["count"] += random.randint(1, 5)
            self.metrics["api_response_time"][response_time_key]["sum"] += random.uniform(0.1, 0.5)
        
        # Update stream data messages
        for source in ["Helius", "Jito", "Lil Jito", "Birdeye", "PumpFun"]:
            success_key = json.dumps({"source": source, "status": "success"})
            error_key = json.dumps({"source": source, "status": "error"})
            
            # Add some random messages
            self.metrics["stream_data_messages"][success_key] += random.randint(1, 3)
            if random.random() < 0.1:  # 10% chance of error
                self.metrics["stream_data_messages"][error_key] += 1
        
        # Update Lil' Jito bundles
        for status in ["Confirmed", "Pending", "Failed", "Timeout"]:
            key = json.dumps({"status": status})
            
            # Add some random bundles
            if status == "Confirmed":
                self.metrics["liljito_bundles"][key] += random.randint(0, 2)
            elif status == "Pending":
                self.metrics["liljito_bundles"][key] = max(0, self.metrics["liljito_bundles"][key] - random.randint(0, 1)) + random.randint(0, 1)
            elif random.random() < 0.1:  # 10% chance of failed or timeout
                self.metrics["liljito_bundles"][key] += 1
        
        # Update Carbon Core metrics
        for token in self.tokens:
            market = f"{token['symbol']}-USDC"
            market_data = self.carbon_core_metrics["market_microstructure"]["markets"][market]
            
            # Update market microstructure metrics
            market_data["effective_spread"] += random.uniform(-0.0005, 0.0005)
            market_data["effective_spread"] = max(0.0005, min(0.01, market_data["effective_spread"]))
            
            market_data["price_impact"] += random.uniform(-0.0003, 0.0003)
            market_data["price_impact"] = max(0.0005, min(0.005, market_data["price_impact"]))
            
            market_data["order_flow_imbalance"] += random.uniform(-0.05, 0.05)
            market_data["order_flow_imbalance"] = max(-0.5, min(0.5, market_data["order_flow_imbalance"]))
            
            market_data["liquidity_score"] += random.uniform(-0.02, 0.02)
            market_data["liquidity_score"] = max(0.5, min(1.0, market_data["liquidity_score"]))
            
            market_data["volatility"] += random.uniform(-0.005, 0.005)
            market_data["volatility"] = max(0.005, min(0.1, market_data["volatility"]))
            
            market_data["market_efficiency"] += random.uniform(-0.02, 0.02)
            market_data["market_efficiency"] = max(0.5, min(1.0, market_data["market_efficiency"]))
            
            # Update order book
            for i in range(len(market_data["order_book"]["bids"])):
                market_data["order_book"]["bids"][i]["price"] = token["price"] * (1 - 0.001 * (i + 1))
                market_data["order_book"]["bids"][i]["size"] += random.uniform(-5, 5)
                market_data["order_book"]["bids"][i]["size"] = max(5, market_data["order_book"]["bids"][i]["size"])
            
            for i in range(len(market_data["order_book"]["asks"])):
                market_data["order_book"]["asks"][i]["price"] = token["price"] * (1 + 0.001 * (i + 1))
                market_data["order_book"]["asks"][i]["size"] += random.uniform(-5, 5)
                market_data["order_book"]["asks"][i]["size"] = max(5, market_data["order_book"]["asks"][i]["size"])

    def _write_output_files(self):
        """
        Write output files for the dashboard.
        """
        # Write metrics.json
        with open(os.path.join(self.output_dir, "metrics.json"), "w") as f:
            json.dump(self.metrics, f, indent=2)
        
        # Write tx_history.json
        with open(os.path.join(self.output_dir, "tx_history.json"), "w") as f:
            json.dump(self.tx_history, f, indent=2)
        
        # Write enriched_signals.json
        with open(os.path.join(self.output_dir, "enriched_signals.json"), "w") as f:
            json.dump(self.enriched_signals, f, indent=2)
        
        # Write token_opportunities.json
        with open(os.path.join(self.output_dir, "token_opportunities.json"), "w") as f:
            json.dump(self.token_opportunities, f, indent=2)
        
        # Write whale_opportunities.json
        with open(os.path.join(self.output_dir, "whale_opportunities.json"), "w") as f:
            json.dump(self.whale_opportunities, f, indent=2)
        
        # Write strategy_metrics.json
        with open(os.path.join(self.output_dir, "strategy_metrics.json"), "w") as f:
            json.dump(self.strategy_metrics, f, indent=2)
        
        # Write carbon_core_metrics.json
        with open(os.path.join(self.output_dir, "carbon_core_metrics.json"), "w") as f:
            json.dump(self.carbon_core_metrics, f, indent=2)
        
        # Write helius_metrics.json
        with open(os.path.join(self.output_dir, "helius_metrics.json"), "w") as f:
            json.dump({"requests": self.metrics["api_requests"][json.dumps({"api": "Helius", "status": "success"})]}, f, indent=2)
        
        # Write jito_metrics.json
        with open(os.path.join(self.output_dir, "jito_metrics.json"), "w") as f:
            json.dump({"requests": self.metrics["api_requests"][json.dumps({"api": "Jito", "status": "success"})]}, f, indent=2)
        
        # Write strategy_profiles.csv
        strategy_profiles = []
        for strategy in self.strategies:
            strategy_profiles.append({
                "strategy_id": strategy["id"],
                "name": strategy["name"],
                "win_rate": strategy["win_rate"],
                "sharpe_ratio": strategy["sharpe_ratio"],
                "composite_score": strategy["composite_score"],
                "profit_factor": random.uniform(1.2, 2.0),
                "max_drawdown": random.uniform(0.05, 0.15),
                "avg_trade": random.uniform(0.01, 0.05)
            })
        
        strategy_profiles_df = pd.DataFrame(strategy_profiles)
        strategy_profiles_df.to_csv(os.path.join(self.output_dir, "strategy_profiles.csv"), index=False)

def main():
    """Main function."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Dashboard Simulation Script")
    parser.add_argument("--output-dir", default="../output", help="Directory to write output files")
    parser.add_argument("--duration", type=int, default=60, help="Duration of the simulation in seconds")
    parser.add_argument("--interval", type=float, default=1.0, help="Interval between updates in seconds")
    
    args = parser.parse_args()
    
    # Get the path to the output directory
    output_dir = os.path.abspath(os.path.join(os.path.dirname(os.path.abspath(__file__)), args.output_dir))
    
    # Create simulator
    simulator = DashboardSimulator(
        output_dir=output_dir,
        duration_seconds=args.duration,
        update_interval=args.interval
    )
    
    # Run simulation
    simulator.run()
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
