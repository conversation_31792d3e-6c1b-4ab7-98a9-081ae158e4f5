# Synergy7 Unified Dashboard

This directory contains the Synergy7 Unified Dashboard, a Streamlit-based dashboard for monitoring and analyzing the Synergy7 Trading System.

## Overview

The Synergy7 Unified Dashboard consolidates multiple dashboard implementations into a single, unified interface. It provides a comprehensive view of the trading system, including trading metrics, system monitoring, market data, and advanced models.

## Features

- **Overview**: System status and key metrics
- **Trading Metrics**: Detailed trading performance metrics
  - PnL Metrics
  - Strategy Performance
  - Transaction History
- **Market Data**: Market information and opportunities
  - Token Scanner
  - Whale Watcher
  - Market Microstructure
- **Advanced Models**: Advanced trading models
  - Market Microstructure Analysis
  - Statistical Signal Processing
  - RL Execution
- **System Monitoring**: System health and performance
  - API Performance
  - System Resources
  - Stream Data
  - Wallet Balance
- **Settings**: Configure dashboard settings

## Directory Structure

```
unified_dashboard/
├── app.py                  # Main dashboard application
├── data_service.py         # Centralized data service
├── run_dashboard.py        # Script to run the dashboard
├── components/             # Dashboard components
│   ├── trading_metrics.py  # Trading metrics components
│   ├── system_metrics.py   # System monitoring components
│   ├── market_data.py      # Market data components
│   └── ...                 # Other components
└── README.md               # This file
```

## Data Sources

The dashboard uses the following data sources:

- `metrics.json`: System metrics
- `carbon_core_metrics.json`: Carbon Core metrics
- `tx_history.json`: Transaction history
- `enriched_signals.json`: Trading signals
- `token_opportunities.json`: Token opportunities
- `whale_opportunities.json`: Whale activities
- `strategy_metrics.json`: Strategy performance metrics
- `helius_metrics.json`: Helius API metrics
- `jito_metrics.json`: Jito API metrics

## Running the Dashboard

### Using the Run Script

```bash
# Run the dashboard with default settings
python3 run_dashboard.py

# Run the dashboard with custom settings
python3 run_dashboard.py --port 8502 --host 127.0.0.1 --browser
```

### Using the Shell Script

```bash
# Run the dashboard with default settings
../run_unified_dashboard.sh

# Run the dashboard with custom settings
../run_unified_dashboard.sh --port 8502 --host 127.0.0.1 --browser
```

### Running with Simulation

To test the dashboard with simulated data, use the simulation script:

```bash
# Run the simulation test for 1 minute
../run_simulation_test.sh --duration 60

# Run the simulation test with custom settings
../run_simulation_test.sh --duration 120 --interval 2.0 --port 8502
```

### Running with Live Trading Integration

To run the dashboard with the live trading integration:

```bash
# Run the live dashboard with default settings
../run_live_dashboard.sh

# Run the live dashboard with custom settings
../run_live_dashboard.sh --port 8502 --host 127.0.0.1
```

### Command-Line Arguments

- `--port`: Port to run Streamlit on (default: 8501)
- `--host`: Host to run Streamlit on (default: 0.0.0.0)
- `--browser`: Open browser automatically
- `--headless`: Run in headless mode

## Development

### Adding a New Component

1. Create a new component file in the `components` directory
2. Implement the component using the Streamlit API
3. Import the component in `app.py`
4. Add the component to the appropriate section in `app.py`

### Modifying Data Sources

1. Update the data service in `data_service.py`
2. Add methods to load the new data source
3. Update the `load_all_data` method to include the new data source

## Simulation

The simulation script (`simulation.py`) generates realistic trading data for testing the dashboard. It simulates a trading session with realistic market data, trades, and metrics.

### Running the Simulation

```bash
# Run the simulation directly
python3 simulation.py --output-dir ../output --duration 60 --interval 1.0

# Run the simulation with the dashboard
../run_simulation_test.sh --duration 60
```

### Simulation Parameters

- `--output-dir`: Directory to write output files (default: `../output`)
- `--duration`: Duration of the simulation in seconds (default: 60)
- `--interval`: Interval between updates in seconds (default: 1.0)

### Simulation Data

The simulation generates the following data:

- System metrics (CPU, memory, disk usage)
- API metrics (requests, response time)
- Stream data metrics (messages, status)
- Wallet balance
- Transaction history
- Trading signals
- Token opportunities
- Whale activities
- Strategy metrics
- Carbon Core metrics

## Live Trading Integration

The live trading integration (`live_trading_integration.py`) provides integration between the Synergy7 Live Trading System and the Unified Dashboard. It handles data transformation, real-time updates, and metrics collection.

### Running the Live Trading Integration

```bash
# Run the live trading integration directly
python3 ../run_live_integration.py

# Run the live trading integration with the dashboard
../run_live_dashboard.sh
```

### Integration with the Live Trading System

To integrate with the live trading system, you need to:

1. Update the `_update_metrics` method in `live_trading_integration.py` to get metrics from the live trading system
2. Update the `_update_transaction_history` method to get transaction history from the live trading system
3. Update the `_update_enriched_signals` method to get enriched signals from the live trading system
4. Update the `_update_token_opportunities` method to get token opportunities from the live trading system
5. Update the `_update_whale_opportunities` method to get whale opportunities from the live trading system
6. Update the `_update_strategy_metrics` method to get strategy metrics from the live trading system
7. Update the `_update_carbon_core_metrics` method to get Carbon Core metrics from the live trading system

The live trading integration runs in a separate thread and updates the output files at regular intervals. The dashboard reads these files to display metrics and visualizations.

## Testing

Run the tests for the unified dashboard:

```bash
python3 ../tests/test_unified_dashboard.py
```

## Troubleshooting

### Dashboard Not Loading

- Check if Streamlit is installed: `pip install streamlit`
- Check if the data files exist in the output directory
- Check the logs in `output/unified_dashboard_log.txt`

### Components Not Loading

- Check if the component files exist in the `components` directory
- Check if the component imports are correct in `app.py`
- Check if the data service is returning the expected data

## License

This project is licensed under the MIT License - see the LICENSE file for details.
