#!/usr/bin/env python3
"""
Test script for Phase 4: Adaptive Strategy Weighting.

This script tests the adaptive weight manager and strategy selector
to ensure they work correctly with the configuration-driven architecture.
"""

import os
import sys
import logging
import numpy as np
from datetime import datetime, timedelta
import yaml
import re

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import our new modules
from core.strategies.adaptive_weight_manager import AdaptiveWeightManager
from core.strategies.strategy_selector import StrategySelector

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_test_config():
    """Load test configuration with environment variable substitution."""
    try:
        # Set test environment variables
        os.environ.setdefault('ADAPTIVE_WEIGHTING_ENABLED', 'true')
        os.environ.setdefault('LEARNING_RATE', '0.05')  # Higher for testing
        os.environ.setdefault('WEIGHT_UPDATE_INTERVAL', '60')  # 1 minute for testing
        
        # Load and process config
        with open('config.yaml', 'r') as f:
            config_text = f.read()
        
        # Simple environment variable substitution
        def replace_env_var(match):
            var_expr = match.group(1)
            if ':-' in var_expr:
                var_name, default_value = var_expr.split(':-', 1)
                return os.environ.get(var_name, default_value)
            else:
                return os.environ.get(var_expr, '')
        
        config_text = re.sub(r'\$\{([^}]+)\}', replace_env_var, config_text)
        config = yaml.safe_load(config_text)
        
        # Convert string values to appropriate types
        def convert_types(obj):
            if isinstance(obj, dict):
                return {k: convert_types(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_types(item) for item in obj]
            elif isinstance(obj, str):
                if obj.lower() == 'true':
                    return True
                elif obj.lower() == 'false':
                    return False
                else:
                    try:
                        if '.' in obj:
                            return float(obj)
                        else:
                            return int(obj)
                    except ValueError:
                        return obj
            return obj
        
        config = convert_types(config)
        return config
    except Exception as e:
        logger.error(f"Error loading config: {str(e)}")
        return {}

def generate_mock_strategy_performance():
    """Generate mock strategy performance data for testing."""
    strategies = {
        'momentum_strategy': {
            'total_trades': 25,
            'net_pnl': 0.15,
            'win_rate': 0.65,
            'sharpe_ratio': 1.2,
            'max_drawdown': -0.08,
            'volatility': 0.02,
            'recent_pnl_7d': 0.03,
            'profit_factor': 1.8
        },
        'mean_reversion': {
            'total_trades': 30,
            'net_pnl': 0.12,
            'win_rate': 0.60,
            'sharpe_ratio': 0.9,
            'max_drawdown': -0.06,
            'volatility': 0.015,
            'recent_pnl_7d': 0.02,
            'profit_factor': 1.5
        },
        'breakout_strategy': {
            'total_trades': 20,
            'net_pnl': 0.08,
            'win_rate': 0.45,
            'sharpe_ratio': 0.6,
            'max_drawdown': -0.12,
            'volatility': 0.025,
            'recent_pnl_7d': 0.01,
            'profit_factor': 1.2
        },
        'scalping_strategy': {
            'total_trades': 50,
            'net_pnl': 0.05,
            'win_rate': 0.70,
            'sharpe_ratio': 0.4,
            'max_drawdown': -0.04,
            'volatility': 0.01,
            'recent_pnl_7d': 0.005,
            'profit_factor': 1.1
        },
        'swing_strategy': {
            'total_trades': 15,
            'net_pnl': -0.02,
            'win_rate': 0.40,
            'sharpe_ratio': -0.2,
            'max_drawdown': -0.15,
            'volatility': 0.03,
            'recent_pnl_7d': -0.01,
            'profit_factor': 0.8
        }
    }
    
    return strategies

def test_adaptive_weight_manager(config):
    """Test the adaptive weight manager."""
    logger.info("Testing Adaptive Weight Manager...")
    
    try:
        # Initialize manager
        manager = AdaptiveWeightManager(config)
        logger.info("Adaptive Weight Manager initialized successfully")
        
        # Generate mock performance data
        strategy_performance = generate_mock_strategy_performance()
        
        # Test performance score calculation
        performance_scores = manager.calculate_performance_scores(strategy_performance)
        logger.info("Performance Scores:")
        for strategy, score in performance_scores.items():
            logger.info(f"  {strategy}: {score:.3f}")
        
        # Test regime adjustments for different regimes
        regimes = ['trending_up', 'trending_down', 'ranging', 'volatile', 'choppy']
        
        for regime in regimes:
            regime_adjustments = manager.calculate_regime_adjustments(regime, strategy_performance)
            logger.info(f"Regime Adjustments for {regime}:")
            for strategy, adjustment in regime_adjustments.items():
                logger.info(f"  {strategy}: {adjustment:.2f}x")
        
        # Test risk adjustments
        risk_adjustments = manager.calculate_risk_adjustments(strategy_performance)
        logger.info("Risk Adjustments:")
        for strategy, adjustment in risk_adjustments.items():
            logger.info(f"  {strategy}: {adjustment:.2f}x")
        
        # Test target weight calculation
        target_weights = manager.calculate_target_weights(strategy_performance, 'trending_up')
        logger.info("Target Weights for Trending Up Regime:")
        for strategy, weight in target_weights.items():
            logger.info(f"  {strategy}: {weight:.3f}")
        
        # Test weight updates
        updated_weights = manager.update_weights(strategy_performance, 'trending_up', force_update=True)
        logger.info("Updated Weights:")
        for strategy, weight in updated_weights.items():
            logger.info(f"  {strategy}: {weight:.3f}")
        
        # Test weight recommendations
        recommendations = manager.get_weight_recommendations(strategy_performance)
        logger.info(f"Weight Recommendations: {len(recommendations)}")
        for rec in recommendations[:3]:  # Show top 3
            logger.info(f"  {rec['action']} {rec['strategy']}: {rec['current_weight']:.3f} -> {rec['target_weight']:.3f}")
        
        # Test weight summary
        summary = manager.get_weight_summary()
        logger.info(f"Weight Summary: {len(summary.get('current_weights', {}))} strategies managed")
        
        return True
    
    except Exception as e:
        logger.error(f"Error testing adaptive weight manager: {str(e)}")
        return False

def test_strategy_selector(config):
    """Test the strategy selector."""
    logger.info("Testing Strategy Selector...")
    
    try:
        # Initialize selector
        selector = StrategySelector(config)
        logger.info("Strategy Selector initialized successfully")
        
        # Register test strategies
        test_strategies = {
            'momentum_strategy': {
                'enabled': True,
                'min_confidence': 0.5,
                'preferred_regimes': ['trending_up', 'trending_down'],
                'risk_level': 'medium'
            },
            'mean_reversion': {
                'enabled': True,
                'min_confidence': 0.4,
                'preferred_regimes': ['ranging'],
                'risk_level': 'low'
            },
            'breakout_strategy': {
                'enabled': True,
                'min_confidence': 0.6,
                'preferred_regimes': ['trending_up', 'volatile'],
                'risk_level': 'high'
            },
            'scalping_strategy': {
                'enabled': True,
                'min_confidence': 0.3,
                'preferred_regimes': ['ranging'],
                'risk_level': 'low'
            },
            'swing_strategy': {
                'enabled': False,  # Disabled due to poor performance
                'min_confidence': 0.5,
                'preferred_regimes': ['trending_up'],
                'risk_level': 'medium'
            }
        }
        
        for strategy_name, strategy_config in test_strategies.items():
            selector.register_strategy(strategy_name, strategy_config)
        
        logger.info(f"Registered {len(test_strategies)} strategies")
        
        # Generate performance data and weights
        strategy_performance = generate_mock_strategy_performance()
        strategy_weights = {
            'momentum_strategy': 0.3,
            'mean_reversion': 0.25,
            'breakout_strategy': 0.2,
            'scalping_strategy': 0.2,
            'swing_strategy': 0.05
        }
        
        # Test strategy suitability calculation
        logger.info("Strategy Suitability for Trending Up Regime:")
        for strategy_name in test_strategies.keys():
            suitability = selector.calculate_strategy_suitability(
                strategy_name, 'trending_up', 0.8, strategy_performance.get(strategy_name, {})
            )
            logger.info(f"  {strategy_name}: {suitability:.3f}")
        
        # Test strategy selection for different regimes
        regimes_to_test = ['trending_up', 'ranging', 'volatile']
        
        for regime in regimes_to_test:
            selected = selector.select_strategies(
                regime, 0.8, strategy_weights, strategy_performance
            )
            logger.info(f"Selected Strategies for {regime} Regime: {len(selected)}")
            for strategy in selected:
                logger.info(f"  {strategy['strategy_name']}: {strategy['effective_allocation']:.3f} allocation")
        
        # Test strategy recommendations
        recommendations = selector.get_strategy_recommendations('trending_up', strategy_performance)
        logger.info("Strategy Recommendations for Trending Up:")
        for rec in recommendations:
            logger.info(f"  {rec['strategy_name']}: {rec['recommendation']} (suitability: {rec['suitability_score']:.3f})")
        
        # Test selection summary
        summary = selector.get_selection_summary()
        logger.info(f"Selection Summary:")
        logger.info(f"  Total Registered: {summary.get('total_registered_strategies', 0)}")
        logger.info(f"  Active Strategies: {summary.get('active_strategies', 0)}")
        logger.info(f"  Active Names: {summary.get('active_strategy_names', [])}")
        
        return True
    
    except Exception as e:
        logger.error(f"Error testing strategy selector: {str(e)}")
        return False

def test_integration(config):
    """Test integration between adaptive weight manager and strategy selector."""
    logger.info("Testing Integration...")
    
    try:
        # Initialize both components
        weight_manager = AdaptiveWeightManager(config)
        strategy_selector = StrategySelector(config)
        
        # Register strategies
        test_strategies = {
            'momentum_strategy': {
                'enabled': True,
                'preferred_regimes': ['trending_up'],
                'risk_level': 'medium'
            },
            'mean_reversion': {
                'enabled': True,
                'preferred_regimes': ['ranging'],
                'risk_level': 'low'
            },
            'breakout_strategy': {
                'enabled': True,
                'preferred_regimes': ['trending_up', 'volatile'],
                'risk_level': 'high'
            }
        }
        
        for name, config_data in test_strategies.items():
            strategy_selector.register_strategy(name, config_data)
        
        # Generate performance data
        strategy_performance = generate_mock_strategy_performance()
        
        # Test workflow: weights -> selection -> performance feedback
        logger.info("Integration Workflow Test:")
        
        # 1. Calculate initial weights
        initial_weights = weight_manager.update_weights(strategy_performance, 'trending_up', force_update=True)
        logger.info(f"1. Initial weights calculated: {len(initial_weights)} strategies")
        
        # 2. Select strategies based on weights
        selected_strategies = strategy_selector.select_strategies(
            'trending_up', 0.8, initial_weights, strategy_performance
        )
        logger.info(f"2. Selected strategies: {len(selected_strategies)} active")
        
        # 3. Simulate performance feedback and weight adjustment
        # Modify performance to simulate changing conditions
        modified_performance = strategy_performance.copy()
        modified_performance['momentum_strategy']['recent_pnl_7d'] = 0.05  # Improved
        modified_performance['breakout_strategy']['recent_pnl_7d'] = -0.01  # Declined
        
        # 4. Update weights based on new performance
        updated_weights = weight_manager.update_weights(modified_performance, 'trending_up', force_update=True)
        logger.info(f"3. Updated weights after performance feedback")
        
        # 5. Show weight changes
        logger.info("Weight Changes:")
        for strategy in initial_weights.keys():
            initial = initial_weights.get(strategy, 0)
            updated = updated_weights.get(strategy, 0)
            change = updated - initial
            logger.info(f"  {strategy}: {initial:.3f} -> {updated:.3f} (change: {change:+.3f})")
        
        # 6. Final selection with updated weights
        final_selection = strategy_selector.select_strategies(
            'trending_up', 0.8, updated_weights, modified_performance
        )
        logger.info(f"4. Final selection: {len(final_selection)} strategies")
        
        return True
    
    except Exception as e:
        logger.error(f"Error testing integration: {str(e)}")
        return False

def main():
    """Main test function."""
    logger.info("Starting Phase 4 Adaptive Strategy Weighting Tests...")
    
    # Load configuration
    config = load_test_config()
    if not config:
        logger.error("Failed to load configuration")
        return False
    
    logger.info("Configuration loaded successfully")
    
    # Test results
    results = {}
    
    # Test adaptive weight manager
    results['adaptive_weight_manager'] = test_adaptive_weight_manager(config)
    
    # Test strategy selector
    results['strategy_selector'] = test_strategy_selector(config)
    
    # Test integration
    results['integration'] = test_integration(config)
    
    # Print results summary
    logger.info("\n" + "="*50)
    logger.info("PHASE 4 ADAPTIVE STRATEGY WEIGHTING TEST RESULTS")
    logger.info("="*50)
    
    all_passed = True
    for test_name, passed in results.items():
        status = "PASS" if passed else "FAIL"
        logger.info(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    logger.info("="*50)
    overall_status = "ALL TESTS PASSED" if all_passed else "SOME TESTS FAILED"
    logger.info(f"Overall Status: {overall_status}")
    logger.info("="*50)
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
