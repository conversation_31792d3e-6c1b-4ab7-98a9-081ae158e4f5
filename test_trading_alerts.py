#!/usr/bin/env python3
"""
Test Trading Alerts

This script tests the trading alerts functionality, including trade notifications,
performance metrics, and system metrics.
"""

import os
import sys
import json
import asyncio
import logging
from datetime import datetime, timedelta
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("test_trading_alerts")

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import trading alerts module
from phase_4_deployment.utils.trading_alerts import (
    TradingAlerts,
    send_trade_alert,
    send_metrics_alert,
    send_system_alert
)

async def test_trading_alerts():
    """Test trading alerts."""
    # Use hardcoded credentials for testing
    # In production, these would be loaded from environment variables
    telegram_bot_token = "**********************************************"
    telegram_chat_id = "5135869709"

    if not telegram_bot_token or telegram_bot_token == "your_telegram_bot_token_here":
        logger.error("Telegram bot token not configured or is set to default value")
        logger.info("Please set TELEGRAM_BOT_TOKEN in .env.paper")
        return False

    if not telegram_chat_id or telegram_chat_id == "your_telegram_chat_id_here":
        logger.error("Telegram chat ID not configured or is set to default value")
        logger.info("Please set TELEGRAM_CHAT_ID in .env.paper")
        return False

    logger.info(f"Using Telegram bot token: {telegram_bot_token[:5]}...{telegram_bot_token[-5:]}")
    logger.info(f"Using Telegram chat ID: {telegram_chat_id}")

    # Create trading alerts instance
    alerts = TradingAlerts(telegram_bot_token, telegram_chat_id)

    # Update wallet balance
    alerts.update_wallet_balance(1000.0, is_initial=True)

    # Test trade notification - Buy
    logger.info("Sending buy trade notification...")
    buy_trade = {
        'action': 'BUY',
        'market': 'SOL-USDC',
        'price': 150.25,
        'size': 2.5,
        'confidence': 0.85,
        'timestamp': datetime.now().isoformat()
    }

    await alerts.send_trade_notification(buy_trade)

    # Wait for alert to be sent
    await asyncio.sleep(2)

    # Test trade notification - Sell (profitable)
    logger.info("Sending sell trade notification (profitable)...")
    sell_trade = {
        'action': 'SELL',
        'market': 'SOL-USDC',
        'price': 155.75,
        'size': 2.5,
        'confidence': 0.78,
        'timestamp': (datetime.now() + timedelta(hours=2)).isoformat()
    }

    await alerts.send_trade_notification(sell_trade)

    # Wait for alert to be sent
    await asyncio.sleep(2)

    # Test trade notification - Buy again
    logger.info("Sending another buy trade notification...")
    buy_trade2 = {
        'action': 'BUY',
        'market': 'SOL-USDC',
        'price': 154.50,
        'size': 1.8,
        'confidence': 0.72,
        'timestamp': (datetime.now() + timedelta(hours=4)).isoformat()
    }

    await alerts.send_trade_notification(buy_trade2)

    # Wait for alert to be sent
    await asyncio.sleep(2)

    # Test trade notification - Sell (loss)
    logger.info("Sending sell trade notification (loss)...")
    sell_trade2 = {
        'action': 'SELL',
        'market': 'SOL-USDC',
        'price': 152.25,
        'size': 1.8,
        'confidence': 0.65,
        'timestamp': (datetime.now() + timedelta(hours=6)).isoformat()
    }

    await alerts.send_trade_notification(sell_trade2)

    # Wait for alert to be sent
    await asyncio.sleep(2)

    # Update max drawdown
    alerts.update_max_drawdown(5.2)

    # Test performance metrics
    logger.info("Sending performance metrics...")
    await alerts.send_performance_metrics()

    # Wait for alert to be sent
    await asyncio.sleep(2)

    # Test system metrics
    logger.info("Sending system metrics...")
    system_data = {
        'signals_generated': 42,
        'signals_filtered': 28,
        'components': {
            'Filter Chain': True,
            'Signal Enricher': True,
            'RL Data Collector': True,
            'Execution Engine': True,
            'Monitoring Service': True
        }
    }

    await alerts.send_system_metrics(system_data)

    # Wait for alert to be sent
    await asyncio.sleep(2)

    # Close the HTTP client
    await alerts.close()

    logger.info("Trading alerts test completed. Please check your Telegram for messages.")
    return True

async def main():
    """Main function."""
    logger.info("Testing trading alerts...")

    success = await test_trading_alerts()

    if success:
        logger.info("Trading alerts test completed. Please check your Telegram for messages.")
    else:
        logger.error("Trading alerts test failed. Please check the configuration.")

if __name__ == "__main__":
    asyncio.run(main())
