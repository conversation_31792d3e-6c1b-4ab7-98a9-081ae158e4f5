"""
Enhanced Risk Management for Synergy7 Trading System.

This module implements enhanced risk management features including dynamic position sizing,
improved stop-loss mechanisms, and circuit breakers.
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from enum import Enum

# Configure logging
logger = logging.getLogger(__name__)

class CircuitBreakerState(Enum):
    """Circuit breaker states."""
    NORMAL = "normal"
    WARNING = "warning"
    TRIGGERED = "triggered"


class RiskManager:
    """
    Enhanced Risk Manager.
    
    This class provides risk management features including position sizing,
    stop-loss calculation, and circuit breakers.
    """
    
    def __init__(self, **kwargs):
        """
        Initialize the risk manager.
        
        Args:
            **kwargs: Configuration parameters
        """
        # Position sizing parameters
        self.position_sizing_method = kwargs.get("position_sizing_method", "risk_based")
        self.max_position_size_pct = kwargs.get("max_position_size_pct", 0.05)  # 5% of portfolio
        self.max_position_size_usd = kwargs.get("max_position_size_usd", 1000)
        self.risk_per_trade_pct = kwargs.get("risk_per_trade_pct", 0.01)  # 1% risk per trade
        
        # Stop-loss parameters
        self.stop_loss_pct = kwargs.get("stop_loss_pct", 0.05)  # 5% stop loss
        self.trailing_stop_pct = kwargs.get("trailing_stop_pct", 0.02)  # 2% trailing stop
        self.atr_multiplier = kwargs.get("atr_multiplier", 1.5)
        self.atr_period = kwargs.get("atr_period", 14)
        self.time_stop_minutes = kwargs.get("time_stop_minutes", 60)  # Exit after 60 minutes if not profitable
        
        # Take-profit parameters
        self.take_profit_pct = kwargs.get("take_profit_pct", 0.10)  # 10% take profit
        self.partial_take_profit_pct = kwargs.get("partial_take_profit_pct", 0.05)  # 5% partial take profit
        self.partial_take_profit_size_pct = kwargs.get("partial_take_profit_size_pct", 0.33)  # Exit 33% at partial take profit
        
        # Circuit breaker parameters
        self.circuit_breaker_enabled = kwargs.get("circuit_breaker_enabled", True)
        self.daily_loss_limit_pct = kwargs.get("daily_loss_limit_pct", 0.05)  # 5% daily loss limit
        self.max_consecutive_losses = kwargs.get("max_consecutive_losses", 3)
        self.circuit_breaker_cooldown_minutes = kwargs.get("circuit_breaker_cooldown_minutes", 60)
        
        # Portfolio parameters
        self.max_open_positions = kwargs.get("max_open_positions", 5)
        self.max_exposure_per_asset_pct = kwargs.get("max_exposure_per_asset_pct", 0.20)  # 20% max exposure per asset
        self.correlation_threshold = kwargs.get("correlation_threshold", 0.7)
        
        # State variables
        self.daily_pnl = 0.0
        self.consecutive_losses = 0
        self.circuit_breaker_state = CircuitBreakerState.NORMAL
        self.circuit_breaker_triggered_time = None
        self.open_positions = {}
        self.trade_history = []
        
        logger.info("Initialized enhanced risk manager")
    
    def calculate_position_size(self, price: float, stop_loss_price: float, 
                               portfolio_value: float, market_data: Dict[str, Any] = None) -> float:
        """
        Calculate position size based on risk management rules.
        
        Args:
            price: Current price
            stop_loss_price: Stop loss price
            portfolio_value: Current portfolio value
            market_data: Additional market data
            
        Returns:
            Position size in base currency
        """
        # Check circuit breaker
        if self.circuit_breaker_state == CircuitBreakerState.TRIGGERED:
            logger.warning("Circuit breaker triggered, position size set to 0")
            return 0.0
        
        # Calculate risk amount
        risk_amount = portfolio_value * self.risk_per_trade_pct
        
        # Calculate position size based on method
        if self.position_sizing_method == "fixed":
            # Fixed position size
            position_size_usd = min(self.max_position_size_usd, portfolio_value * self.max_position_size_pct)
            position_size = position_size_usd / price
        
        elif self.position_sizing_method == "percentage":
            # Percentage of portfolio
            position_size_usd = portfolio_value * self.max_position_size_pct
            position_size = position_size_usd / price
        
        elif self.position_sizing_method == "risk_based":
            # Risk-based position sizing
            if price == stop_loss_price:
                logger.warning("Price equals stop loss price, using default position size")
                position_size_usd = min(self.max_position_size_usd, portfolio_value * self.max_position_size_pct)
                position_size = position_size_usd / price
            else:
                # Calculate risk per unit
                risk_per_unit = abs(price - stop_loss_price)
                
                # Calculate position size
                if risk_per_unit > 0:
                    position_size_usd = risk_amount / (risk_per_unit / price)
                    
                    # Apply maximum limits
                    position_size_usd = min(position_size_usd, self.max_position_size_usd, 
                                          portfolio_value * self.max_position_size_pct)
                    
                    position_size = position_size_usd / price
                else:
                    logger.warning("Risk per unit is zero, using default position size")
                    position_size_usd = min(self.max_position_size_usd, portfolio_value * self.max_position_size_pct)
                    position_size = position_size_usd / price
        
        else:
            logger.warning(f"Unknown position sizing method: {self.position_sizing_method}, using default")
            position_size_usd = min(self.max_position_size_usd, portfolio_value * self.max_position_size_pct)
            position_size = position_size_usd / price
        
        # Check if we have too many open positions
        if len(self.open_positions) >= self.max_open_positions:
            logger.warning(f"Maximum number of open positions reached ({self.max_open_positions}), position size set to 0")
            return 0.0
        
        # Check if we have too much exposure to this asset
        asset_exposure = sum(pos["size"] * pos["price"] for pos in self.open_positions.values() 
                           if pos["symbol"] == market_data.get("symbol", ""))
        
        if asset_exposure + (position_size * price) > portfolio_value * self.max_exposure_per_asset_pct:
            logger.warning(f"Maximum exposure to asset reached ({self.max_exposure_per_asset_pct * 100}%), adjusting position size")
            max_additional_exposure = (portfolio_value * self.max_exposure_per_asset_pct) - asset_exposure
            position_size = max(0, max_additional_exposure / price)
        
        return position_size
    
    def calculate_stop_loss(self, price: float, direction: int, market_data: Dict[str, Any] = None) -> float:
        """
        Calculate stop loss price based on risk management rules.
        
        Args:
            price: Current price
            direction: Trade direction (1 for long, -1 for short)
            market_data: Additional market data
            
        Returns:
            Stop loss price
        """
        # Default stop loss
        stop_loss_price = price * (1 - self.stop_loss_pct * direction)
        
        # Use ATR-based stop loss if available
        if market_data and "atr" in market_data:
            atr = market_data["atr"]
            stop_loss_price = price - (atr * self.atr_multiplier * direction)
        
        return stop_loss_price
    
    def calculate_take_profit(self, price: float, direction: int, market_data: Dict[str, Any] = None) -> Dict[str, float]:
        """
        Calculate take profit levels based on risk management rules.
        
        Args:
            price: Current price
            direction: Trade direction (1 for long, -1 for short)
            market_data: Additional market data
            
        Returns:
            Dictionary with take profit levels
        """
        # Default take profit
        take_profit_price = price * (1 + self.take_profit_pct * direction)
        
        # Partial take profit
        partial_take_profit_price = price * (1 + self.partial_take_profit_pct * direction)
        
        return {
            "full": take_profit_price,
            "partial": partial_take_profit_price,
            "partial_size_pct": self.partial_take_profit_size_pct
        }
    
    def update_trailing_stop(self, position_id: str, current_price: float) -> Optional[float]:
        """
        Update trailing stop for a position.
        
        Args:
            position_id: Position ID
            current_price: Current price
            
        Returns:
            New stop loss price or None if position not found
        """
        if position_id not in self.open_positions:
            logger.warning(f"Position {position_id} not found")
            return None
        
        position = self.open_positions[position_id]
        direction = 1 if position["direction"] == "long" else -1
        
        # Calculate new stop loss based on trailing stop
        if direction == 1:  # Long position
            # Calculate new stop loss
            new_stop_loss = current_price * (1 - self.trailing_stop_pct)
            
            # Update stop loss if it's higher than the current stop loss
            if new_stop_loss > position["stop_loss"]:
                position["stop_loss"] = new_stop_loss
                logger.info(f"Updated trailing stop for position {position_id} to {new_stop_loss:.4f}")
        
        else:  # Short position
            # Calculate new stop loss
            new_stop_loss = current_price * (1 + self.trailing_stop_pct)
            
            # Update stop loss if it's lower than the current stop loss
            if new_stop_loss < position["stop_loss"]:
                position["stop_loss"] = new_stop_loss
                logger.info(f"Updated trailing stop for position {position_id} to {new_stop_loss:.4f}")
        
        return position["stop_loss"]
    
    def check_time_stop(self, position_id: str, current_time: datetime, current_price: float) -> bool:
        """
        Check if time-based stop loss should be triggered.
        
        Args:
            position_id: Position ID
            current_time: Current time
            current_price: Current price
            
        Returns:
            True if time stop triggered, False otherwise
        """
        if position_id not in self.open_positions:
            logger.warning(f"Position {position_id} not found")
            return False
        
        position = self.open_positions[position_id]
        
        # Check if position has been open for too long
        entry_time = datetime.fromisoformat(position["entry_time"])
        time_open = (current_time - entry_time).total_seconds() / 60  # in minutes
        
        if time_open > self.time_stop_minutes:
            # Check if position is profitable
            direction = 1 if position["direction"] == "long" else -1
            profit_pct = (current_price - position["price"]) / position["price"] * direction
            
            if profit_pct <= 0:
                logger.info(f"Time stop triggered for position {position_id} after {time_open:.1f} minutes")
                return True
        
        return False
    
    def check_circuit_breaker(self, portfolio_value: float) -> bool:
        """
        Check if circuit breaker should be triggered.
        
        Args:
            portfolio_value: Current portfolio value
            
        Returns:
            True if circuit breaker triggered, False otherwise
        """
        if not self.circuit_breaker_enabled:
            return False
        
        # Check if circuit breaker is already triggered
        if self.circuit_breaker_state == CircuitBreakerState.TRIGGERED:
            # Check if cooldown period has elapsed
            if self.circuit_breaker_triggered_time:
                time_since_trigger = (datetime.now() - self.circuit_breaker_triggered_time).total_seconds() / 60
                
                if time_since_trigger > self.circuit_breaker_cooldown_minutes:
                    logger.info(f"Circuit breaker cooldown period elapsed ({self.circuit_breaker_cooldown_minutes} minutes)")
                    self.circuit_breaker_state = CircuitBreakerState.NORMAL
                    self.circuit_breaker_triggered_time = None
                    return False
            
            return True
        
        # Check daily loss limit
        if abs(self.daily_pnl) > portfolio_value * self.daily_loss_limit_pct:
            logger.warning(f"Daily loss limit reached: {self.daily_pnl:.2f} > {portfolio_value * self.daily_loss_limit_pct:.2f}")
            self.circuit_breaker_state = CircuitBreakerState.TRIGGERED
            self.circuit_breaker_triggered_time = datetime.now()
            return True
        
        # Check consecutive losses
        if self.consecutive_losses >= self.max_consecutive_losses:
            logger.warning(f"Maximum consecutive losses reached: {self.consecutive_losses} >= {self.max_consecutive_losses}")
            self.circuit_breaker_state = CircuitBreakerState.TRIGGERED
            self.circuit_breaker_triggered_time = datetime.now()
            return True
        
        return False
    
    def open_position(self, symbol: str, direction: str, price: float, size: float, 
                     stop_loss: float, take_profit: Dict[str, float], 
                     entry_time: datetime = None) -> str:
        """
        Open a new position.
        
        Args:
            symbol: Trading symbol
            direction: Trade direction ("long" or "short")
            price: Entry price
            size: Position size
            stop_loss: Stop loss price
            take_profit: Take profit levels
            entry_time: Entry time (defaults to current time)
            
        Returns:
            Position ID
        """
        # Generate position ID
        position_id = f"{symbol}_{direction}_{datetime.now().strftime('%Y%m%d%H%M%S')}"
        
        # Create position
        position = {
            "id": position_id,
            "symbol": symbol,
            "direction": direction,
            "price": price,
            "size": size,
            "stop_loss": stop_loss,
            "take_profit": take_profit,
            "entry_time": entry_time.isoformat() if entry_time else datetime.now().isoformat(),
            "partial_exits": []
        }
        
        # Add position to open positions
        self.open_positions[position_id] = position
        
        logger.info(f"Opened {direction} position {position_id} for {symbol} at {price:.4f} with size {size:.4f}")
        
        return position_id
    
    def close_position(self, position_id: str, price: float, exit_time: datetime = None, 
                      reason: str = "manual") -> Dict[str, Any]:
        """
        Close an open position.
        
        Args:
            position_id: Position ID
            price: Exit price
            exit_time: Exit time (defaults to current time)
            reason: Reason for closing position
            
        Returns:
            Closed position details
        """
        if position_id not in self.open_positions:
            logger.warning(f"Position {position_id} not found")
            return {}
        
        # Get position
        position = self.open_positions[position_id]
        
        # Calculate profit/loss
        direction = 1 if position["direction"] == "long" else -1
        profit_loss = (price - position["price"]) * position["size"] * direction
        profit_loss_pct = (price - position["price"]) / position["price"] * direction
        
        # Update daily P&L
        self.daily_pnl += profit_loss
        
        # Update consecutive losses
        if profit_loss < 0:
            self.consecutive_losses += 1
        else:
            self.consecutive_losses = 0
        
        # Create trade record
        trade = {
            "id": position_id,
            "symbol": position["symbol"],
            "direction": position["direction"],
            "entry_price": position["price"],
            "exit_price": price,
            "size": position["size"],
            "entry_time": position["entry_time"],
            "exit_time": exit_time.isoformat() if exit_time else datetime.now().isoformat(),
            "profit_loss": profit_loss,
            "profit_loss_pct": profit_loss_pct,
            "reason": reason,
            "partial_exits": position["partial_exits"]
        }
        
        # Add trade to history
        self.trade_history.append(trade)
        
        # Remove position from open positions
        del self.open_positions[position_id]
        
        logger.info(f"Closed {position['direction']} position {position_id} for {position['symbol']} at {price:.4f} with P&L {profit_loss:.4f} ({profit_loss_pct:.2%})")
        
        return trade
    
    def partial_exit(self, position_id: str, price: float, size_pct: float, 
                    exit_time: datetime = None, reason: str = "partial_take_profit") -> Dict[str, Any]:
        """
        Partially exit a position.
        
        Args:
            position_id: Position ID
            price: Exit price
            size_pct: Percentage of position to exit
            exit_time: Exit time (defaults to current time)
            reason: Reason for partial exit
            
        Returns:
            Partial exit details
        """
        if position_id not in self.open_positions:
            logger.warning(f"Position {position_id} not found")
            return {}
        
        # Get position
        position = self.open_positions[position_id]
        
        # Calculate exit size
        exit_size = position["size"] * size_pct
        
        # Calculate profit/loss
        direction = 1 if position["direction"] == "long" else -1
        profit_loss = (price - position["price"]) * exit_size * direction
        profit_loss_pct = (price - position["price"]) / position["price"] * direction
        
        # Update daily P&L
        self.daily_pnl += profit_loss
        
        # Create partial exit record
        partial_exit = {
            "price": price,
            "size": exit_size,
            "size_pct": size_pct,
            "time": exit_time.isoformat() if exit_time else datetime.now().isoformat(),
            "profit_loss": profit_loss,
            "profit_loss_pct": profit_loss_pct,
            "reason": reason
        }
        
        # Add partial exit to position
        position["partial_exits"].append(partial_exit)
        
        # Update position size
        position["size"] -= exit_size
        
        logger.info(f"Partially exited {position['direction']} position {position_id} for {position['symbol']} at {price:.4f} with size {exit_size:.4f} ({size_pct:.2%}) and P&L {profit_loss:.4f} ({profit_loss_pct:.2%})")
        
        return partial_exit
    
    def reset_daily_metrics(self) -> None:
        """Reset daily metrics."""
        self.daily_pnl = 0.0
        logger.info("Reset daily metrics")
    
    def get_portfolio_stats(self) -> Dict[str, Any]:
        """
        Get portfolio statistics.
        
        Returns:
            Dictionary with portfolio statistics
        """
        # Calculate total position value
        total_position_value = sum(pos["size"] * pos["price"] for pos in self.open_positions.values())
        
        # Calculate win rate
        total_trades = len(self.trade_history)
        winning_trades = sum(1 for trade in self.trade_history if trade["profit_loss"] > 0)
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        # Calculate profit factor
        gross_profit = sum(trade["profit_loss"] for trade in self.trade_history if trade["profit_loss"] > 0)
        gross_loss = sum(abs(trade["profit_loss"]) for trade in self.trade_history if trade["profit_loss"] < 0)
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')
        
        # Calculate average trade
        avg_trade = sum(trade["profit_loss"] for trade in self.trade_history) / total_trades if total_trades > 0 else 0
        
        return {
            "open_positions": len(self.open_positions),
            "total_position_value": total_position_value,
            "daily_pnl": self.daily_pnl,
            "consecutive_losses": self.consecutive_losses,
            "circuit_breaker_state": self.circuit_breaker_state.value,
            "total_trades": total_trades,
            "winning_trades": winning_trades,
            "win_rate": win_rate,
            "profit_factor": profit_factor,
            "avg_trade": avg_trade
        }
