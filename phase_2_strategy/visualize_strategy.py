#!/usr/bin/env python3
"""
Strategy Visualization Script for Synergy7 Trading System.

This script demonstrates how to use the visualization module to generate
comprehensive visual statistics for strategy validation, parameter optimization,
and performance analysis in small incremental calls.
"""

import os
import sys
import json
import logging
import argparse
import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime

# Add parent directory to path
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

# Import visualization module
from phase_2_strategy.visualization import StrategyVisualizer

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(os.path.join(parent_dir, "logs", "visualize_strategy.log")),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("visualize_strategy")

def load_backtest_results(results_path: str) -> Dict[str, Any]:
    """
    Load backtest results from JSON file.

    Args:
        results_path: Path to backtest results JSON

    Returns:
        Dictionary with backtest results
    """
    logger.info(f"Loading backtest results from {results_path}")

    try:
        with open(results_path, 'r') as f:
            results = json.load(f)

        logger.info(f"Loaded backtest results with {len(results.get('trades', []))} trades")
        return results

    except Exception as e:
        logger.error(f"Error loading backtest results: {str(e)}")
        raise

def load_optimization_results(results_path: str) -> List[Dict[str, Any]]:
    """
    Load optimization results from JSON file.

    Args:
        results_path: Path to optimization results JSON

    Returns:
        List of optimization results
    """
    logger.info(f"Loading optimization results from {results_path}")

    try:
        with open(results_path, 'r') as f:
            results = json.load(f)

        logger.info(f"Loaded optimization results with {len(results)} parameter combinations")
        return results

    except Exception as e:
        logger.error(f"Error loading optimization results: {str(e)}")
        raise

def load_market_regime_results(results_path: str) -> Dict[str, Any]:
    """
    Load market regime results from JSON file.

    Args:
        results_path: Path to market regime results JSON

    Returns:
        Dictionary with market regime results
    """
    logger.info(f"Loading market regime results from {results_path}")

    try:
        with open(results_path, 'r') as f:
            results = json.load(f)

        logger.info(f"Loaded market regime results with {len(results.get('regimes', []))} regime periods")
        return results

    except Exception as e:
        logger.error(f"Error loading market regime results: {str(e)}")
        raise

def prepare_portfolio_dataframe(backtest_results: Dict[str, Any]) -> pd.DataFrame:
    """
    Prepare portfolio DataFrame from backtest results.

    Args:
        backtest_results: Dictionary with backtest results

    Returns:
        DataFrame with portfolio data
    """
    logger.info("Preparing portfolio DataFrame")

    try:
        # Check if portfolio data is already in DataFrame format
        if 'portfolio' in backtest_results:
            portfolio_data = backtest_results['portfolio']

            if isinstance(portfolio_data, dict):
                # Convert dictionary to DataFrame
                portfolio_df = pd.DataFrame(portfolio_data)

                # Convert index to datetime if it's not already
                if not isinstance(portfolio_df.index, pd.DatetimeIndex):
                    portfolio_df.index = pd.to_datetime(portfolio_df.index)

                logger.info(f"Prepared portfolio DataFrame with {len(portfolio_df)} rows")
                return portfolio_df

            elif isinstance(portfolio_data, list):
                # Convert list of dictionaries to DataFrame
                portfolio_df = pd.DataFrame(portfolio_data)

                # Convert timestamp to datetime if it exists
                if 'timestamp' in portfolio_df.columns:
                    portfolio_df['timestamp'] = pd.to_datetime(portfolio_df['timestamp'])
                    portfolio_df.set_index('timestamp', inplace=True)

                logger.info(f"Prepared portfolio DataFrame with {len(portfolio_df)} rows")
                return portfolio_df

        # If portfolio data is not available, try to reconstruct from trades
        if 'trades' in backtest_results:
            trades = backtest_results['trades']

            # Extract initial and final capital
            initial_capital = backtest_results.get('initial_capital', 10000)
            final_capital = backtest_results.get('final_capital', initial_capital)

            # Create a simple equity curve based on trades
            dates = []
            equity = []

            # Add initial point
            if trades and 'entry_date' in trades[0]:
                start_date = pd.to_datetime(trades[0]['entry_date'])
                dates.append(start_date - pd.Timedelta(days=1))
                equity.append(initial_capital)

            # Add points for each trade
            cumulative_pnl = 0
            for trade in trades:
                if 'exit_date' in trade and 'profit_loss' in trade:
                    exit_date = pd.to_datetime(trade['exit_date'])
                    cumulative_pnl += trade['profit_loss']
                    dates.append(exit_date)
                    equity.append(initial_capital + cumulative_pnl)

            # Create DataFrame
            portfolio_df = pd.DataFrame({'equity': equity}, index=dates)

            # Sort by date
            portfolio_df.sort_index(inplace=True)

            # Calculate returns
            portfolio_df['returns'] = portfolio_df['equity'].pct_change()

            # Calculate drawdown
            portfolio_df['drawdown'] = portfolio_df['equity'] / portfolio_df['equity'].cummax() - 1

            logger.info(f"Reconstructed portfolio DataFrame with {len(portfolio_df)} rows")
            return portfolio_df

        logger.error("Could not prepare portfolio DataFrame from backtest results")
        raise ValueError("Could not prepare portfolio DataFrame from backtest results")

    except Exception as e:
        logger.error(f"Error preparing portfolio DataFrame: {str(e)}")
        raise

def prepare_returns_series(portfolio_df: pd.DataFrame) -> pd.Series:
    """
    Prepare returns series from portfolio DataFrame.

    Args:
        portfolio_df: DataFrame with portfolio data

    Returns:
        Series of returns
    """
    logger.info("Preparing returns series")

    try:
        # Check if returns are already in DataFrame
        if 'returns' in portfolio_df.columns:
            returns = portfolio_df['returns']
        else:
            # Calculate returns from equity or total
            if 'equity' in portfolio_df.columns:
                returns = portfolio_df['equity'].pct_change()
            elif 'total' in portfolio_df.columns:
                returns = portfolio_df['total'].pct_change()
            else:
                # Use first column
                returns = portfolio_df.iloc[:, 0].pct_change()

        # Remove NaN values
        returns = returns.dropna()

        logger.info(f"Prepared returns series with {len(returns)} values")
        return returns

    except Exception as e:
        logger.error(f"Error preparing returns series: {str(e)}")
        raise

def prepare_regimes_series(market_regime_results: Dict[str, Any], returns_index: pd.DatetimeIndex) -> pd.Series:
    """
    Prepare market regimes series aligned with returns index.

    Args:
        market_regime_results: Dictionary with market regime results
        returns_index: DatetimeIndex to align with

    Returns:
        Series of market regimes
    """
    logger.info("Preparing market regimes series")

    try:
        # Extract regime periods
        regimes = market_regime_results.get('regimes', [])

        if not regimes:
            logger.warning("No regime periods found in market regime results")
            return pd.Series(index=returns_index)

        # Create a series with regime for each date
        regime_dict = {}

        for regime_period in regimes:
            regime = regime_period.get('regime')
            start = pd.to_datetime(regime_period.get('start'))
            end = pd.to_datetime(regime_period.get('end'))

            # Create date range
            date_range = pd.date_range(start=start, end=end, freq='D')

            # Add to dictionary
            for date in date_range:
                regime_dict[date] = regime

        # Create series
        regime_series = pd.Series(regime_dict)

        # Align with returns index
        aligned_regimes = pd.Series(index=returns_index)

        for date in returns_index:
            # Find closest date in regime_series
            closest_date = regime_series.index[regime_series.index.get_indexer([date], method='nearest')[0]]
            aligned_regimes[date] = regime_series[closest_date]

        logger.info(f"Prepared market regimes series with {len(aligned_regimes)} values")
        return aligned_regimes

    except Exception as e:
        logger.error(f"Error preparing market regimes series: {str(e)}")
        raise

def visualize_performance(backtest_results: Dict[str, Any], output_dir: str, dark_mode: bool = False) -> None:
    """
    Visualize strategy performance.

    Args:
        backtest_results: Dictionary with backtest results
        output_dir: Directory to save visualizations
        dark_mode: Whether to use dark mode for visualizations
    """
    logger.info("Visualizing strategy performance")

    try:
        # Create visualizer
        visualizer = StrategyVisualizer(output_dir, dark_mode)

        # Prepare portfolio DataFrame
        portfolio_df = prepare_portfolio_dataframe(backtest_results)

        # Prepare returns series
        returns = prepare_returns_series(portfolio_df)

        # Visualize equity curve
        visualizer.plot_equity_curve(portfolio_df, title="Strategy Equity Curve", filename="equity_curve")

        # Visualize drawdown
        visualizer.plot_drawdown(portfolio_df, title="Strategy Drawdown", filename="drawdown")

        # Visualize returns distribution
        visualizer.plot_returns_distribution(returns, title="Returns Distribution", filename="returns_distribution")

        # Visualize monthly returns heatmap
        visualizer.plot_monthly_returns_heatmap(returns, title="Monthly Returns", filename="monthly_returns_heatmap")

        # Visualize rolling Sharpe ratio
        visualizer.plot_rolling_sharpe(returns, window=252, title="Rolling Sharpe Ratio (252 days)", filename="rolling_sharpe_252")

        # Visualize underwater equity curve
        visualizer.plot_underwater_equity(portfolio_df, title="Underwater Equity Curve", filename="underwater_equity")

        logger.info("Strategy performance visualization complete")

    except Exception as e:
        logger.error(f"Error visualizing strategy performance: {str(e)}")
        raise

def visualize_parameter_sensitivity(optimization_results: List[Dict[str, Any]], output_dir: str, dark_mode: bool = False) -> None:
    """
    Visualize parameter sensitivity.

    Args:
        optimization_results: List of optimization results
        output_dir: Directory to save visualizations
        dark_mode: Whether to use dark mode for visualizations
    """
    logger.info("Visualizing parameter sensitivity")

    try:
        # Create visualizer
        visualizer = StrategyVisualizer(output_dir, dark_mode)

        # Get list of parameters
        if optimization_results and 'params' in optimization_results[0]:
            params = list(optimization_results[0]['params'].keys())

            # Visualize parameter sensitivity for each parameter
            for param in params:
                # Visualize Sharpe ratio sensitivity
                visualizer.plot_parameter_sensitivity(
                    optimization_results, param, metric_name="sharpe_ratio",
                    title=f"Sharpe Ratio vs {param.replace('_', ' ').title()}",
                    filename=f"sharpe_vs_{param}"
                )

                # Visualize total return sensitivity
                visualizer.plot_parameter_sensitivity(
                    optimization_results, param, metric_name="total_return",
                    title=f"Total Return vs {param.replace('_', ' ').title()}",
                    filename=f"return_vs_{param}"
                )

                # Visualize win rate sensitivity
                visualizer.plot_parameter_sensitivity(
                    optimization_results, param, metric_name="win_rate",
                    title=f"Win Rate vs {param.replace('_', ' ').title()}",
                    filename=f"win_rate_vs_{param}"
                )

        logger.info("Parameter sensitivity visualization complete")

    except Exception as e:
        logger.error(f"Error visualizing parameter sensitivity: {str(e)}")
        raise

def visualize_regime_performance(backtest_results: Dict[str, Any], market_regime_results: Dict[str, Any],
                               output_dir: str, dark_mode: bool = False) -> None:
    """
    Visualize performance by market regime.

    Args:
        backtest_results: Dictionary with backtest results
        market_regime_results: Dictionary with market regime results
        output_dir: Directory to save visualizations
        dark_mode: Whether to use dark mode for visualizations
    """
    logger.info("Visualizing performance by market regime")

    try:
        # Create visualizer
        visualizer = StrategyVisualizer(output_dir, dark_mode)

        # Prepare portfolio DataFrame
        portfolio_df = prepare_portfolio_dataframe(backtest_results)

        # Prepare returns series
        returns = prepare_returns_series(portfolio_df)

        # Prepare regimes series
        regimes = prepare_regimes_series(market_regime_results, returns.index)

        # Visualize performance by market regime
        visualizer.plot_regime_performance(returns, regimes, title="Performance by Market Regime", filename="regime_performance")

        logger.info("Regime performance visualization complete")

    except Exception as e:
        logger.error(f"Error visualizing regime performance: {str(e)}")
        raise

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Visualize strategy performance")
    parser.add_argument("--backtest", type=str, help="Path to backtest results JSON")
    parser.add_argument("--optimization", type=str, help="Path to optimization results JSON")
    parser.add_argument("--regime", type=str, help="Path to market regime results JSON")
    parser.add_argument("--output", type=str, default="visualizations", help="Output directory")
    parser.add_argument("--dark-mode", action="store_true", help="Use dark mode for visualizations")
    parser.add_argument("--performance", action="store_true", help="Visualize performance")
    parser.add_argument("--parameters", action="store_true", help="Visualize parameter sensitivity")
    parser.add_argument("--regimes", action="store_true", help="Visualize performance by market regime")

    args = parser.parse_args()

    # Create logs directory if it doesn't exist
    os.makedirs(os.path.join(parent_dir, "logs"), exist_ok=True)

    # Create output directory if it doesn't exist
    os.makedirs(args.output, exist_ok=True)

    try:
        # Load backtest results if needed
        backtest_results = None
        if args.performance or args.regimes:
            if not args.backtest:
                logger.error("Backtest results path must be provided for performance visualization")
                return 1
            logger.info(f"Loading backtest results from {args.backtest}")
            backtest_results = load_backtest_results(args.backtest)
            logger.info(f"Loaded backtest results: {backtest_results.keys()}")

        # Load optimization results if needed
        optimization_results = None
        if args.parameters:
            if not args.optimization:
                logger.error("Optimization results path must be provided for parameter sensitivity visualization")
                return 1
            logger.info(f"Loading optimization results from {args.optimization}")
            optimization_results = load_optimization_results(args.optimization)
            logger.info(f"Loaded optimization results with {len(optimization_results)} entries")

        # Load market regime results if needed
        market_regime_results = None
        if args.regimes:
            if not args.regime:
                logger.error("Market regime results path must be provided for regime performance visualization")
                return 1
            logger.info(f"Loading market regime results from {args.regime}")
            market_regime_results = load_market_regime_results(args.regime)
            logger.info(f"Loaded market regime results: {market_regime_results.keys()}")

        # Visualize performance
        if args.performance:
            visualize_performance(backtest_results, args.output, args.dark_mode)

        # Visualize parameter sensitivity
        if args.parameters:
            visualize_parameter_sensitivity(optimization_results, args.output, args.dark_mode)

        # Visualize performance by market regime
        if args.regimes:
            visualize_regime_performance(backtest_results, market_regime_results, args.output, args.dark_mode)

        # If no specific visualization is requested, do all
        if not (args.performance or args.parameters or args.regimes):
            if backtest_results:
                visualize_performance(backtest_results, args.output, args.dark_mode)

            if optimization_results:
                visualize_parameter_sensitivity(optimization_results, args.output, args.dark_mode)

            if backtest_results and market_regime_results:
                visualize_regime_performance(backtest_results, market_regime_results, args.output, args.dark_mode)

        logger.info("Visualization complete")

        return 0

    except Exception as e:
        logger.error(f"Error: {str(e)}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
