"""
Market Regime Detection for Synergy7 Trading System.

This module implements market regime detection to identify different market conditions
and adapt trading strategies accordingly.
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, Any, List, Optional, Tuple
from enum import Enum
from datetime import datetime

# Configure logging
logger = logging.getLogger(__name__)

class MarketRegime(Enum):
    """Market regime types."""
    TRENDING_UP = "trending_up"
    TRENDING_DOWN = "trending_down"
    RANGING = "ranging"
    VOLATILE = "volatile"
    CHOPPY = "choppy"
    UNKNOWN = "unknown"


class MarketRegimeDetector:
    """
    Market Regime Detector.
    
    This class detects the current market regime based on various indicators
    and provides recommendations for strategy selection.
    """
    
    def __init__(self, **kwargs):
        """
        Initialize the market regime detector.
        
        Args:
            **kwargs: Configuration parameters
        """
        # Trend detection parameters
        self.adx_period = kwargs.get("adx_period", 14)
        self.adx_threshold = kwargs.get("adx_threshold", 25)
        
        # Volatility detection parameters
        self.bb_period = kwargs.get("bb_period", 20)
        self.bb_std_dev = kwargs.get("bb_std_dev", 2)
        self.atr_period = kwargs.get("atr_period", 14)
        self.volatility_lookback = kwargs.get("volatility_lookback", 50)
        self.volatility_threshold = kwargs.get("volatility_threshold", 0.03)
        
        # Range detection parameters
        self.range_period = kwargs.get("range_period", 20)
        self.range_threshold = kwargs.get("range_threshold", 0.05)
        
        # Choppiness detection parameters
        self.choppiness_period = kwargs.get("choppiness_period", 14)
        self.choppiness_threshold = kwargs.get("choppiness_threshold", 50)
        
        # Regime change detection
        self.regime_change_lookback = kwargs.get("regime_change_lookback", 5)
        
        # Historical regimes
        self.historical_regimes = []
        
        logger.info("Initialized market regime detector")
    
    def calculate_adx(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate Average Directional Index (ADX) for trend detection.
        
        Args:
            df: DataFrame with OHLCV data
            
        Returns:
            DataFrame with ADX values
        """
        # Make a copy of the data
        data = df.copy()
        
        # Calculate True Range (TR)
        data['tr0'] = abs(data['high'] - data['low'])
        data['tr1'] = abs(data['high'] - data['close'].shift())
        data['tr2'] = abs(data['low'] - data['close'].shift())
        data['tr'] = data[['tr0', 'tr1', 'tr2']].max(axis=1)
        
        # Calculate Directional Movement (DM)
        data['up_move'] = data['high'] - data['high'].shift()
        data['down_move'] = data['low'].shift() - data['low']
        
        # Calculate Positive and Negative DM
        data['plus_dm'] = 0
        data.loc[(data['up_move'] > data['down_move']) & (data['up_move'] > 0), 'plus_dm'] = data['up_move']
        
        data['minus_dm'] = 0
        data.loc[(data['down_move'] > data['up_move']) & (data['down_move'] > 0), 'minus_dm'] = data['down_move']
        
        # Calculate Smoothed TR and DM
        period = self.adx_period
        
        # Calculate smoothed TR
        data['atr'] = data['tr'].rolling(window=period).mean()
        
        # Calculate smoothed +DM and -DM
        data['plus_di'] = 100 * (data['plus_dm'].rolling(window=period).mean() / data['atr'])
        data['minus_di'] = 100 * (data['minus_dm'].rolling(window=period).mean() / data['atr'])
        
        # Calculate Directional Index (DX)
        data['dx'] = 100 * (abs(data['plus_di'] - data['minus_di']) / (data['plus_di'] + data['minus_di']))
        
        # Calculate ADX
        data['adx'] = data['dx'].rolling(window=period).mean()
        
        return data
    
    def calculate_bollinger_bands(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate Bollinger Bands for volatility detection.
        
        Args:
            df: DataFrame with OHLCV data
            
        Returns:
            DataFrame with Bollinger Bands
        """
        # Make a copy of the data
        data = df.copy()
        
        # Calculate Bollinger Bands
        data['ma'] = data['close'].rolling(window=self.bb_period).mean()
        data['std'] = data['close'].rolling(window=self.bb_period).std()
        data['upper_band'] = data['ma'] + (data['std'] * self.bb_std_dev)
        data['lower_band'] = data['ma'] - (data['std'] * self.bb_std_dev)
        data['bb_width'] = (data['upper_band'] - data['lower_band']) / data['ma']
        
        return data
    
    def calculate_choppiness_index(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate Choppiness Index for detecting choppy markets.
        
        Args:
            df: DataFrame with OHLCV data
            
        Returns:
            DataFrame with Choppiness Index
        """
        # Make a copy of the data
        data = df.copy()
        
        # Calculate ATR sum
        data['atr'] = data['tr'].rolling(window=self.choppiness_period).sum()
        
        # Calculate range
        data['range'] = data['high'].rolling(window=self.choppiness_period).max() - data['low'].rolling(window=self.choppiness_period).min()
        
        # Calculate Choppiness Index
        data['choppiness'] = 100 * np.log10(data['atr'] / data['range']) / np.log10(self.choppiness_period)
        
        return data
    
    def detect_regime(self, df: pd.DataFrame) -> Tuple[MarketRegime, Dict[str, float]]:
        """
        Detect the current market regime.
        
        Args:
            df: DataFrame with OHLCV data
            
        Returns:
            Tuple of (MarketRegime, metrics)
        """
        if len(df) < max(self.adx_period, self.bb_period, self.choppiness_period) + 10:
            logger.warning("Not enough data for regime detection")
            return MarketRegime.UNKNOWN, {}
        
        # Calculate indicators
        data = self.calculate_adx(df)
        data = self.calculate_bollinger_bands(data)
        data = self.calculate_choppiness_index(data)
        
        # Get the latest values
        latest = data.iloc[-1]
        
        # Calculate historical volatility
        returns = df['close'].pct_change().dropna()
        volatility = returns.rolling(self.volatility_lookback).std().iloc[-1] * np.sqrt(252)
        
        # Calculate metrics
        metrics = {
            "adx": latest['adx'],
            "plus_di": latest['plus_di'],
            "minus_di": latest['minus_di'],
            "bb_width": latest['bb_width'],
            "choppiness": latest['choppiness'],
            "volatility": volatility
        }
        
        # Detect regime
        if latest['choppiness'] > self.choppiness_threshold:
            regime = MarketRegime.CHOPPY
        elif volatility > self.volatility_threshold:
            regime = MarketRegime.VOLATILE
        elif latest['adx'] > self.adx_threshold:
            if latest['plus_di'] > latest['minus_di']:
                regime = MarketRegime.TRENDING_UP
            else:
                regime = MarketRegime.TRENDING_DOWN
        else:
            regime = MarketRegime.RANGING
        
        # Store regime
        self.historical_regimes.append(regime)
        if len(self.historical_regimes) > self.regime_change_lookback:
            self.historical_regimes.pop(0)
        
        return regime, metrics
    
    def get_strategy_recommendation(self, regime: MarketRegime) -> Dict[str, Any]:
        """
        Get strategy recommendations based on market regime.
        
        Args:
            regime: Current market regime
            
        Returns:
            Dictionary with strategy recommendations
        """
        recommendations = {
            "primary_strategy": None,
            "position_size_multiplier": 1.0,
            "stop_loss_multiplier": 1.0,
            "take_profit_multiplier": 1.0,
            "regime": regime.value
        }
        
        if regime == MarketRegime.TRENDING_UP:
            recommendations["primary_strategy"] = "momentum"
            recommendations["position_size_multiplier"] = 1.0
            recommendations["stop_loss_multiplier"] = 1.2
            recommendations["take_profit_multiplier"] = 1.5
        
        elif regime == MarketRegime.TRENDING_DOWN:
            recommendations["primary_strategy"] = "momentum"
            recommendations["position_size_multiplier"] = 0.8
            recommendations["stop_loss_multiplier"] = 1.0
            recommendations["take_profit_multiplier"] = 1.2
        
        elif regime == MarketRegime.RANGING:
            recommendations["primary_strategy"] = "mean_reversion"
            recommendations["position_size_multiplier"] = 1.0
            recommendations["stop_loss_multiplier"] = 1.0
            recommendations["take_profit_multiplier"] = 1.0
        
        elif regime == MarketRegime.VOLATILE:
            recommendations["primary_strategy"] = "mean_reversion"
            recommendations["position_size_multiplier"] = 0.7
            recommendations["stop_loss_multiplier"] = 1.5
            recommendations["take_profit_multiplier"] = 0.8
        
        elif regime == MarketRegime.CHOPPY:
            recommendations["primary_strategy"] = None  # No trading
            recommendations["position_size_multiplier"] = 0.0
            recommendations["stop_loss_multiplier"] = 0.0
            recommendations["take_profit_multiplier"] = 0.0
        
        else:  # UNKNOWN
            recommendations["primary_strategy"] = None
            recommendations["position_size_multiplier"] = 0.5
            recommendations["stop_loss_multiplier"] = 1.5
            recommendations["take_profit_multiplier"] = 0.8
        
        return recommendations
    
    def analyze_market(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Analyze the market and provide regime detection and strategy recommendations.
        
        Args:
            df: DataFrame with OHLCV data
            
        Returns:
            Dictionary with analysis results
        """
        # Detect regime
        regime, metrics = self.detect_regime(df)
        
        # Get strategy recommendations
        recommendations = self.get_strategy_recommendation(regime)
        
        # Check for regime change
        regime_change = False
        if len(self.historical_regimes) > 1:
            regime_change = self.historical_regimes[-1] != self.historical_regimes[-2]
        
        # Combine results
        results = {
            "regime": regime.value,
            "metrics": metrics,
            "recommendations": recommendations,
            "regime_change": regime_change,
            "timestamp": datetime.now().isoformat()
        }
        
        return results


# Example usage
if __name__ == "__main__":
    import sys
    import os
    import argparse
    
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    
    parser = argparse.ArgumentParser(description="Market Regime Detection")
    parser.add_argument("--data", type=str, required=True, help="Path to historical data CSV")
    
    args = parser.parse_args()
    
    # Load data
    try:
        df = pd.read_csv(args.data)
        
        # Convert timestamp to datetime if present
        if 'timestamp' in df.columns:
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df.set_index('timestamp', inplace=True)
        
        # Create detector
        detector = MarketRegimeDetector()
        
        # Analyze market
        results = detector.analyze_market(df)
        
        # Print results
        print(f"Market Regime: {results['regime']}")
        print(f"Metrics: {results['metrics']}")
        print(f"Recommendations: {results['recommendations']}")
        print(f"Regime Change: {results['regime_change']}")
        
    except Exception as e:
        logger.error(f"Error: {str(e)}")
        sys.exit(1)
    
    sys.exit(0)
