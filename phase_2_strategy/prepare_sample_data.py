#!/usr/bin/env python3
"""
Prepare Sample Data for Strategy Optimization.

This script prepares sample data for strategy optimization by generating
OHLCV data with realistic price movements and technical indicators.
"""

import os
import sys
import json
import logging
import argparse
import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta

# Add parent directory to path
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("prepare_sample_data")

def generate_sample_ohlcv(start_date: str, end_date: str, timeframe: str = "1h") -> pd.DataFrame:
    """
    Generate sample OHLCV data with realistic price movements.
    
    Args:
        start_date: Start date (YYYY-MM-DD)
        end_date: End date (YYYY-MM-DD)
        timeframe: Timeframe (e.g., "1h", "1d")
        
    Returns:
        DataFrame with OHLCV data
    """
    logger.info(f"Generating sample OHLCV data from {start_date} to {end_date} with timeframe {timeframe}")
    
    # Convert dates to datetime
    start_dt = pd.to_datetime(start_date)
    end_dt = pd.to_datetime(end_date)
    
    # Generate date range based on timeframe
    if timeframe == "1h":
        date_range = pd.date_range(start=start_dt, end=end_dt, freq="H")
    elif timeframe == "1d":
        date_range = pd.date_range(start=start_dt, end=end_dt, freq="D")
    else:
        raise ValueError(f"Unsupported timeframe: {timeframe}")
    
    # Set random seed for reproducibility
    np.random.seed(42)
    
    # Generate price data with realistic movements
    # Start with a base price
    base_price = 100.0
    
    # Generate random returns with drift and volatility
    drift = 0.0001  # Daily drift (upward trend)
    volatility = 0.02  # Daily volatility
    
    # Adjust for timeframe
    if timeframe == "1h":
        drift /= 24
        volatility /= np.sqrt(24)
    
    # Generate returns
    returns = np.random.normal(drift, volatility, len(date_range))
    
    # Add some autocorrelation to returns
    for i in range(1, len(returns)):
        returns[i] = 0.1 * returns[i-1] + 0.9 * returns[i]
    
    # Add some seasonality
    seasonality = 0.005 * np.sin(np.linspace(0, 10 * np.pi, len(date_range)))
    returns += seasonality
    
    # Add some jumps
    jump_points = np.random.choice(len(date_range), size=10, replace=False)
    jump_sizes = np.random.normal(0, 0.05, size=10)
    for i, jump in enumerate(jump_points):
        returns[jump] += jump_sizes[i]
    
    # Calculate price from returns
    prices = base_price * np.cumprod(1 + returns)
    
    # Generate OHLCV data
    df = pd.DataFrame(index=date_range)
    df['close'] = prices
    
    # Generate open, high, low based on close
    df['open'] = df['close'].shift(1)
    df.loc[df.index[0], 'open'] = df.loc[df.index[0], 'close'] * (1 - np.random.uniform(0, 0.01))
    
    # Generate high and low
    daily_volatility = volatility
    df['high'] = df.apply(lambda x: max(x['open'], x['close']) * (1 + np.random.uniform(0, daily_volatility)), axis=1)
    df['low'] = df.apply(lambda x: min(x['open'], x['close']) * (1 - np.random.uniform(0, daily_volatility)), axis=1)
    
    # Generate volume
    base_volume = 1000000
    df['volume'] = np.random.lognormal(np.log(base_volume), 0.5, len(df))
    
    # Increase volume during price jumps
    df.loc[date_range[jump_points], 'volume'] *= 2
    
    # Ensure high >= max(open, close) and low <= min(open, close)
    df['high'] = df.apply(lambda x: max(x['high'], x['open'], x['close']), axis=1)
    df['low'] = df.apply(lambda x: min(x['low'], x['open'], x['close']), axis=1)
    
    logger.info(f"Generated {len(df)} OHLCV data points")
    
    return df

def calculate_indicators(df: pd.DataFrame) -> pd.DataFrame:
    """
    Calculate technical indicators for the sample data.
    
    Args:
        df: DataFrame with OHLCV data
        
    Returns:
        DataFrame with added indicators
    """
    logger.info("Calculating technical indicators")
    
    # Make a copy of the DataFrame
    df = df.copy()
    
    # Calculate moving averages
    for period in [5, 10, 20, 50, 100, 200]:
        df[f'ma_{period}'] = df['close'].rolling(window=period).mean()
        df[f'ema_{period}'] = df['close'].ewm(span=period, adjust=False).mean()
    
    # Calculate Bollinger Bands
    for period in [20, 50]:
        for std in [1.5, 2.0, 2.5]:
            # Calculate middle band (SMA)
            df[f'bb_middle_{period}_{std}'] = df['close'].rolling(window=period).mean()
            
            # Calculate standard deviation
            df[f'bb_std_{period}_{std}'] = df['close'].rolling(window=period).std()
            
            # Calculate upper and lower bands
            df[f'bb_upper_{period}_{std}'] = df[f'bb_middle_{period}_{std}'] + (df[f'bb_std_{period}_{std}'] * std)
            df[f'bb_lower_{period}_{std}'] = df[f'bb_middle_{period}_{std}'] - (df[f'bb_std_{period}_{std}'] * std)
            
            # Calculate %B
            df[f'bb_b_{period}_{std}'] = (df['close'] - df[f'bb_lower_{period}_{std}']) / (df[f'bb_upper_{period}_{std}'] - df[f'bb_lower_{period}_{std}'])
            
            # Calculate Bollinger Band width
            df[f'bb_width_{period}_{std}'] = (df[f'bb_upper_{period}_{std}'] - df[f'bb_lower_{period}_{std}']) / df[f'bb_middle_{period}_{std}']
    
    # Calculate RSI
    for period in [7, 14, 21]:
        # Calculate price changes
        delta = df['close'].diff()
        
        # Calculate gains and losses
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)
        
        # Calculate average gains and losses
        avg_gain = gain.rolling(window=period).mean()
        avg_loss = loss.rolling(window=period).mean()
        
        # Calculate RS
        rs = avg_gain / avg_loss
        
        # Calculate RSI
        df[f'rsi_{period}'] = 100 - (100 / (1 + rs))
    
    # Calculate MACD
    df['macd_12_26'] = df['close'].ewm(span=12, adjust=False).mean() - df['close'].ewm(span=26, adjust=False).mean()
    df['macd_signal_12_26_9'] = df['macd_12_26'].ewm(span=9, adjust=False).mean()
    df['macd_histogram_12_26_9'] = df['macd_12_26'] - df['macd_signal_12_26_9']
    
    # Calculate ATR
    for period in [7, 14, 21]:
        # Calculate True Range
        df['tr'] = np.maximum(
            df['high'] - df['low'],
            np.maximum(
                abs(df['high'] - df['close'].shift(1)),
                abs(df['low'] - df['close'].shift(1))
            )
        )
        
        # Calculate ATR
        df[f'atr_{period}'] = df['tr'].rolling(window=period).mean()
    
    # Calculate ADX
    for period in [7, 14, 21]:
        # Calculate +DM and -DM
        df['plus_dm'] = np.where(
            (df['high'] - df['high'].shift(1)) > (df['low'].shift(1) - df['low']),
            np.maximum(df['high'] - df['high'].shift(1), 0),
            0
        )
        df['minus_dm'] = np.where(
            (df['low'].shift(1) - df['low']) > (df['high'] - df['high'].shift(1)),
            np.maximum(df['low'].shift(1) - df['low'], 0),
            0
        )
        
        # Calculate +DI and -DI
        df[f'plus_di_{period}'] = 100 * df['plus_dm'].rolling(window=period).mean() / df[f'atr_{period}']
        df[f'minus_di_{period}'] = 100 * df['minus_dm'].rolling(window=period).mean() / df[f'atr_{period}']
        
        # Calculate DX
        df[f'dx_{period}'] = 100 * abs(df[f'plus_di_{period}'] - df[f'minus_di_{period}']) / (df[f'plus_di_{period}'] + df[f'minus_di_{period}'])
        
        # Calculate ADX
        df[f'adx_{period}'] = df[f'dx_{period}'].rolling(window=period).mean()
    
    # Calculate Choppiness Index
    for period in [14, 21]:
        # Calculate sum of ATR
        df[f'atr_sum_{period}'] = df['tr'].rolling(window=period).sum()
        
        # Calculate range
        df[f'range_{period}'] = df['high'].rolling(window=period).max() - df['low'].rolling(window=period).min()
        
        # Calculate Choppiness Index
        df[f'choppiness_{period}'] = 100 * np.log10(df[f'atr_sum_{period}'] / df[f'range_{period}']) / np.log10(period)
    
    # Calculate volume features
    df['volume_ma_20'] = df['volume'].rolling(window=20).mean()
    df['volume_ratio'] = df['volume'] / df['volume_ma_20']
    
    # Remove rows with NaN values
    df.dropna(inplace=True)
    
    logger.info(f"Calculated indicators. DataFrame has {len(df)} data points")
    
    return df

def split_data(df: pd.DataFrame, train_size: float = 0.6, validation_size: float = 0.2) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
    """
    Split data into training, validation, and testing sets.
    
    Args:
        df: DataFrame to split
        train_size: Proportion of data to use for training
        validation_size: Proportion of data to use for validation
        
    Returns:
        Tuple of (train_data, validation_data, test_data)
    """
    logger.info(f"Splitting data with train_size={train_size}, validation_size={validation_size}")
    
    # Calculate split indices
    train_idx = int(len(df) * train_size)
    validation_idx = int(len(df) * (train_size + validation_size))
    
    # Split data
    train_data = df.iloc[:train_idx].copy()
    validation_data = df.iloc[train_idx:validation_idx].copy()
    test_data = df.iloc[validation_idx:].copy()
    
    logger.info(f"Split data into {len(train_data)} training points, "
               f"{len(validation_data)} validation points, and {len(test_data)} testing points")
    
    return train_data, validation_data, test_data

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Prepare sample data for strategy optimization")
    parser.add_argument("--start-date", type=str, default="2022-01-01", help="Start date (YYYY-MM-DD)")
    parser.add_argument("--end-date", type=str, default="2023-12-31", help="End date (YYYY-MM-DD)")
    parser.add_argument("--timeframe", type=str, default="1h", help="Timeframe (e.g., '1h', '1d')")
    parser.add_argument("--output", type=str, default="data/prepared", help="Output directory")
    parser.add_argument("--train-size", type=float, default=0.6, help="Proportion of data to use for training")
    parser.add_argument("--validation-size", type=float, default=0.2, help="Proportion of data to use for validation")
    
    args = parser.parse_args()
    
    try:
        # Create output directory if it doesn't exist
        os.makedirs(args.output, exist_ok=True)
        
        # Generate sample OHLCV data
        df = generate_sample_ohlcv(args.start_date, args.end_date, args.timeframe)
        
        # Calculate indicators
        df = calculate_indicators(df)
        
        # Split data
        train_data, validation_data, test_data = split_data(df, args.train_size, args.validation_size)
        
        # Save data
        train_path = os.path.join(args.output, "train_data.csv")
        validation_path = os.path.join(args.output, "validation_data.csv")
        test_path = os.path.join(args.output, "test_data.csv")
        full_path = os.path.join(args.output, "full_data.csv")
        
        train_data.to_csv(train_path)
        validation_data.to_csv(validation_path)
        test_data.to_csv(test_path)
        df.to_csv(full_path)
        
        logger.info(f"Saved training data to {train_path}")
        logger.info(f"Saved validation data to {validation_path}")
        logger.info(f"Saved testing data to {test_path}")
        logger.info(f"Saved full data to {full_path}")
        
        return 0
    
    except Exception as e:
        logger.error(f"Error: {str(e)}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
