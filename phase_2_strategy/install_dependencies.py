#!/usr/bin/env python3
"""
Install dependencies for Synergy7 Strategy Finder.

This script installs the required dependencies for the Synergy7 Strategy Finder.
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("install_dependencies")

def install_dependencies(requirements_path: str) -> int:
    """
    Install dependencies from requirements file.
    
    Args:
        requirements_path: Path to requirements file
        
    Returns:
        Return code from pip install
    """
    logger.info(f"Installing dependencies from {requirements_path}")
    
    # Check if requirements file exists
    if not os.path.exists(requirements_path):
        logger.error(f"Requirements file not found: {requirements_path}")
        return 1
    
    # Install dependencies
    cmd = [sys.executable, "-m", "pip", "install", "-r", requirements_path]
    logger.info(f"Running command: {' '.join(cmd)}")
    
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode != 0:
        logger.error(f"Error installing dependencies: {result.stderr}")
    else:
        logger.info("Dependencies installed successfully")
    
    return result.returncode

def main():
    """Main function."""
    # Get path to requirements file
    script_dir = Path(__file__).parent
    requirements_path = script_dir / "requirements.txt"
    
    # Install dependencies
    return_code = install_dependencies(str(requirements_path))
    
    return return_code

if __name__ == "__main__":
    sys.exit(main())
