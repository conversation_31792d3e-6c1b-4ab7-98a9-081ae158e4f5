#!/usr/bin/env python3
"""
Run Data Collection and Preparation for Synergy7 Trading System.

This script runs the entire data collection and preparation process for SOL/USD,
including collecting data from multiple sources, merging, calculating features,
and splitting into training and testing sets.
"""

import os
import sys
import logging
import argparse
import subprocess
from datetime import datetime, timedelta

# Add parent directory to path
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(os.path.join(parent_dir, "logs", "run_data_collection.log")),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("run_data_collection")

def run_data_collection(args):
    """
    Run data collection process.
    
    Args:
        args: Command-line arguments
    
    Returns:
        Path to collected data file
    """
    logger.info("Running data collection")
    
    # Create command
    cmd = [
        "python", os.path.join(parent_dir, "phase_2_strategy", "data_collection.py"),
        "--symbol", args.symbol,
        "--timeframe", args.timeframe,
        "--start-date", args.start_date,
        "--end-date", args.end_date,
        "--output", os.path.join(args.output, "raw")
    ]
    
    # Add sources if specified
    if args.sources:
        cmd.extend(["--sources"] + args.sources)
    
    # Add Birdeye API key if specified
    if args.birdeye_api_key:
        cmd.extend(["--birdeye-api-key", args.birdeye_api_key])
    
    # Run command
    logger.info(f"Running command: {' '.join(cmd)}")
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    # Check result
    if result.returncode != 0:
        logger.error(f"Data collection failed: {result.stderr}")
        raise RuntimeError("Data collection failed")
    
    # Extract data file path from output
    output = result.stdout.strip()
    data_path = None
    
    for line in output.split("\n"):
        if line.startswith("Data saved to "):
            data_path = line.replace("Data saved to ", "").strip()
            break
    
    if not data_path:
        logger.error("Could not find data file path in output")
        raise RuntimeError("Could not find data file path in output")
    
    logger.info(f"Data collection complete. Data saved to {data_path}")
    
    return data_path

def run_data_preparation(args, data_path):
    """
    Run data preparation process.
    
    Args:
        args: Command-line arguments
        data_path: Path to collected data file
    
    Returns:
        Tuple of (train_data_path, test_data_path)
    """
    logger.info("Running data preparation")
    
    # Create command
    cmd = [
        "python", os.path.join(parent_dir, "phase_2_strategy", "prepare_data.py"),
        "--data", data_path,
        "--output", os.path.join(args.output, "prepared"),
        "--test-size", str(args.test_size)
    ]
    
    # Add no-visualize flag if specified
    if args.no_visualize:
        cmd.append("--no-visualize")
    
    # Run command
    logger.info(f"Running command: {' '.join(cmd)}")
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    # Check result
    if result.returncode != 0:
        logger.error(f"Data preparation failed: {result.stderr}")
        raise RuntimeError("Data preparation failed")
    
    # Extract data file paths from output
    output = result.stdout.strip()
    train_path = None
    test_path = None
    
    for line in output.split("\n"):
        if line.startswith("Training data: "):
            train_path = line.replace("Training data: ", "").strip()
        elif line.startswith("Testing data: "):
            test_path = line.replace("Testing data: ", "").strip()
    
    if not train_path or not test_path:
        logger.error("Could not find data file paths in output")
        raise RuntimeError("Could not find data file paths in output")
    
    logger.info(f"Data preparation complete. Training data: {train_path}, Testing data: {test_path}")
    
    return train_path, test_path

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Run data collection and preparation")
    parser.add_argument("--symbol", type=str, default="SOL/USDT", help="Trading symbol")
    parser.add_argument("--timeframe", type=str, default="1h", help="Timeframe")
    parser.add_argument("--start-date", type=str, default="2023-01-01", help="Start date (YYYY-MM-DD)")
    parser.add_argument("--end-date", type=str, default="2023-12-31", help="End date (YYYY-MM-DD)")
    parser.add_argument("--output", type=str, default="data", help="Output directory")
    parser.add_argument("--sources", type=str, nargs="+", default=["binance", "birdeye", "coingecko"], help="Data sources")
    parser.add_argument("--birdeye-api-key", type=str, help="Birdeye API key")
    parser.add_argument("--test-size", type=float, default=0.3, help="Proportion of data to use for testing")
    parser.add_argument("--no-visualize", action="store_true", help="Disable data visualization")
    parser.add_argument("--skip-collection", action="store_true", help="Skip data collection")
    parser.add_argument("--skip-preparation", action="store_true", help="Skip data preparation")
    parser.add_argument("--data-path", type=str, help="Path to raw data CSV (for skipping collection)")
    
    args = parser.parse_args()
    
    # Create logs directory if it doesn't exist
    os.makedirs(os.path.join(parent_dir, "logs"), exist_ok=True)
    
    # Create output directory if it doesn't exist
    os.makedirs(args.output, exist_ok=True)
    
    try:
        # Run data collection
        if not args.skip_collection:
            data_path = run_data_collection(args)
        else:
            if not args.data_path:
                logger.error("Data path must be provided when skipping collection")
                return 1
            data_path = args.data_path
        
        # Run data preparation
        if not args.skip_preparation:
            train_path, test_path = run_data_preparation(args, data_path)
        
        logger.info("Data collection and preparation complete")
        
        return 0
    
    except Exception as e:
        logger.error(f"Error: {str(e)}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
