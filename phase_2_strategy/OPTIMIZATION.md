# Synergy7 Strategy Optimization and Validation Guide

This guide explains how to optimize and validate trading strategies for the Synergy7 trading system.

## Overview

The optimization and validation process consists of the following steps:

1. **Parameter Optimization**: Find optimal parameters for the Mean Reversion and Momentum strategies
2. **Strategy Validation**: Validate the optimized strategies on out-of-sample data
3. **Performance Visualization**: Visualize the performance of the optimized strategies

## Parameter Optimization

The parameter optimization process uses grid search to find the best parameter combinations for each strategy.

### Mean Reversion Strategy Optimization

```bash
python phase_2_strategy/optimize_mean_reversion.py \
  --data data/prepared/train_data.csv \
  --output output/mean_reversion \
  --test-size 0.3 \
  --top-n 10
```

Options:
- `--data`: Path to training data CSV
- `--output`: Output directory
- `--test-size`: Proportion of data to use for testing
- `--top-n`: Number of top parameter combinations to return

### Momentum Strategy Optimization

```bash
python phase_2_strategy/optimize_momentum.py \
  --data data/prepared/train_data.csv \
  --output output/momentum \
  --test-size 0.3 \
  --top-n 10 \
  --max-combinations 100
```

Options:
- `--data`: Path to training data CSV
- `--output`: Output directory
- `--test-size`: Proportion of data to use for testing
- `--top-n`: Number of top parameter combinations to return
- `--max-combinations`: Maximum number of parameter combinations to test

## Strategy Validation

The strategy validation process tests the optimized strategies on out-of-sample data to ensure they are robust and not overfitted.

```bash
python phase_2_strategy/validate_strategies.py \
  --data data/prepared/test_data.csv \
  --mean-reversion-params output/mean_reversion/best_params.json \
  --momentum-params output/momentum/best_params.json \
  --output output/validation \
  --initial-capital 10000.0
```

Options:
- `--data`: Path to out-of-sample data CSV
- `--mean-reversion-params`: Path to Mean Reversion parameters JSON
- `--momentum-params`: Path to Momentum parameters JSON
- `--output`: Output directory
- `--initial-capital`: Initial capital for backtesting

## Running the Complete Process

For convenience, a script is provided to run the entire optimization and validation process:

```bash
python phase_2_strategy/run_optimization.py \
  --train-data data/prepared/train_data.csv \
  --validation-data data/prepared/test_data.csv \
  --output output \
  --test-size 0.3 \
  --top-n 10 \
  --max-combinations 100 \
  --initial-capital 10000.0
```

Options:
- `--train-data`: Path to training data CSV
- `--validation-data`: Path to validation data CSV
- `--output`: Output directory
- `--test-size`: Proportion of training data to use for testing
- `--top-n`: Number of top parameter combinations to return
- `--max-combinations`: Maximum number of parameter combinations to test
- `--initial-capital`: Initial capital for backtesting
- `--skip-mean-reversion`: Skip Mean Reversion optimization
- `--skip-momentum`: Skip Momentum optimization
- `--skip-validation`: Skip strategy validation
- `--skip-visualization`: Skip visualization
- `--mean-reversion-params`: Path to Mean Reversion parameters (for skipping optimization)
- `--momentum-params`: Path to Momentum parameters (for skipping optimization)

## Output Files

The optimization and validation process generates the following files:

### Mean Reversion Optimization

- `output/mean_reversion/all_results.json`: All parameter combinations and their performance metrics
- `output/mean_reversion/top_results.json`: Top parameter combinations and their performance metrics
- `output/mean_reversion/best_params.json`: Best parameter combination
- `output/mean_reversion/best_strategy_results.json`: Backtest results for the best parameter combination
- `output/mean_reversion/visualizations/`: Visualizations of optimization results

### Momentum Optimization

- `output/momentum/all_results.json`: All parameter combinations and their performance metrics
- `output/momentum/top_results.json`: Top parameter combinations and their performance metrics
- `output/momentum/best_params.json`: Best parameter combination
- `output/momentum/best_strategy_results.json`: Backtest results for the best parameter combination
- `output/momentum/visualizations/`: Visualizations of optimization results

### Strategy Validation

- `output/validation/mean_reversion_validation_results.json`: Validation results for Mean Reversion strategy
- `output/validation/momentum_validation_results.json`: Validation results for Momentum strategy
- `output/validation/combined_validation_results.json`: Validation results for combined strategy
- `output/validation/visualizations/`: Visualizations of validation results

## Performance Metrics

The optimization and validation process evaluates strategies based on the following metrics:

- **Total Return**: The total return of the strategy
- **Sharpe Ratio**: The risk-adjusted return of the strategy
- **Max Drawdown**: The maximum drawdown of the strategy
- **Win Rate**: The percentage of winning trades
- **Profit Factor**: The ratio of gross profit to gross loss
- **Total Trades**: The total number of trades

## Optimization Parameters

### Mean Reversion Strategy Parameters

- `lookback_period`: Period for Bollinger Bands calculation
- `std_dev_multiplier`: Standard deviation multiplier for Bollinger Bands
- `mean_period`: Period for moving average calculation
- `use_adaptive_params`: Whether to adapt parameters based on volatility
- `position_sizing_method`: Method for position sizing
- `risk_per_trade`: Risk per trade as a fraction of portfolio

### Momentum Strategy Parameters

- `ema_short_period`: Period for short EMA
- `ema_medium_period`: Period for medium EMA
- `ema_long_period`: Period for long EMA
- `adx_period`: Period for ADX calculation
- `adx_threshold`: Threshold for ADX to indicate trend
- `rsi_period`: Period for RSI calculation
- `rsi_oversold`: RSI threshold for oversold condition
- `rsi_overbought`: RSI threshold for overbought condition
- `pullback_threshold`: Threshold for pullback entry
- `volume_threshold`: Threshold for volume confirmation
- `take_profit_atr_multiple`: Multiplier for ATR-based take profit
- `stop_loss_atr_multiple`: Multiplier for ATR-based stop loss
- `trailing_stop_atr_multiple`: Multiplier for ATR-based trailing stop

## Combined Strategy

The combined strategy uses both Mean Reversion and Momentum strategies with the following rules:

1. Exit signals take priority over entry signals
2. Mean Reversion signals take priority over Momentum signals
3. Each strategy uses its own optimized parameters
4. Risk management is applied at the portfolio level

## Visualization

The visualization process generates the following visualizations:

- **Equity Curves**: Shows the growth of the portfolio over time
- **Drawdowns**: Shows the drawdowns over time
- **Performance Metrics Comparison**: Compares performance metrics across strategies
- **Strategy Breakdown**: Shows the performance of each strategy in the combined strategy

## Troubleshooting

### Optimization Takes Too Long

If the optimization process takes too long, try:
- Reducing the parameter grid
- Reducing the `max-combinations` parameter
- Using a smaller dataset

### Overfitting

If the strategy performs well on training data but poorly on validation data, try:
- Simplifying the strategy
- Reducing the number of parameters
- Using a larger training dataset
- Adding regularization to the optimization process

### Memory Issues

If you encounter memory issues, try:
- Reducing the size of the dataset
- Reducing the number of parameter combinations
- Running the optimization in smaller batches

## Next Steps

After optimizing and validating the strategies, you can:

1. **Deploy the Strategies**: Integrate the optimized strategies into the live trading system
2. **Monitor Performance**: Set up monitoring to track strategy performance in real-time
3. **Periodic Reoptimization**: Reoptimize the strategies periodically to adapt to changing market conditions
