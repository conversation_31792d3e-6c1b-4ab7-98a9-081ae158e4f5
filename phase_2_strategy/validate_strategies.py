#!/usr/bin/env python3
"""
Strategy Validation Script for Synergy7 Trading System.

This script validates the optimized Mean Reversion and Momentum strategies
on out-of-sample data to ensure they are robust and not overfitted.
"""

import os
import sys
import json
import logging
import argparse
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime

# Add parent directory to path
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

# Import strategies
from phase_2_strategy.mean_reversion import MeanReversionStrategy
from phase_2_strategy.momentum_strategy import MomentumStrategy

# Import visualization module
from phase_2_strategy.visualization import StrategyVisualizer

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(os.path.join(parent_dir, "logs", "validate_strategies.log")),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("validate_strategies")

def load_data(data_path: str) -> pd.DataFrame:
    """
    Load data from CSV file.

    Args:
        data_path: Path to CSV file

    Returns:
        DataFrame with data
    """
    logger.info(f"Loading data from {data_path}")

    try:
        # Load data
        df = pd.read_csv(data_path)

        # Convert timestamp to datetime if it's a string
        if 'timestamp' in df.columns and isinstance(df['timestamp'].iloc[0], str):
            df['timestamp'] = pd.to_datetime(df['timestamp'])

        # Set timestamp as index if it exists
        if 'timestamp' in df.columns:
            df.set_index('timestamp', inplace=True)

        # Ensure required columns exist
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        for col in required_columns:
            if col not in df.columns:
                logger.error(f"Required column '{col}' not found in data")
                raise ValueError(f"Required column '{col}' not found in data")

        logger.info(f"Loaded {len(df)} data points from {data_path}")
        return df

    except Exception as e:
        logger.error(f"Error loading data: {str(e)}")
        raise

def load_parameters(params_path: str) -> Dict[str, Any]:
    """
    Load strategy parameters from JSON file.

    Args:
        params_path: Path to parameters JSON file

    Returns:
        Dictionary with parameters
    """
    logger.info(f"Loading parameters from {params_path}")

    try:
        with open(params_path, 'r') as f:
            params = json.load(f)

        logger.info(f"Loaded parameters: {params}")
        return params

    except Exception as e:
        logger.error(f"Error loading parameters: {str(e)}")
        raise

def validate_mean_reversion(data: pd.DataFrame, params: Dict[str, Any],
                          output_dir: str, initial_capital: float = 10000.0) -> Dict[str, Any]:
    """
    Validate Mean Reversion strategy on out-of-sample data.

    Args:
        data: Out-of-sample data
        params: Strategy parameters
        output_dir: Directory to save results
        initial_capital: Initial capital for backtesting

    Returns:
        Dictionary with validation results
    """
    logger.info("Validating Mean Reversion strategy")

    # Initialize strategy with parameters
    strategy = MeanReversionStrategy(**params)

    # Backtest on out-of-sample data
    results = strategy.backtest(data, initial_capital)

    # Save results
    results_path = os.path.join(output_dir, "mean_reversion_validation_results.json")
    with open(results_path, 'w') as f:
        json.dump(results, f, indent=2, default=str)

    logger.info(f"Saved Mean Reversion validation results to {results_path}")

    # Log key metrics
    logger.info(f"Mean Reversion validation results:")
    logger.info(f"  Total Return: {results['total_return']:.2%}")
    logger.info(f"  Sharpe Ratio: {results['sharpe_ratio']:.2f}")
    logger.info(f"  Max Drawdown: {results['max_drawdown']:.2%}")
    logger.info(f"  Win Rate: {results['win_rate']:.2%}")
    logger.info(f"  Profit Factor: {results['profit_factor']:.2f}")
    logger.info(f"  Total Trades: {results['total_trades']}")

    return results

def validate_momentum(data: pd.DataFrame, params: Dict[str, Any],
                    output_dir: str, initial_capital: float = 10000.0) -> Dict[str, Any]:
    """
    Validate Momentum strategy on out-of-sample data.

    Args:
        data: Out-of-sample data
        params: Strategy parameters
        output_dir: Directory to save results
        initial_capital: Initial capital for backtesting

    Returns:
        Dictionary with validation results
    """
    logger.info("Validating Momentum strategy")

    # Initialize strategy with parameters
    strategy = MomentumStrategy(**params)

    # Backtest on out-of-sample data
    results = strategy.backtest(data, initial_capital)

    # Save results
    results_path = os.path.join(output_dir, "momentum_validation_results.json")
    with open(results_path, 'w') as f:
        json.dump(results, f, indent=2, default=str)

    logger.info(f"Saved Momentum validation results to {results_path}")

    # Log key metrics
    logger.info(f"Momentum validation results:")
    logger.info(f"  Total Return: {results['total_return']:.2%}")
    logger.info(f"  Sharpe Ratio: {results['sharpe_ratio']:.2f}")
    logger.info(f"  Max Drawdown: {results['max_drawdown']:.2%}")
    logger.info(f"  Win Rate: {results['win_rate']:.2%}")
    logger.info(f"  Profit Factor: {results['profit_factor']:.2f}")
    logger.info(f"  Total Trades: {results['total_trades']}")

    return results

def validate_combined_strategy(data: pd.DataFrame, mean_reversion_params: Dict[str, Any],
                             momentum_params: Dict[str, Any], output_dir: str,
                             initial_capital: float = 10000.0) -> Dict[str, Any]:
    """
    Validate combined strategy on out-of-sample data.

    Args:
        data: Out-of-sample data
        mean_reversion_params: Mean Reversion strategy parameters
        momentum_params: Momentum strategy parameters
        output_dir: Directory to save results
        initial_capital: Initial capital for backtesting

    Returns:
        Dictionary with validation results
    """
    logger.info("Validating combined strategy")

    # Initialize strategies
    mean_reversion = MeanReversionStrategy(**mean_reversion_params)
    momentum = MomentumStrategy(**momentum_params)

    # Generate signals for both strategies
    mean_reversion_signals = mean_reversion.generate_signals(data)
    momentum_signals = momentum.generate_signals(data)

    # Combine signals
    combined_signals = data.copy()
    combined_signals['mean_reversion_signal'] = mean_reversion_signals['signal']
    combined_signals['momentum_signal'] = momentum_signals['signal']

    # Initialize portfolio and tracking variables
    portfolio = pd.DataFrame(index=combined_signals.index)
    portfolio['holdings'] = 0.0
    portfolio['cash'] = initial_capital
    portfolio['total'] = initial_capital
    portfolio['returns'] = 0.0

    # Track trades
    trades = []
    current_position = 0
    entry_price = 0
    entry_date = None
    entry_strategy = None

    # Simulate trading
    for i, idx in enumerate(portfolio.index):
        if i == 0:  # Skip first row
            continue

        # Get signals
        mr_signal = combined_signals.loc[idx, 'mean_reversion_signal']
        mom_signal = combined_signals.loc[idx, 'momentum_signal']

        # Determine combined signal
        # Priority: 1. Exit signals, 2. Mean Reversion, 3. Momentum
        signal = 0
        strategy_name = None

        # Exit signals
        if (current_position == 1 and (mr_signal == -1 or mom_signal == -1)) or \
           (current_position == -1 and (mr_signal == 1 or mom_signal == 1)):
            signal = -current_position
            strategy_name = "exit"

        # Entry signals
        elif current_position == 0:
            if mr_signal != 0:
                signal = mr_signal
                strategy_name = "mean_reversion"
            elif mom_signal != 0:
                signal = mom_signal
                strategy_name = "momentum"

        # Update portfolio based on signal
        if signal != 0:
            price = combined_signals.loc[idx, 'close']

            # Exit position
            if current_position != 0 and signal == -current_position:
                # Calculate profit/loss
                profit_loss = (price - entry_price) * current_position
                profit_loss_pct = (price - entry_price) / entry_price * current_position

                # Record trade
                trades.append({
                    "entry_date": entry_date,
                    "exit_date": idx,
                    "entry_price": entry_price,
                    "exit_price": price,
                    "position": "long" if current_position == 1 else "short",
                    "profit_loss": profit_loss,
                    "profit_loss_pct": profit_loss_pct,
                    "strategy": entry_strategy
                })

                # Update portfolio
                portfolio.loc[idx, 'holdings'] = 0
                portfolio.loc[idx, 'cash'] = portfolio.loc[portfolio.index[i-1], 'cash'] + profit_loss

                # Reset position
                current_position = 0

            # Enter position
            if current_position == 0 and signal != 0:
                # Calculate position size (1% risk)
                risk_amount = portfolio.loc[portfolio.index[i-1], 'cash'] * 0.01
                position_size = risk_amount / (price * 0.05)  # Assume 5% stop loss

                # Update portfolio
                portfolio.loc[idx, 'holdings'] = position_size * price * signal
                portfolio.loc[idx, 'cash'] = portfolio.loc[portfolio.index[i-1], 'cash'] - position_size * price * signal

                # Record entry
                current_position = signal
                entry_price = price
                entry_date = idx
                entry_strategy = strategy_name

        else:
            # No signal, carry forward
            portfolio.loc[idx, 'holdings'] = portfolio.loc[portfolio.index[i-1], 'holdings']
            portfolio.loc[idx, 'cash'] = portfolio.loc[portfolio.index[i-1], 'cash']

        # Update total value and returns
        portfolio.loc[idx, 'total'] = portfolio.loc[idx, 'holdings'] + portfolio.loc[idx, 'cash']
        portfolio.loc[idx, 'returns'] = portfolio.loc[idx, 'total'] / portfolio.loc[portfolio.index[i-1], 'total'] - 1

    # Close any open position at the end
    if current_position != 0:
        exit_date = portfolio.index[-1]
        exit_price = combined_signals.loc[exit_date, 'close']
        profit_loss = (exit_price - entry_price) * current_position
        profit_loss_pct = (exit_price - entry_price) / entry_price * current_position

        trades.append({
            "entry_date": entry_date,
            "exit_date": exit_date,
            "entry_price": entry_price,
            "exit_price": exit_price,
            "position": "long" if current_position == 1 else "short",
            "profit_loss": profit_loss,
            "profit_loss_pct": profit_loss_pct,
            "strategy": entry_strategy
        })

    # Calculate performance metrics
    total_return = portfolio['total'].iloc[-1] / initial_capital - 1
    daily_returns = portfolio['returns'].dropna()
    sharpe_ratio = daily_returns.mean() / daily_returns.std() * np.sqrt(252) if daily_returns.std() > 0 else 0

    # Calculate drawdown
    portfolio['drawdown'] = portfolio['total'] / portfolio['total'].cummax() - 1
    max_drawdown = portfolio['drawdown'].min()

    # Calculate win rate
    winning_trades = sum(1 for trade in trades if trade['profit_loss'] > 0)
    total_trades = len(trades)
    win_rate = winning_trades / total_trades if total_trades > 0 else 0

    # Calculate profit factor
    gross_profit = sum(trade['profit_loss'] for trade in trades if trade['profit_loss'] > 0)
    gross_loss = sum(abs(trade['profit_loss']) for trade in trades if trade['profit_loss'] < 0)
    profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')

    # Calculate strategy breakdown
    strategy_breakdown = {}
    for strategy in ["mean_reversion", "momentum"]:
        strategy_trades = [trade for trade in trades if trade['strategy'] == strategy]
        strategy_winning_trades = sum(1 for trade in strategy_trades if trade['profit_loss'] > 0)
        strategy_total_trades = len(strategy_trades)
        strategy_win_rate = strategy_winning_trades / strategy_total_trades if strategy_total_trades > 0 else 0
        strategy_profit = sum(trade['profit_loss'] for trade in strategy_trades)

        strategy_breakdown[strategy] = {
            "total_trades": strategy_total_trades,
            "winning_trades": strategy_winning_trades,
            "win_rate": strategy_win_rate,
            "total_profit": strategy_profit
        }

    # Create results dictionary
    results = {
        "strategy": "combined",
        "initial_capital": initial_capital,
        "final_capital": portfolio['total'].iloc[-1],
        "total_return": total_return,
        "sharpe_ratio": sharpe_ratio,
        "max_drawdown": max_drawdown,
        "win_rate": win_rate,
        "profit_factor": profit_factor,
        "total_trades": total_trades,
        "trades": trades,
        "portfolio": portfolio.to_dict(),
        "strategy_breakdown": strategy_breakdown
    }

    # Save results
    results_path = os.path.join(output_dir, "combined_validation_results.json")
    with open(results_path, 'w') as f:
        json.dump(results, f, indent=2, default=str)

    logger.info(f"Saved combined strategy validation results to {results_path}")

    # Log key metrics
    logger.info(f"Combined strategy validation results:")
    logger.info(f"  Total Return: {results['total_return']:.2%}")
    logger.info(f"  Sharpe Ratio: {results['sharpe_ratio']:.2f}")
    logger.info(f"  Max Drawdown: {results['max_drawdown']:.2%}")
    logger.info(f"  Win Rate: {results['win_rate']:.2%}")
    logger.info(f"  Profit Factor: {results['profit_factor']:.2f}")
    logger.info(f"  Total Trades: {results['total_trades']}")
    logger.info(f"  Strategy Breakdown:")
    for strategy, metrics in results['strategy_breakdown'].items():
        logger.info(f"    {strategy}: {metrics['total_trades']} trades, {metrics['win_rate']:.2%} win rate, ${metrics['total_profit']:.2f} profit")

    return results

def visualize_validation_results(mean_reversion_results: Dict[str, Any],
                               momentum_results: Dict[str, Any],
                               combined_results: Dict[str, Any],
                               output_dir: str) -> None:
    """
    Visualize validation results.

    Args:
        mean_reversion_results: Mean Reversion validation results
        momentum_results: Momentum validation results
        combined_results: Combined strategy validation results
        output_dir: Directory to save visualizations
    """
    logger.info("Visualizing validation results")

    # Create visualizer
    visualizer = StrategyVisualizer(output_dir)

    # Create directory for visualizations
    viz_dir = os.path.join(output_dir, "visualizations")
    os.makedirs(viz_dir, exist_ok=True)

    # Convert portfolio dictionaries to DataFrames
    mr_portfolio = pd.DataFrame.from_dict(mean_reversion_results['portfolio'])
    mom_portfolio = pd.DataFrame.from_dict(momentum_results['portfolio'])
    combined_portfolio = pd.DataFrame.from_dict(combined_results['portfolio'])

    # Plot equity curves
    plt.figure(figsize=(12, 6))
    plt.plot(mr_portfolio.index, mr_portfolio['total'], label='Mean Reversion')
    plt.plot(mom_portfolio.index, mom_portfolio['total'], label='Momentum')
    plt.plot(combined_portfolio.index, combined_portfolio['total'], label='Combined')
    plt.title('Equity Curves')
    plt.xlabel('Date')
    plt.ylabel('Portfolio Value')
    plt.legend()
    plt.grid(True)
    plt.savefig(os.path.join(viz_dir, 'equity_curves.png'))
    plt.close()

    # Plot drawdowns
    plt.figure(figsize=(12, 6))
    plt.plot(mr_portfolio.index, mr_portfolio['drawdown'] * 100, label='Mean Reversion')
    plt.plot(mom_portfolio.index, mom_portfolio['drawdown'] * 100, label='Momentum')
    plt.plot(combined_portfolio.index, combined_portfolio['drawdown'] * 100, label='Combined')
    plt.title('Drawdowns')
    plt.xlabel('Date')
    plt.ylabel('Drawdown (%)')
    plt.legend()
    plt.grid(True)
    plt.savefig(os.path.join(viz_dir, 'drawdowns.png'))
    plt.close()

    # Plot performance metrics comparison
    metrics = ['total_return', 'sharpe_ratio', 'max_drawdown', 'win_rate', 'profit_factor']
    strategies = ['Mean Reversion', 'Momentum', 'Combined']

    for metric in metrics:
        plt.figure(figsize=(10, 6))
        values = [
            mean_reversion_results[metric] * (100 if metric in ['total_return', 'max_drawdown', 'win_rate'] else 1),
            momentum_results[metric] * (100 if metric in ['total_return', 'max_drawdown', 'win_rate'] else 1),
            combined_results[metric] * (100 if metric in ['total_return', 'max_drawdown', 'win_rate'] else 1)
        ]
        plt.bar(strategies, values)
        plt.title(f'{metric.replace("_", " ").title()} Comparison')
        plt.ylabel(f'{metric.replace("_", " ").title()}' + (' (%)' if metric in ['total_return', 'max_drawdown', 'win_rate'] else ''))
        plt.grid(True)
        plt.savefig(os.path.join(viz_dir, f'{metric}_comparison.png'))
        plt.close()

    # Plot strategy breakdown for combined strategy
    strategy_breakdown = combined_results['strategy_breakdown']
    strategies = list(strategy_breakdown.keys())
    metrics = ['total_trades', 'win_rate', 'total_profit']

    for metric in metrics:
        plt.figure(figsize=(10, 6))
        values = [
            strategy_breakdown[strategy][metric] * (100 if metric == 'win_rate' else 1)
            for strategy in strategies
        ]
        plt.bar(strategies, values)
        plt.title(f'Strategy Breakdown - {metric.replace("_", " ").title()}')
        plt.ylabel(f'{metric.replace("_", " ").title()}' + (' (%)' if metric == 'win_rate' else ''))
        plt.grid(True)
        plt.savefig(os.path.join(viz_dir, f'strategy_breakdown_{metric}.png'))
        plt.close()

    logger.info(f"Saved visualizations to {viz_dir}")

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Validate trading strategies")
    parser.add_argument("--data", type=str, required=True, help="Path to out-of-sample data CSV")
    parser.add_argument("--mean-reversion-params", type=str, required=True, help="Path to Mean Reversion parameters JSON")
    parser.add_argument("--momentum-params", type=str, required=True, help="Path to Momentum parameters JSON")
    parser.add_argument("--output", type=str, default="output/validation", help="Output directory")
    parser.add_argument("--initial-capital", type=float, default=10000.0, help="Initial capital for backtesting")

    args = parser.parse_args()

    # Create logs directory if it doesn't exist
    os.makedirs(os.path.join(parent_dir, "logs"), exist_ok=True)

    # Create output directory if it doesn't exist
    os.makedirs(args.output, exist_ok=True)

    try:
        # Load data
        data = load_data(args.data)

        # Load parameters
        mean_reversion_params = load_parameters(args.mean_reversion_params)
        momentum_params = load_parameters(args.momentum_params)

        # Validate Mean Reversion strategy
        mean_reversion_results = validate_mean_reversion(
            data=data,
            params=mean_reversion_params,
            output_dir=args.output,
            initial_capital=args.initial_capital
        )

        # Validate Momentum strategy
        momentum_results = validate_momentum(
            data=data,
            params=momentum_params,
            output_dir=args.output,
            initial_capital=args.initial_capital
        )

        # Validate combined strategy
        combined_results = validate_combined_strategy(
            data=data,
            mean_reversion_params=mean_reversion_params,
            momentum_params=momentum_params,
            output_dir=args.output,
            initial_capital=args.initial_capital
        )

        # Visualize validation results
        visualize_validation_results(
            mean_reversion_results=mean_reversion_results,
            momentum_results=momentum_results,
            combined_results=combined_results,
            output_dir=args.output
        )

        logger.info("Validation completed successfully")

        return 0

    except Exception as e:
        logger.error(f"Error: {str(e)}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
