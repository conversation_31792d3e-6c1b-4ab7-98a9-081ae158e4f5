#!/usr/bin/env python3
"""
Simple Visualization Script for Synergy7 Trading System.

This script provides a simple way to visualize strategy performance metrics
without relying on complex visualization libraries.
"""

import os
import sys
import json
import logging
import argparse
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime

# Add parent directory to path
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(os.path.join(parent_dir, "logs", "simple_visualization.log")),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("simple_visualization")

def load_backtest_results(results_path: str) -> Dict[str, Any]:
    """
    Load backtest results from JSON file.
    
    Args:
        results_path: Path to backtest results JSON
        
    Returns:
        Dictionary with backtest results
    """
    logger.info(f"Loading backtest results from {results_path}")
    
    try:
        with open(results_path, 'r') as f:
            results = json.load(f)
        
        logger.info(f"Loaded backtest results with {len(results.get('trades', []))} trades")
        return results
    
    except Exception as e:
        logger.error(f"Error loading backtest results: {str(e)}")
        raise

def load_optimization_results(results_path: str) -> List[Dict[str, Any]]:
    """
    Load optimization results from JSON file.
    
    Args:
        results_path: Path to optimization results JSON
        
    Returns:
        List of optimization results
    """
    logger.info(f"Loading optimization results from {results_path}")
    
    try:
        with open(results_path, 'r') as f:
            results = json.load(f)
        
        logger.info(f"Loaded optimization results with {len(results)} parameter combinations")
        return results
    
    except Exception as e:
        logger.error(f"Error loading optimization results: {str(e)}")
        raise

def load_market_regime_results(results_path: str) -> Dict[str, Any]:
    """
    Load market regime results from JSON file.
    
    Args:
        results_path: Path to market regime results JSON
        
    Returns:
        Dictionary with market regime results
    """
    logger.info(f"Loading market regime results from {results_path}")
    
    try:
        with open(results_path, 'r') as f:
            results = json.load(f)
        
        logger.info(f"Loaded market regime results with {len(results.get('regimes', []))} regime periods")
        return results
    
    except Exception as e:
        logger.error(f"Error loading market regime results: {str(e)}")
        raise

def print_performance_summary(backtest_results: Dict[str, Any]) -> None:
    """
    Print performance summary.
    
    Args:
        backtest_results: Dictionary with backtest results
    """
    logger.info("Printing performance summary")
    
    print("\n" + "="*80)
    print(" "*30 + "PERFORMANCE SUMMARY")
    print("="*80)
    
    # Print strategy info
    print(f"Strategy: {backtest_results.get('strategy', 'Unknown')}")
    print(f"Version: {backtest_results.get('version', 'Unknown')}")
    print(f"Symbol: {backtest_results.get('symbol', 'Unknown')}")
    print("-"*80)
    
    # Print performance metrics
    print("Performance Metrics:")
    print(f"  Initial Capital: ${backtest_results.get('initial_capital', 0):.2f}")
    print(f"  Final Capital: ${backtest_results.get('final_capital', 0):.2f}")
    print(f"  Total Return: {backtest_results.get('total_return', 0)*100:.2f}%")
    print(f"  Sharpe Ratio: {backtest_results.get('sharpe_ratio', 0):.2f}")
    print(f"  Max Drawdown: {backtest_results.get('max_drawdown', 0)*100:.2f}%")
    print(f"  Win Rate: {backtest_results.get('win_rate', 0)*100:.2f}%")
    print(f"  Profit Factor: {backtest_results.get('profit_factor', 0):.2f}")
    print(f"  Total Trades: {backtest_results.get('total_trades', 0)}")
    print("-"*80)
    
    # Print trade summary
    trades = backtest_results.get('trades', [])
    if trades:
        winning_trades = sum(1 for trade in trades if trade.get('profit_loss', 0) > 0)
        losing_trades = sum(1 for trade in trades if trade.get('profit_loss', 0) < 0)
        
        print("Trade Summary:")
        print(f"  Total Trades: {len(trades)}")
        print(f"  Winning Trades: {winning_trades} ({winning_trades/len(trades)*100:.2f}%)")
        print(f"  Losing Trades: {losing_trades} ({losing_trades/len(trades)*100:.2f}%)")
        
        # Calculate average profit/loss
        avg_profit = sum(trade.get('profit_loss', 0) for trade in trades if trade.get('profit_loss', 0) > 0) / winning_trades if winning_trades > 0 else 0
        avg_loss = sum(abs(trade.get('profit_loss', 0)) for trade in trades if trade.get('profit_loss', 0) < 0) / losing_trades if losing_trades > 0 else 0
        
        print(f"  Average Profit: ${avg_profit:.2f}")
        print(f"  Average Loss: ${avg_loss:.2f}")
        print(f"  Profit/Loss Ratio: {avg_profit/avg_loss:.2f}" if avg_loss > 0 else "  Profit/Loss Ratio: ∞")
    
    print("="*80 + "\n")

def print_optimization_summary(optimization_results: List[Dict[str, Any]], top_n: int = 5) -> None:
    """
    Print optimization summary.
    
    Args:
        optimization_results: List of optimization results
        top_n: Number of top results to print
    """
    logger.info(f"Printing optimization summary for top {top_n} results")
    
    print("\n" + "="*100)
    print(" "*35 + "OPTIMIZATION SUMMARY")
    print("="*100)
    
    # Sort results by combined score
    sorted_results = sorted(optimization_results, key=lambda x: x.get('combined_score', 0), reverse=True)
    
    # Print top N results
    print(f"Top {top_n} Parameter Combinations:")
    print("-"*100)
    
    # Print header
    print(f"{'Rank':<5} {'Combined Score':<15} {'Train Sharpe':<12} {'Test Sharpe':<12} {'Train Return':<12} {'Test Return':<12} {'Parameters'}")
    print("-"*100)
    
    for i, result in enumerate(sorted_results[:top_n]):
        # Get metrics
        combined_score = result.get('combined_score', 0)
        train_metrics = result.get('train_metrics', {})
        test_metrics = result.get('test_metrics', {})
        params = result.get('params', {})
        
        # Format parameters
        param_str = ", ".join(f"{k}={v}" for k, v in params.items())
        
        # Print result
        print(f"{i+1:<5} {combined_score:<15.4f} {train_metrics.get('sharpe_ratio', 0):<12.2f} {test_metrics.get('sharpe_ratio', 0):<12.2f} {train_metrics.get('total_return', 0)*100:<12.2f}% {test_metrics.get('total_return', 0)*100:<12.2f}% {param_str}")
    
    print("="*100 + "\n")

def print_regime_summary(market_regime_results: Dict[str, Any]) -> None:
    """
    Print market regime summary.
    
    Args:
        market_regime_results: Dictionary with market regime results
    """
    logger.info("Printing market regime summary")
    
    print("\n" + "="*80)
    print(" "*30 + "MARKET REGIME SUMMARY")
    print("="*80)
    
    # Print regime summary
    summary = market_regime_results.get('summary', {})
    
    print("Regime Distribution:")
    print("-"*80)
    
    # Print header
    print(f"{'Regime':<15} {'Count':<8} {'Total Duration':<15} {'Avg Duration':<15} {'% of Time'}")
    print("-"*80)
    
    for regime, stats in summary.items():
        count = stats.get('count', 0)
        total_duration = stats.get('total_duration', 0)
        avg_duration = stats.get('avg_duration', 0)
        pct_time = stats.get('pct_time', 0)
        
        print(f"{regime:<15} {count:<8} {total_duration:<15.1f} {avg_duration:<15.1f} {pct_time:.2f}%")
    
    print("-"*80)
    
    # Print regime transitions
    transitions = market_regime_results.get('transitions', [])
    
    if transitions:
        print("\nRegime Transitions:")
        print("-"*80)
        
        # Print header
        print(f"{'From':<15} {'To':<15} {'Time'}")
        print("-"*80)
        
        for transition in transitions[:10]:  # Print first 10 transitions
            from_regime = transition.get('from', '')
            to_regime = transition.get('to', '')
            time = transition.get('time', '')
            
            print(f"{from_regime:<15} {to_regime:<15} {time}")
        
        if len(transitions) > 10:
            print(f"... and {len(transitions) - 10} more transitions")
    
    print("="*80 + "\n")

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Simple visualization for strategy performance")
    parser.add_argument("--backtest", type=str, help="Path to backtest results JSON")
    parser.add_argument("--optimization", type=str, help="Path to optimization results JSON")
    parser.add_argument("--regime", type=str, help="Path to market regime results JSON")
    parser.add_argument("--top-n", type=int, default=5, help="Number of top optimization results to print")
    
    args = parser.parse_args()
    
    # Create logs directory if it doesn't exist
    os.makedirs(os.path.join(parent_dir, "logs"), exist_ok=True)
    
    try:
        # Load backtest results if provided
        if args.backtest:
            backtest_results = load_backtest_results(args.backtest)
            print_performance_summary(backtest_results)
        
        # Load optimization results if provided
        if args.optimization:
            optimization_results = load_optimization_results(args.optimization)
            print_optimization_summary(optimization_results, args.top_n)
        
        # Load market regime results if provided
        if args.regime:
            market_regime_results = load_market_regime_results(args.regime)
            print_regime_summary(market_regime_results)
        
        # If no arguments provided, print help
        if not (args.backtest or args.optimization or args.regime):
            parser.print_help()
        
        return 0
    
    except Exception as e:
        logger.error(f"Error: {str(e)}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
