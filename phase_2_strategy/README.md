# Synergy7 Strategy Finder

This module implements a comprehensive strategy finding system for the Synergy7 trading platform. It combines data collection, market regime detection, enhanced strategy implementations, robust risk management, and visualization tools to identify and validate optimal trading strategies for different market conditions.

## Overview

The strategy finder consists of the following components:

1. **Data Collection**: Tools for collecting high-quality historical data for SOL/USD from multiple sources (Binance, Birdeye, CoinGecko).
2. **Data Preparation**: Tools for calculating additional features and splitting data into training and testing sets.
3. **Market Regime Detection**: Identifies different market conditions (trending, ranging, volatile, choppy) to adapt trading strategies accordingly.
4. **Enhanced Mean Reversion Strategy**: An improved mean reversion strategy with adaptive parameters based on market volatility.
5. **Enhanced Momentum Strategy**: A revised momentum strategy with trend confirmation, pullback entry, and improved exit rules.
6. **Robust Risk Management**: Dynamic position sizing, improved stop-loss mechanisms, and circuit breakers to protect capital.
7. **Strategy Optimization**: Parameter optimization for both Mean Reversion and Momentum strategies using grid search and cross-validation.
8. **Combined Strategy Backtest**: Backtesting of the combined strategy with regime-based strategy selection.
9. **Performance Visualization**: Tools for visualizing strategy performance metrics, parameter sensitivity, and market regime analysis.

## Components

### Data Collection (`data_collection.py`)

Collects high-quality historical data for SOL/USD from multiple sources:

- Binance (via CCXT)
- Birdeye API
- CoinGecko API

Merges data from multiple sources for better quality and validates data integrity.

### Data Preparation (`prepare_data.py`)

Prepares collected data for strategy optimization:

- Calculates technical indicators (Moving Averages, Bollinger Bands, RSI, MACD, ATR, ADX, etc.)
- Splits data into training and testing sets
- Visualizes key metrics and indicators

### Market Regime Detection (`market_regime.py`)

Detects the current market regime based on various indicators:

- ADX for trend strength
- Bollinger Bands for volatility
- Choppiness Index for choppy markets

Provides strategy recommendations based on the detected regime.

### Mean Reversion Strategy (`mean_reversion.py`)

An enhanced mean reversion strategy with:

- Adaptive parameters based on market volatility
- Bollinger Bands for overbought/oversold conditions
- RSI confirmation
- Improved exit rules with trailing stops

### Momentum Strategy (`momentum_strategy.py`)

A revised momentum strategy with:

- Multiple trend confirmation filters (EMA alignment, ADX)
- Pullback entry for better entry timing
- Volume confirmation
- ATR-based stop loss and take profit
- Trailing stops for profit protection

### Risk Management (`risk_management.py`)

Robust risk management features:

- Dynamic position sizing based on ATR
- Improved stop-loss mechanisms with trailing stops
- Circuit breakers to protect capital during adverse conditions
- Portfolio-level risk controls

### Strategy Optimization

- `optimize_mean_reversion.py`: Optimizes Mean Reversion strategy parameters
- `optimize_momentum.py`: Optimizes Momentum strategy parameters

### Strategy Finder (`strategy_finder.py`)

Combines all components to find optimal trading strategies:

- Detects market regimes in historical data
- Backtests strategies on different market regimes
- Analyzes performance by market regime
- Visualizes results

## Usage

### Running the Strategy Finder

```bash
python run_strategy_finder.py --data path/to/data.csv --output output/directory
```

Options:
- `--data`: Path to historical data CSV
- `--output`: Output directory (default: "output/strategy_finder")
- `--symbol`: Trading symbol (default: "SOL/USD")
- `--timeframe`: Timeframe (default: "1h")
- `--start-date`: Start date (default: "2023-01-01")
- `--end-date`: End date (default: "2023-12-31")
- `--skip-download`: Skip data download
- `--skip-regime`: Skip market regime detection
- `--skip-mean-reversion`: Skip Mean Reversion optimization
- `--skip-momentum`: Skip Momentum optimization
- `--skip-combined`: Skip combined strategy backtest

### Optimizing Mean Reversion Strategy

```bash
python optimize_mean_reversion.py --data path/to/data.csv --output output/mean_reversion
```

Options:
- `--data`: Path to historical data CSV
- `--output`: Output directory (default: "output/mean_reversion_optimization")
- `--test-size`: Proportion of data to use for testing (default: 0.3)
- `--top-n`: Number of top parameter combinations to return (default: 10)

### Optimizing Momentum Strategy

```bash
python optimize_momentum.py --data path/to/data.csv --output output/momentum
```

Options:
- `--data`: Path to historical data CSV
- `--output`: Output directory (default: "output/momentum_optimization")
- `--test-size`: Proportion of data to use for testing (default: 0.3)
- `--top-n`: Number of top parameter combinations to return (default: 10)
- `--max-combinations`: Maximum number of parameter combinations to test (default: 100)

## Data Format

The strategy finder expects historical data in CSV format with the following columns:

- `timestamp`: Timestamp (optional, will be used as index if present)
- `open`: Open price
- `high`: High price
- `low`: Low price
- `close`: Close price
- `volume`: Volume

## Output

The strategy finder generates the following output:

- **Market Regime Detection**:
  - `market_regime_results.json`: Market regime periods and transitions

- **Mean Reversion Optimization**:
  - `all_results.json`: All parameter combinations with performance metrics
  - `top_results.json`: Top parameter combinations with performance metrics
  - `best_strategy_results.json`: Backtest results for the best parameter combination
  - `visualizations/`: Visualizations of optimization results

- **Momentum Optimization**:
  - `all_results.json`: All parameter combinations with performance metrics
  - `top_results.json`: Top parameter combinations with performance metrics
  - `best_strategy_results.json`: Backtest results for the best parameter combination
  - `visualizations/`: Visualizations of optimization results

- **Combined Strategy Backtest**:
  - `results.json`: Backtest results for the combined strategy
  - `visualizations/`: Visualizations of backtest results

## Implementation Details

### Market Regime Detection

The market regime detector uses the following indicators:

- **ADX (Average Directional Index)**: Measures trend strength
- **Bollinger Bands**: Measures volatility
- **Choppiness Index**: Identifies choppy markets

Market regimes are classified as:
- **Trending Up**: Strong uptrend (ADX > 25, +DI > -DI)
- **Trending Down**: Strong downtrend (ADX > 25, -DI > +DI)
- **Ranging**: Low volatility, weak trend (ADX < 20)
- **Volatile**: High volatility (Bollinger Band width > threshold)
- **Choppy**: Frequent direction changes (Choppiness Index > 50)

### Mean Reversion Strategy

The mean reversion strategy identifies overbought and oversold conditions using:

- **Bollinger Bands**: Price exceeding upper/lower bands
- **RSI (Relative Strength Index)**: Confirmation of overbought/oversold conditions

Parameters are adapted based on market volatility:
- **Low Volatility**: Tighter Bollinger Bands (1.5 standard deviations)
- **Medium Volatility**: Standard Bollinger Bands (2.0 standard deviations)
- **High Volatility**: Wider Bollinger Bands (2.5 standard deviations)

### Momentum Strategy

The momentum strategy identifies trends and enters on pullbacks:

- **EMA Alignment**: Short-term EMA > Medium-term EMA > Long-term EMA for uptrends
- **ADX**: Confirms trend strength (ADX > 25)
- **Pullbacks**: Enters on pullbacks within trends
- **RSI**: Confirms oversold conditions in uptrends, overbought in downtrends
- **Volume**: Confirms entry with above-average volume

### Risk Management

The risk manager implements:

- **Position Sizing**: Based on ATR and risk per trade
- **Stop Loss**: ATR-based stop loss with trailing stops
- **Take Profit**: ATR-based take profit levels with partial exits
- **Circuit Breakers**: Pauses trading after consecutive losses or daily loss limit

## Performance Metrics

The strategy finder evaluates strategies using the following metrics:

- **Sharpe Ratio**: Risk-adjusted return
- **Total Return**: Overall return
- **Max Drawdown**: Maximum peak-to-trough decline
- **Win Rate**: Percentage of winning trades
- **Profit Factor**: Gross profit / Gross loss

### Performance Visualization

The visualization system provides comprehensive visual statistics for strategy validation, parameter optimization, and performance analysis:

#### Simple Visualization (`simple_visualization.py`)

Provides a quick overview of strategy performance:

- Performance metrics (Total return, Sharpe ratio, drawdown, win rate, etc.)
- Optimization results (Top parameter combinations and their performance)
- Market regime analysis (Performance by market regime and regime transitions)

#### Advanced Visualization (`visualization.py` and `visualize_strategy.py`)

Generates detailed visualizations for in-depth analysis:

- Equity curve and drawdown charts
- Returns distribution and monthly returns heatmap
- Rolling Sharpe ratio and underwater equity curve
- Parameter sensitivity analysis
- Performance by market regime

## Running the Complete Workflow

### 1. Collect Data

```bash
python phase_2_strategy/run_data_collection.py \
  --symbol SOL/USDT \
  --timeframe 1h \
  --start-date 2023-01-01 \
  --end-date 2023-12-31 \
  --output data \
  --sources binance birdeye coingecko
```

### 2. Optimize Strategies

```bash
python phase_2_strategy/run_strategy_finder.py \
  --data data/prepared/full_data.csv \
  --output results
```

### 3. Visualize Performance

```bash
python phase_2_strategy/visualize_strategy.py \
  --backtest results/combined_strategy/results.json \
  --optimization results/mean_reversion/top_results.json \
  --regime results/market_regime/market_regime_results.json \
  --output visualizations
```

## Dependencies

- Python 3.7+
- NumPy
- Pandas
- Matplotlib
- Seaborn
- Plotly
- CCXT
- Requests
- tqdm

## Documentation

- [DATA_COLLECTION.md](DATA_COLLECTION.md): Detailed documentation for data collection
- [VISUALIZATION.md](VISUALIZATION.md): Detailed documentation for visualization tools

## License

This software is proprietary and confidential. Unauthorized copying, distribution, or use is strictly prohibited.
