"""
Enhanced Visualization Module for Synergy7 Trading System.

This module provides comprehensive visualization capabilities for strategy validation,
parameter optimization, and performance analysis with a focus on transparency,
strategy control, and impact assessment.
"""

import os
import logging
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import seaborn as sns
from typing import Dict, Any, List, Optional, Tuple, Union
from datetime import datetime, timedelta
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots

# Configure logging
logger = logging.getLogger(__name__)

class StrategyVisualizer:
    """
    Enhanced Strategy Visualizer.
    
    This class provides comprehensive visualization capabilities for strategy validation,
    parameter optimization, and performance analysis.
    """
    
    def __init__(self, output_dir: str, dark_mode: bool = False):
        """
        Initialize the strategy visualizer.
        
        Args:
            output_dir: Directory to save visualizations
            dark_mode: Whether to use dark mode for visualizations
        """
        self.output_dir = output_dir
        self.dark_mode = dark_mode
        
        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)
        
        # Set style
        if dark_mode:
            plt.style.use('dark_background')
            self.colors = {
                'primary': '#00FFFF',  # Cyan
                'secondary': '#FF00FF',  # Magenta
                'positive': '#00FF00',  # Green
                'negative': '#FF0000',  # Red
                'neutral': '#FFFFFF',  # White
                'background': '#121212',  # Dark gray
                'grid': '#333333'  # Light gray
            }
        else:
            plt.style.use('seaborn-v0_8-whitegrid')
            self.colors = {
                'primary': '#1F77B4',  # Blue
                'secondary': '#FF7F0E',  # Orange
                'positive': '#2CA02C',  # Green
                'negative': '#D62728',  # Red
                'neutral': '#7F7F7F',  # Gray
                'background': '#FFFFFF',  # White
                'grid': '#E5E5E5'  # Light gray
            }
        
        logger.info(f"Initialized strategy visualizer with output directory: {output_dir}")
    
    def save_figure(self, fig, filename: str, formats: List[str] = None) -> List[str]:
        """
        Save figure to file.
        
        Args:
            fig: Figure to save
            filename: Filename without extension
            formats: List of formats to save (default: ['png', 'pdf'])
            
        Returns:
            List of saved file paths
        """
        if formats is None:
            formats = ['png', 'pdf']
        
        saved_paths = []
        
        for fmt in formats:
            filepath = os.path.join(self.output_dir, f"{filename}.{fmt}")
            
            if isinstance(fig, (plt.Figure, plt.Axes)):
                fig.figure.savefig(filepath, dpi=300, bbox_inches='tight')
            elif hasattr(fig, 'write_image'):
                fig.write_image(filepath)
            
            saved_paths.append(filepath)
            logger.info(f"Saved figure to {filepath}")
        
        return saved_paths
    
    def plot_equity_curve(self, portfolio_df: pd.DataFrame, benchmark_df: pd.DataFrame = None,
                         title: str = "Equity Curve", filename: str = "equity_curve") -> plt.Figure:
        """
        Plot equity curve with optional benchmark comparison.
        
        Args:
            portfolio_df: DataFrame with portfolio value
            benchmark_df: DataFrame with benchmark value (optional)
            title: Plot title
            filename: Filename to save
            
        Returns:
            Figure object
        """
        logger.info("Plotting equity curve")
        
        fig, ax = plt.subplots(figsize=(12, 6))
        
        # Plot portfolio equity curve
        if 'total' in portfolio_df.columns:
            ax.plot(portfolio_df.index, portfolio_df['total'], 
                   label='Strategy', color=self.colors['primary'], linewidth=2)
        elif 'equity' in portfolio_df.columns:
            ax.plot(portfolio_df.index, portfolio_df['equity'], 
                   label='Strategy', color=self.colors['primary'], linewidth=2)
        else:
            ax.plot(portfolio_df.index, portfolio_df.iloc[:, 0], 
                   label='Strategy', color=self.colors['primary'], linewidth=2)
        
        # Plot benchmark if provided
        if benchmark_df is not None:
            if 'close' in benchmark_df.columns:
                # Normalize benchmark to start at the same value as portfolio
                benchmark_values = benchmark_df['close'] / benchmark_df['close'].iloc[0] * portfolio_df.iloc[0, 0]
                ax.plot(benchmark_df.index, benchmark_values, 
                       label='Benchmark', color=self.colors['secondary'], linewidth=1.5, alpha=0.7)
        
        # Format plot
        ax.set_title(title, fontsize=14)
        ax.set_xlabel('Date', fontsize=12)
        ax.set_ylabel('Portfolio Value', fontsize=12)
        ax.grid(True, alpha=0.3)
        ax.legend()
        
        # Format x-axis dates
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        fig.autofmt_xdate()
        
        # Save figure
        self.save_figure(fig, filename)
        
        return fig
    
    def plot_drawdown(self, portfolio_df: pd.DataFrame, title: str = "Drawdown",
                     filename: str = "drawdown") -> plt.Figure:
        """
        Plot drawdown chart.
        
        Args:
            portfolio_df: DataFrame with portfolio value
            title: Plot title
            filename: Filename to save
            
        Returns:
            Figure object
        """
        logger.info("Plotting drawdown")
        
        fig, ax = plt.subplots(figsize=(12, 6))
        
        # Calculate drawdown if not already in DataFrame
        if 'drawdown' not in portfolio_df.columns:
            if 'total' in portfolio_df.columns:
                equity = portfolio_df['total']
            elif 'equity' in portfolio_df.columns:
                equity = portfolio_df['equity']
            else:
                equity = portfolio_df.iloc[:, 0]
            
            # Calculate drawdown
            portfolio_df['drawdown'] = equity / equity.cummax() - 1
        
        # Plot drawdown
        ax.fill_between(portfolio_df.index, 0, portfolio_df['drawdown'] * 100, 
                       color=self.colors['negative'], alpha=0.5)
        ax.plot(portfolio_df.index, portfolio_df['drawdown'] * 100, 
               color=self.colors['negative'], linewidth=1)
        
        # Format plot
        ax.set_title(title, fontsize=14)
        ax.set_xlabel('Date', fontsize=12)
        ax.set_ylabel('Drawdown (%)', fontsize=12)
        ax.grid(True, alpha=0.3)
        
        # Format y-axis as percentage
        ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: f'{y:.0f}%'))
        
        # Format x-axis dates
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        fig.autofmt_xdate()
        
        # Save figure
        self.save_figure(fig, filename)
        
        return fig
    
    def plot_returns_distribution(self, returns: pd.Series, title: str = "Returns Distribution",
                                filename: str = "returns_distribution") -> plt.Figure:
        """
        Plot returns distribution with key statistics.
        
        Args:
            returns: Series of returns
            title: Plot title
            filename: Filename to save
            
        Returns:
            Figure object
        """
        logger.info("Plotting returns distribution")
        
        fig, ax = plt.subplots(figsize=(12, 6))
        
        # Plot histogram
        sns.histplot(returns, kde=True, ax=ax, color=self.colors['primary'], 
                    stat='density', alpha=0.6)
        
        # Plot normal distribution for comparison
        x = np.linspace(returns.min(), returns.max(), 100)
        y = np.exp(-(x - returns.mean())**2 / (2 * returns.std()**2)) / (returns.std() * np.sqrt(2 * np.pi))
        ax.plot(x, y, color=self.colors['secondary'], linewidth=2, label='Normal Distribution')
        
        # Add vertical lines for key statistics
        ax.axvline(x=0, color=self.colors['neutral'], linestyle='--', alpha=0.5)
        ax.axvline(x=returns.mean(), color=self.colors['positive'], linestyle='-', 
                  label=f'Mean: {returns.mean():.4f}')
        
        # Calculate and display key statistics
        stats_text = (
            f"Mean: {returns.mean():.4f}\n"
            f"Median: {returns.median():.4f}\n"
            f"Std Dev: {returns.std():.4f}\n"
            f"Skew: {returns.skew():.4f}\n"
            f"Kurtosis: {returns.kurtosis():.4f}\n"
            f"Sharpe: {returns.mean() / returns.std() * np.sqrt(252):.4f}\n"
            f"Sortino: {returns.mean() / returns[returns < 0].std() * np.sqrt(252):.4f}\n"
            f"Max Drawdown: {(returns.cumsum() - returns.cumsum().cummax()).min():.4f}"
        )
        
        # Add text box with statistics
        props = dict(boxstyle='round', facecolor=self.colors['background'], alpha=0.5)
        ax.text(0.05, 0.95, stats_text, transform=ax.transAxes, fontsize=10,
               verticalalignment='top', bbox=props)
        
        # Format plot
        ax.set_title(title, fontsize=14)
        ax.set_xlabel('Returns', fontsize=12)
        ax.set_ylabel('Density', fontsize=12)
        ax.grid(True, alpha=0.3)
        ax.legend()
        
        # Save figure
        self.save_figure(fig, filename)
        
        return fig
    
    def plot_monthly_returns_heatmap(self, returns: pd.Series, title: str = "Monthly Returns",
                                   filename: str = "monthly_returns_heatmap") -> plt.Figure:
        """
        Plot monthly returns heatmap.
        
        Args:
            returns: Series of returns with datetime index
            title: Plot title
            filename: Filename to save
            
        Returns:
            Figure object
        """
        logger.info("Plotting monthly returns heatmap")
        
        # Resample returns to monthly
        monthly_returns = returns.resample('M').apply(lambda x: (1 + x).prod() - 1)
        
        # Create monthly returns by year
        returns_by_month = monthly_returns.groupby([monthly_returns.index.year, monthly_returns.index.month]).first()
        returns_by_month = returns_by_month.unstack(level=1)
        
        # Replace month numbers with month names
        month_names = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
        returns_by_month.columns = [month_names[i-1] for i in returns_by_month.columns]
        
        # Create figure
        fig, ax = plt.subplots(figsize=(12, 8))
        
        # Create heatmap
        cmap = sns.diverging_palette(10, 133, as_cmap=True)
        sns.heatmap(returns_by_month * 100, annot=True, fmt=".2f", cmap=cmap, center=0,
                   linewidths=0.5, cbar_kws={'label': 'Returns (%)'},
                   ax=ax)
        
        # Format plot
        ax.set_title(title, fontsize=14)
        ax.set_ylabel('Year', fontsize=12)
        
        # Add year totals
        year_returns = returns.resample('A').apply(lambda x: (1 + x).prod() - 1)
        year_returns.index = year_returns.index.year
        
        # Create a new column for year totals
        ax.text(returns_by_month.shape[1] + 0.5, 0.5, 'Year Total', 
               fontsize=10, ha='center', va='center', fontweight='bold')
        
        for i, year in enumerate(returns_by_month.index):
            if year in year_returns.index:
                value = year_returns.loc[year] * 100
                color = 'green' if value >= 0 else 'red'
                ax.text(returns_by_month.shape[1] + 0.5, i + 0.5, f"{value:.2f}%", 
                       fontsize=9, ha='center', va='center', color=color, fontweight='bold')
        
        # Save figure
        self.save_figure(fig, filename)
        
        return fig
    
    def plot_rolling_sharpe(self, returns: pd.Series, window: int = 252,
                          title: str = "Rolling Sharpe Ratio", filename: str = "rolling_sharpe") -> plt.Figure:
        """
        Plot rolling Sharpe ratio.
        
        Args:
            returns: Series of returns
            window: Rolling window size
            title: Plot title
            filename: Filename to save
            
        Returns:
            Figure object
        """
        logger.info(f"Plotting rolling Sharpe ratio with window={window}")
        
        fig, ax = plt.subplots(figsize=(12, 6))
        
        # Calculate rolling Sharpe ratio
        rolling_mean = returns.rolling(window=window).mean()
        rolling_std = returns.rolling(window=window).std()
        rolling_sharpe = rolling_mean / rolling_std * np.sqrt(252)
        
        # Plot rolling Sharpe ratio
        ax.plot(rolling_sharpe.index, rolling_sharpe, color=self.colors['primary'], linewidth=2)
        
        # Add horizontal line at 0
        ax.axhline(y=0, color=self.colors['neutral'], linestyle='--', alpha=0.5)
        
        # Add horizontal line at 1
        ax.axhline(y=1, color=self.colors['positive'], linestyle='--', alpha=0.5)
        
        # Format plot
        ax.set_title(title, fontsize=14)
        ax.set_xlabel('Date', fontsize=12)
        ax.set_ylabel(f'Rolling Sharpe Ratio ({window} days)', fontsize=12)
        ax.grid(True, alpha=0.3)
        
        # Format x-axis dates
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        fig.autofmt_xdate()
        
        # Save figure
        self.save_figure(fig, filename)
        
        return fig
    
    def plot_underwater_equity(self, portfolio_df: pd.DataFrame, title: str = "Underwater Equity Curve",
                             filename: str = "underwater_equity") -> plt.Figure:
        """
        Plot underwater equity curve (drawdown-adjusted equity).
        
        Args:
            portfolio_df: DataFrame with portfolio value
            title: Plot title
            filename: Filename to save
            
        Returns:
            Figure object
        """
        logger.info("Plotting underwater equity curve")
        
        fig, ax = plt.subplots(figsize=(12, 6))
        
        # Get equity series
        if 'total' in portfolio_df.columns:
            equity = portfolio_df['total']
        elif 'equity' in portfolio_df.columns:
            equity = portfolio_df['equity']
        else:
            equity = portfolio_df.iloc[:, 0]
        
        # Calculate drawdown
        drawdown = equity / equity.cummax() - 1
        
        # Calculate underwater equity
        underwater_equity = equity * (1 + drawdown)
        
        # Plot equity curve
        ax.plot(equity.index, equity, color=self.colors['primary'], linewidth=2, alpha=0.5, label='Equity')
        
        # Plot underwater equity
        ax.plot(underwater_equity.index, underwater_equity, color=self.colors['negative'], linewidth=2, label='Underwater Equity')
        
        # Fill the area between equity and underwater equity
        ax.fill_between(equity.index, equity, underwater_equity, color=self.colors['negative'], alpha=0.3)
        
        # Format plot
        ax.set_title(title, fontsize=14)
        ax.set_xlabel('Date', fontsize=12)
        ax.set_ylabel('Portfolio Value', fontsize=12)
        ax.grid(True, alpha=0.3)
        ax.legend()
        
        # Format x-axis dates
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        fig.autofmt_xdate()
        
        # Save figure
        self.save_figure(fig, filename)
        
        return fig
    
    def plot_regime_performance(self, returns: pd.Series, regimes: pd.Series,
                              title: str = "Performance by Market Regime",
                              filename: str = "regime_performance") -> plt.Figure:
        """
        Plot performance by market regime.
        
        Args:
            returns: Series of returns
            regimes: Series of market regimes with same index as returns
            title: Plot title
            filename: Filename to save
            
        Returns:
            Figure object
        """
        logger.info("Plotting performance by market regime")
        
        # Calculate cumulative returns by regime
        regime_returns = {}
        regime_sharpe = {}
        regime_drawdown = {}
        regime_win_rate = {}
        
        for regime in regimes.unique():
            # Get returns for this regime
            regime_mask = regimes == regime
            if regime_mask.sum() == 0:
                continue
                
            regime_ret = returns[regime_mask]
            
            # Calculate cumulative return
            cum_ret = (1 + regime_ret).prod() - 1
            regime_returns[regime] = cum_ret
            
            # Calculate Sharpe ratio
            sharpe = regime_ret.mean() / regime_ret.std() * np.sqrt(252) if regime_ret.std() > 0 else 0
            regime_sharpe[regime] = sharpe
            
            # Calculate max drawdown
            cum_ret_series = (1 + regime_ret).cumprod()
            drawdown = (cum_ret_series / cum_ret_series.cummax() - 1).min()
            regime_drawdown[regime] = drawdown
            
            # Calculate win rate
            win_rate = (regime_ret > 0).mean()
            regime_win_rate[regime] = win_rate
        
        # Create figure with subplots
        fig, axes = plt.subplots(2, 2, figsize=(14, 10))
        
        # Plot cumulative returns by regime
        axes[0, 0].bar(regime_returns.keys(), [r * 100 for r in regime_returns.values()], 
                      color=[self.colors['positive'] if r > 0 else self.colors['negative'] for r in regime_returns.values()])
        axes[0, 0].set_title('Cumulative Returns by Regime (%)')
        axes[0, 0].set_ylabel('Return (%)')
        axes[0, 0].grid(True, alpha=0.3)
        
        # Plot Sharpe ratio by regime
        axes[0, 1].bar(regime_sharpe.keys(), list(regime_sharpe.values()), 
                      color=[self.colors['positive'] if s > 0 else self.colors['negative'] for s in regime_sharpe.values()])
        axes[0, 1].set_title('Sharpe Ratio by Regime')
        axes[0, 1].set_ylabel('Sharpe Ratio')
        axes[0, 1].grid(True, alpha=0.3)
        
        # Plot max drawdown by regime
        axes[1, 0].bar(regime_drawdown.keys(), [d * 100 for d in regime_drawdown.values()], 
                      color=self.colors['negative'])
        axes[1, 0].set_title('Max Drawdown by Regime (%)')
        axes[1, 0].set_ylabel('Drawdown (%)')
        axes[1, 0].grid(True, alpha=0.3)
        
        # Plot win rate by regime
        axes[1, 1].bar(regime_win_rate.keys(), [w * 100 for w in regime_win_rate.values()], 
                      color=self.colors['primary'])
        axes[1, 1].set_title('Win Rate by Regime (%)')
        axes[1, 1].set_ylabel('Win Rate (%)')
        axes[1, 1].grid(True, alpha=0.3)
        
        # Add overall title
        fig.suptitle(title, fontsize=16)
        fig.tight_layout(rect=[0, 0, 1, 0.95])
        
        # Save figure
        self.save_figure(fig, filename)
        
        return fig
    
    def plot_parameter_sensitivity(self, param_results: List[Dict[str, Any]], param_name: str,
                                 metric_name: str = "sharpe_ratio", title: str = None,
                                 filename: str = None) -> plt.Figure:
        """
        Plot parameter sensitivity.
        
        Args:
            param_results: List of parameter optimization results
            param_name: Name of parameter to analyze
            metric_name: Name of metric to plot
            title: Plot title
            filename: Filename to save
            
        Returns:
            Figure object
        """
        if title is None:
            title = f"{metric_name.replace('_', ' ').title()} vs {param_name.replace('_', ' ').title()}"
        
        if filename is None:
            filename = f"{metric_name}_vs_{param_name}"
        
        logger.info(f"Plotting parameter sensitivity for {param_name}")
        
        fig, ax = plt.subplots(figsize=(12, 6))
        
        # Extract parameter values and metric values
        param_values = []
        train_metrics = []
        test_metrics = []
        
        for result in param_results:
            if param_name in result["params"]:
                param_values.append(result["params"][param_name])
                train_metrics.append(result["train_metrics"][metric_name])
                test_metrics.append(result["test_metrics"][metric_name])
        
        # Sort by parameter value
        sorted_indices = np.argsort(param_values)
        param_values = [param_values[i] for i in sorted_indices]
        train_metrics = [train_metrics[i] for i in sorted_indices]
        test_metrics = [test_metrics[i] for i in sorted_indices]
        
        # Plot train metrics
        ax.plot(param_values, train_metrics, 'o-', color=self.colors['primary'], 
               label=f'Train {metric_name.replace("_", " ").title()}')
        
        # Plot test metrics
        ax.plot(param_values, test_metrics, 'o-', color=self.colors['secondary'], 
               label=f'Test {metric_name.replace("_", " ").title()}')
        
        # Format plot
        ax.set_title(title, fontsize=14)
        ax.set_xlabel(param_name.replace('_', ' ').title(), fontsize=12)
        ax.set_ylabel(metric_name.replace('_', ' ').title(), fontsize=12)
        ax.grid(True, alpha=0.3)
        ax.legend()
        
        # Save figure
        self.save_figure(fig, filename)
        
        return fig
