#!/usr/bin/env python3
"""
Strategy Finder for Synergy7 Trading System.

This script implements a comprehensive strategy finder that combines market regime detection,
enhanced mean reversion strategy, and improved risk management to find optimal trading strategies.
"""

import os
import sys
import json
import logging
import argparse
import numpy as np
import pandas as pd
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import seaborn as sns

# Add parent directory to path
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

# Import components
from phase_2_strategy.mean_reversion import MeanReversionStrategy
from phase_2_strategy.market_regime import MarketRegimeDetector, MarketRegime
from phase_2_strategy.risk_management import RiskManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(os.path.join(parent_dir, "logs", "strategy_finder.log")),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("strategy_finder")

class StrategyFinder:
    """
    Strategy Finder.
    
    This class combines market regime detection, enhanced mean reversion strategy,
    and improved risk management to find optimal trading strategies.
    """
    
    def __init__(self, data_path: str, output_dir: str, config_path: str = None):
        """
        Initialize the strategy finder.
        
        Args:
            data_path: Path to historical data
            output_dir: Directory to save results
            config_path: Path to configuration file
        """
        self.data_path = data_path
        self.output_dir = output_dir
        
        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)
        
        # Load configuration
        self.config = self._load_config(config_path)
        
        # Load historical data
        self.data = self._load_data(data_path)
        
        # Initialize components
        self.market_regime_detector = MarketRegimeDetector(**self.config.get("market_regime", {}))
        self.mean_reversion_strategy = MeanReversionStrategy(**self.config.get("mean_reversion", {}))
        self.risk_manager = RiskManager(**self.config.get("risk_management", {}))
        
        # Initialize results
        self.results = {
            "market_regimes": [],
            "strategy_performance": {},
            "portfolio_performance": {},
            "trades": []
        }
        
        logger.info(f"Initialized strategy finder with data from {data_path}")
        logger.info(f"Output directory: {output_dir}")
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """
        Load configuration from file.
        
        Args:
            config_path: Path to configuration file
            
        Returns:
            Configuration dictionary
        """
        # Default configuration
        config = {
            "market_regime": {
                "adx_period": 14,
                "adx_threshold": 25,
                "bb_period": 20,
                "bb_std_dev": 2,
                "volatility_lookback": 50,
                "volatility_threshold": 0.03
            },
            "mean_reversion": {
                "lookback_period": 20,
                "std_dev_multiplier": 2.0,
                "mean_period": 50,
                "use_adaptive_params": True,
                "position_sizing_method": "risk_based",
                "risk_per_trade": 0.01
            },
            "risk_management": {
                "position_sizing_method": "risk_based",
                "max_position_size_pct": 0.05,
                "risk_per_trade_pct": 0.01,
                "stop_loss_pct": 0.05,
                "trailing_stop_pct": 0.02,
                "atr_multiplier": 1.5,
                "circuit_breaker_enabled": True,
                "daily_loss_limit_pct": 0.05,
                "max_consecutive_losses": 3
            },
            "backtest": {
                "initial_capital": 10000,
                "test_size": 0.3,
                "commission_pct": 0.001,
                "slippage_pct": 0.001
            }
        }
        
        # Load configuration from file if provided
        if config_path:
            try:
                with open(config_path, "r") as f:
                    loaded_config = json.load(f)
                
                # Update default configuration with loaded configuration
                for section, params in loaded_config.items():
                    if section in config:
                        config[section].update(params)
                    else:
                        config[section] = params
                
                logger.info(f"Loaded configuration from {config_path}")
            
            except Exception as e:
                logger.error(f"Error loading configuration: {str(e)}")
        
        return config
    
    def _load_data(self, data_path: str) -> pd.DataFrame:
        """
        Load historical data from CSV file.
        
        Args:
            data_path: Path to CSV file
            
        Returns:
            DataFrame with historical data
        """
        try:
            # Load data
            df = pd.read_csv(data_path)
            
            # Convert timestamp to datetime
            if 'timestamp' in df.columns:
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                df.set_index('timestamp', inplace=True)
            
            # Ensure required columns exist
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in required_columns:
                if col not in df.columns:
                    logger.error(f"Required column '{col}' not found in data")
                    raise ValueError(f"Required column '{col}' not found in data")
            
            logger.info(f"Loaded {len(df)} data points from {data_path}")
            return df
        
        except Exception as e:
            logger.error(f"Error loading data: {str(e)}")
            raise
    
    def _split_data(self, test_size: float = 0.3) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        Split data into training and testing sets.
        
        Args:
            test_size: Proportion of data to use for testing
            
        Returns:
            Tuple of (train_data, test_data)
        """
        split_idx = int(len(self.data) * (1 - test_size))
        train_data = self.data.iloc[:split_idx].copy()
        test_data = self.data.iloc[split_idx:].copy()
        
        logger.info(f"Split data into {len(train_data)} training points and {len(test_data)} testing points")
        return train_data, test_data
    
    def detect_market_regimes(self) -> List[Dict[str, Any]]:
        """
        Detect market regimes in the historical data.
        
        Returns:
            List of market regime periods
        """
        logger.info("Detecting market regimes")
        
        # Initialize variables
        regimes = []
        current_regime = None
        regime_start = None
        
        # Analyze each window of data
        window_size = 50  # Use 50 data points for each analysis
        
        for i in range(window_size, len(self.data), 10):  # Step by 10 data points
            # Get data window
            window = self.data.iloc[i-window_size:i]
            
            # Detect regime
            regime, metrics = self.market_regime_detector.detect_regime(window)
            
            # Check if regime has changed
            if regime != current_regime:
                # Record previous regime if it exists
                if current_regime is not None:
                    regimes.append({
                        "regime": current_regime.value,
                        "start": regime_start,
                        "end": window.index[-1],
                        "duration": (window.index[-1] - regime_start).total_seconds() / 86400  # in days
                    })
                
                # Start new regime
                current_regime = regime
                regime_start = window.index[-1]
        
        # Record final regime
        if current_regime is not None:
            regimes.append({
                "regime": current_regime.value,
                "start": regime_start,
                "end": self.data.index[-1],
                "duration": (self.data.index[-1] - regime_start).total_seconds() / 86400  # in days
            })
        
        # Store regimes
        self.results["market_regimes"] = regimes
        
        logger.info(f"Detected {len(regimes)} market regime periods")
        
        return regimes
    
    def backtest_strategy(self, train_data: pd.DataFrame, test_data: pd.DataFrame) -> Dict[str, Any]:
        """
        Backtest the strategy on training and testing data.
        
        Args:
            train_data: Training data
            test_data: Testing data
            
        Returns:
            Dictionary with backtest results
        """
        logger.info("Backtesting strategy")
        
        # Get backtest parameters
        initial_capital = self.config["backtest"]["initial_capital"]
        commission_pct = self.config["backtest"]["commission_pct"]
        slippage_pct = self.config["backtest"]["slippage_pct"]
        
        # Backtest on training data
        logger.info("Backtesting on training data")
        train_results = self.mean_reversion_strategy.backtest(train_data, initial_capital)
        
        # Backtest on testing data
        logger.info("Backtesting on testing data")
        test_results = self.mean_reversion_strategy.backtest(test_data, initial_capital)
        
        # Combine results
        results = {
            "train": train_results,
            "test": test_results,
            "combined": {
                "total_trades": train_results["total_trades"] + test_results["total_trades"],
                "win_rate": (train_results["win_rate"] * train_results["total_trades"] + 
                           test_results["win_rate"] * test_results["total_trades"]) / 
                          (train_results["total_trades"] + test_results["total_trades"]) 
                          if (train_results["total_trades"] + test_results["total_trades"]) > 0 else 0,
                "profit_factor": (train_results["profit_factor"] * train_results["total_trades"] + 
                                test_results["profit_factor"] * test_results["total_trades"]) / 
                               (train_results["total_trades"] + test_results["total_trades"])
                               if (train_results["total_trades"] + test_results["total_trades"]) > 0 else 0,
                "sharpe_ratio": (train_results["sharpe_ratio"] + test_results["sharpe_ratio"]) / 2
            }
        }
        
        # Store results
        self.results["strategy_performance"] = results
        
        # Store trades
        self.results["trades"] = train_results["trades"] + test_results["trades"]
        
        logger.info(f"Backtest complete. Train: {train_results['total_return']:.2%}, Test: {test_results['total_return']:.2%}")
        
        return results
    
    def analyze_results(self) -> Dict[str, Any]:
        """
        Analyze backtest results.
        
        Returns:
            Dictionary with analysis results
        """
        logger.info("Analyzing results")
        
        # Check if we have results
        if not self.results["strategy_performance"]:
            logger.warning("No strategy performance results to analyze")
            return {}
        
        # Get results
        train_results = self.results["strategy_performance"]["train"]
        test_results = self.results["strategy_performance"]["test"]
        trades = self.results["trades"]
        
        # Calculate performance by market regime
        performance_by_regime = {}
        
        for regime_period in self.results["market_regimes"]:
            regime = regime_period["regime"]
            start = pd.to_datetime(regime_period["start"])
            end = pd.to_datetime(regime_period["end"])
            
            # Filter trades by regime period
            regime_trades = [trade for trade in trades 
                           if pd.to_datetime(trade["entry_time"]) >= start 
                           and pd.to_datetime(trade["exit_time"]) <= end]
            
            # Calculate performance metrics
            total_trades = len(regime_trades)
            winning_trades = sum(1 for trade in regime_trades if trade["profit_loss"] > 0)
            win_rate = winning_trades / total_trades if total_trades > 0 else 0
            
            gross_profit = sum(trade["profit_loss"] for trade in regime_trades if trade["profit_loss"] > 0)
            gross_loss = sum(abs(trade["profit_loss"]) for trade in regime_trades if trade["profit_loss"] < 0)
            profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')
            
            performance_by_regime[regime] = {
                "total_trades": total_trades,
                "winning_trades": winning_trades,
                "win_rate": win_rate,
                "profit_factor": profit_factor,
                "total_profit_loss": sum(trade["profit_loss"] for trade in regime_trades)
            }
        
        # Store analysis results
        analysis = {
            "performance_by_regime": performance_by_regime,
            "best_regime": max(performance_by_regime.items(), key=lambda x: x[1]["win_rate"])[0] 
                         if performance_by_regime else None,
            "worst_regime": min(performance_by_regime.items(), key=lambda x: x[1]["win_rate"])[0]
                          if performance_by_regime else None
        }
        
        self.results["analysis"] = analysis
        
        logger.info(f"Analysis complete. Best regime: {analysis['best_regime']}, Worst regime: {analysis['worst_regime']}")
        
        return analysis
    
    def visualize_results(self) -> None:
        """Visualize backtest results."""
        logger.info("Visualizing results")
        
        # Create directory for visualizations
        viz_dir = os.path.join(self.output_dir, "visualizations")
        os.makedirs(viz_dir, exist_ok=True)
        
        # Check if we have results
        if not self.results["strategy_performance"]:
            logger.warning("No strategy performance results to visualize")
            return
        
        # Get results
        train_results = self.results["strategy_performance"]["train"]
        test_results = self.results["strategy_performance"]["test"]
        
        # Plot equity curves
        plt.figure(figsize=(12, 6))
        
        # Plot training equity curve
        plt.plot(train_results["portfolio"]["total"], label="Train")
        
        # Plot testing equity curve
        plt.plot(test_results["portfolio"]["total"], label="Test")
        
        plt.title("Equity Curve")
        plt.xlabel("Date")
        plt.ylabel("Portfolio Value")
        plt.legend()
        plt.grid(True)
        
        # Save plot
        plt.savefig(os.path.join(viz_dir, "equity_curve.png"))
        plt.close()
        
        # Plot drawdowns
        plt.figure(figsize=(12, 6))
        
        # Plot training drawdown
        plt.plot(train_results["portfolio"]["drawdown"], label="Train")
        
        # Plot testing drawdown
        plt.plot(test_results["portfolio"]["drawdown"], label="Test")
        
        plt.title("Drawdown")
        plt.xlabel("Date")
        plt.ylabel("Drawdown")
        plt.legend()
        plt.grid(True)
        
        # Save plot
        plt.savefig(os.path.join(viz_dir, "drawdown.png"))
        plt.close()
        
        # Plot performance by market regime
        if "analysis" in self.results and "performance_by_regime" in self.results["analysis"]:
            performance_by_regime = self.results["analysis"]["performance_by_regime"]
            
            # Create DataFrame
            regimes = []
            win_rates = []
            profit_factors = []
            total_trades = []
            
            for regime, performance in performance_by_regime.items():
                regimes.append(regime)
                win_rates.append(performance["win_rate"])
                profit_factors.append(min(performance["profit_factor"], 5))  # Cap at 5 for visualization
                total_trades.append(performance["total_trades"])
            
            # Plot win rates
            plt.figure(figsize=(10, 6))
            
            plt.bar(regimes, win_rates)
            plt.title("Win Rate by Market Regime")
            plt.xlabel("Market Regime")
            plt.ylabel("Win Rate")
            plt.grid(True)
            
            # Save plot
            plt.savefig(os.path.join(viz_dir, "win_rate_by_regime.png"))
            plt.close()
            
            # Plot profit factors
            plt.figure(figsize=(10, 6))
            
            plt.bar(regimes, profit_factors)
            plt.title("Profit Factor by Market Regime")
            plt.xlabel("Market Regime")
            plt.ylabel("Profit Factor")
            plt.grid(True)
            
            # Save plot
            plt.savefig(os.path.join(viz_dir, "profit_factor_by_regime.png"))
            plt.close()
            
            # Plot total trades
            plt.figure(figsize=(10, 6))
            
            plt.bar(regimes, total_trades)
            plt.title("Total Trades by Market Regime")
            plt.xlabel("Market Regime")
            plt.ylabel("Total Trades")
            plt.grid(True)
            
            # Save plot
            plt.savefig(os.path.join(viz_dir, "total_trades_by_regime.png"))
            plt.close()
        
        logger.info(f"Saved visualizations to {viz_dir}")
    
    def save_results(self) -> None:
        """Save results to file."""
        logger.info("Saving results")
        
        # Save results to JSON file
        results_path = os.path.join(self.output_dir, "results.json")
        
        # Convert datetime objects to strings
        results = json.dumps(self.results, default=str)
        results = json.loads(results)
        
        with open(results_path, "w") as f:
            json.dump(results, f, indent=2)
        
        logger.info(f"Saved results to {results_path}")
    
    def run(self) -> Dict[str, Any]:
        """
        Run the strategy finder.
        
        Returns:
            Dictionary with results
        """
        logger.info("Running strategy finder")
        
        # Detect market regimes
        self.detect_market_regimes()
        
        # Split data
        train_data, test_data = self._split_data(self.config["backtest"]["test_size"])
        
        # Backtest strategy
        self.backtest_strategy(train_data, test_data)
        
        # Analyze results
        self.analyze_results()
        
        # Visualize results
        self.visualize_results()
        
        # Save results
        self.save_results()
        
        logger.info("Strategy finder complete")
        
        return self.results


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Strategy Finder")
    parser.add_argument("--data", type=str, required=True, help="Path to historical data CSV")
    parser.add_argument("--output", type=str, default="output/strategy_finder", help="Output directory")
    parser.add_argument("--config", type=str, help="Path to configuration file")
    
    args = parser.parse_args()
    
    # Create strategy finder
    finder = StrategyFinder(args.data, args.output, args.config)
    
    # Run strategy finder
    results = finder.run()
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
