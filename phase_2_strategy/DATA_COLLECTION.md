# Synergy7 Data Collection Guide

This guide explains how to collect high-quality historical data for SOL/USD using the Synergy7 data collection tools.

## Overview

The data collection process consists of the following steps:

1. **Data Collection**: Collect raw historical data from multiple sources
2. **Data Preparation**: Calculate additional features and split into training and testing sets
3. **Data Validation**: Validate data quality and visualize key metrics

## Data Collection

The data collection tool fetches historical data for SOL/USD from multiple sources:

- **Binance**: High-quality exchange data with good liquidity
- **Birdeye**: Solana-specific data with DEX information
- **CoinGecko**: Aggregated market data with good historical coverage

### Running Data Collection

```bash
python phase_2_strategy/data_collection.py \
  --symbol SOL/USDT \
  --timeframe 1h \
  --start-date 2023-01-01 \
  --end-date 2023-12-31 \
  --output data/raw \
  --sources binance birdeye coingecko
```

Options:
- `--symbol`: Trading symbol (default: "SOL/USDT")
- `--timeframe`: Timeframe (default: "1h")
- `--start-date`: Start date (default: "2023-01-01")
- `--end-date`: End date (default: "2023-12-31")
- `--output`: Output directory (default: "data")
- `--sources`: Data sources (default: ["binance", "birdeye", "coingecko"])
- `--birdeye-api-key`: Birdeye API key (optional)

## Data Preparation

The data preparation tool calculates additional features and splits the data into training and testing sets:

### Running Data Preparation

```bash
python phase_2_strategy/prepare_data.py \
  --data data/raw/SOL_USDT_1h_2023-01-01_2023-12-31.csv \
  --output data/prepared \
  --test-size 0.3
```

Options:
- `--data`: Path to raw data CSV
- `--output`: Output directory (default: "data/prepared")
- `--test-size`: Proportion of data to use for testing (default: 0.3)
- `--no-visualize`: Disable data visualization
- `--collect`: Collect data before preparation
- `--symbol`: Trading symbol (for data collection)
- `--timeframe`: Timeframe (for data collection)
- `--start-date`: Start date (for data collection)
- `--end-date`: End date (for data collection)

## Running the Complete Process

For convenience, a script is provided to run the entire data collection and preparation process:

```bash
python phase_2_strategy/run_data_collection.py \
  --symbol SOL/USDT \
  --timeframe 1h \
  --start-date 2023-01-01 \
  --end-date 2023-12-31 \
  --output data \
  --sources binance birdeye coingecko \
  --test-size 0.3
```

Options:
- `--symbol`: Trading symbol (default: "SOL/USDT")
- `--timeframe`: Timeframe (default: "1h")
- `--start-date`: Start date (default: "2023-01-01")
- `--end-date`: End date (default: "2023-12-31")
- `--output`: Output directory (default: "data")
- `--sources`: Data sources (default: ["binance", "birdeye", "coingecko"])
- `--birdeye-api-key`: Birdeye API key (optional)
- `--test-size`: Proportion of data to use for testing (default: 0.3)
- `--no-visualize`: Disable data visualization
- `--skip-collection`: Skip data collection
- `--skip-preparation`: Skip data preparation
- `--data-path`: Path to raw data CSV (for skipping collection)

## Data Format

The data collection tool saves data in CSV format with the following columns:

- `timestamp`: Timestamp (used as index)
- `open`: Open price
- `high`: High price
- `low`: Low price
- `close`: Close price
- `volume`: Volume

The data preparation tool calculates additional features:

- **Returns**: Percentage price changes
- **Moving Averages**: Simple and exponential moving averages
- **Bollinger Bands**: Price volatility bands
- **RSI**: Relative Strength Index for overbought/oversold conditions
- **MACD**: Moving Average Convergence Divergence for trend detection
- **ATR**: Average True Range for volatility measurement
- **ADX**: Average Directional Index for trend strength
- **Choppiness Index**: For identifying choppy markets

## Data Quality Validation

The data preparation tool validates data quality by checking:

- **Missing Values**: Ensuring no missing values in required columns
- **Negative Prices**: Ensuring all prices are positive
- **High-Low Relationship**: Ensuring high price is greater than low price
- **Open-Close Relationship**: Ensuring open and close prices are within high-low range
- **Large Gaps**: Identifying large gaps in the time series

## Data Visualization

The data preparation tool generates visualizations to help understand the data:

- **Price Chart**: Historical price chart
- **Volume Chart**: Historical volume chart
- **Returns Distribution**: Distribution of returns
- **Bollinger Bands**: Price with Bollinger Bands
- **RSI**: Relative Strength Index
- **MACD**: Moving Average Convergence Divergence
- **ADX**: Average Directional Index
- **Choppiness Index**: Choppiness Index

## Output Files

The data collection and preparation process generates the following files:

- **Raw Data**: `data/raw/SOL_USDT_1h_2023-01-01_2023-12-31.csv`
- **Full Data**: `data/prepared/full_data.csv`
- **Training Data**: `data/prepared/train_data.csv`
- **Testing Data**: `data/prepared/test_data.csv`
- **Visualizations**: `data/prepared/visualizations/`

## Dependencies

- Python 3.7+
- pandas
- numpy
- matplotlib
- seaborn
- ccxt
- requests
- tqdm

## Troubleshooting

### API Rate Limits

If you encounter API rate limit errors, try:
- Reducing the date range
- Using fewer data sources
- Adding delays between requests

### Missing Data

If you encounter missing data, try:
- Using different data sources
- Adjusting the date range
- Checking for market closures or trading halts

### Data Quality Issues

If you encounter data quality issues, try:
- Using different data sources
- Adjusting the validation criteria
- Manually inspecting and cleaning the data
