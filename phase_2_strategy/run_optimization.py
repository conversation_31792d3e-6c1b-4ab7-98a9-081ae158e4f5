#!/usr/bin/env python3
"""
Run Optimization and Validation for Synergy7 Trading System.

This script runs the entire optimization and validation process for the
Mean Reversion and Momentum strategies.
"""

import os
import sys
import json
import logging
import argparse
import subprocess
from datetime import datetime

# Add parent directory to path
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(os.path.join(parent_dir, "logs", "run_optimization.log")),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("run_optimization")

def run_mean_reversion_optimization(args):
    """
    Run Mean Reversion strategy optimization.
    
    Args:
        args: Command-line arguments
    
    Returns:
        Path to best parameters file
    """
    logger.info("Running Mean Reversion strategy optimization")
    
    # Create command
    cmd = [
        "python", os.path.join(parent_dir, "phase_2_strategy", "optimize_mean_reversion.py"),
        "--data", args.train_data,
        "--output", os.path.join(args.output, "mean_reversion"),
        "--test-size", str(args.test_size),
        "--top-n", str(args.top_n)
    ]
    
    # Run command
    logger.info(f"Running command: {' '.join(cmd)}")
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    # Check result
    if result.returncode != 0:
        logger.error(f"Mean Reversion optimization failed: {result.stderr}")
        raise RuntimeError("Mean Reversion optimization failed")
    
    # Get path to best parameters
    best_params_path = os.path.join(args.output, "mean_reversion", "best_params.json")
    
    logger.info(f"Mean Reversion optimization complete. Best parameters saved to {best_params_path}")
    
    return best_params_path

def run_momentum_optimization(args):
    """
    Run Momentum strategy optimization.
    
    Args:
        args: Command-line arguments
    
    Returns:
        Path to best parameters file
    """
    logger.info("Running Momentum strategy optimization")
    
    # Create command
    cmd = [
        "python", os.path.join(parent_dir, "phase_2_strategy", "optimize_momentum.py"),
        "--data", args.train_data,
        "--output", os.path.join(args.output, "momentum"),
        "--test-size", str(args.test_size),
        "--top-n", str(args.top_n),
        "--max-combinations", str(args.max_combinations)
    ]
    
    # Run command
    logger.info(f"Running command: {' '.join(cmd)}")
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    # Check result
    if result.returncode != 0:
        logger.error(f"Momentum optimization failed: {result.stderr}")
        raise RuntimeError("Momentum optimization failed")
    
    # Get path to best parameters
    best_params_path = os.path.join(args.output, "momentum", "best_params.json")
    
    logger.info(f"Momentum optimization complete. Best parameters saved to {best_params_path}")
    
    return best_params_path

def run_strategy_validation(args, mean_reversion_params_path, momentum_params_path):
    """
    Run strategy validation on out-of-sample data.
    
    Args:
        args: Command-line arguments
        mean_reversion_params_path: Path to Mean Reversion parameters
        momentum_params_path: Path to Momentum parameters
    
    Returns:
        Path to validation results directory
    """
    logger.info("Running strategy validation")
    
    # Create command
    cmd = [
        "python", os.path.join(parent_dir, "phase_2_strategy", "validate_strategies.py"),
        "--data", args.validation_data,
        "--mean-reversion-params", mean_reversion_params_path,
        "--momentum-params", momentum_params_path,
        "--output", os.path.join(args.output, "validation"),
        "--initial-capital", str(args.initial_capital)
    ]
    
    # Run command
    logger.info(f"Running command: {' '.join(cmd)}")
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    # Check result
    if result.returncode != 0:
        logger.error(f"Strategy validation failed: {result.stderr}")
        raise RuntimeError("Strategy validation failed")
    
    # Get path to validation results
    validation_results_path = os.path.join(args.output, "validation")
    
    logger.info(f"Strategy validation complete. Results saved to {validation_results_path}")
    
    return validation_results_path

def run_visualization(args, validation_results_path):
    """
    Run visualization of validation results.
    
    Args:
        args: Command-line arguments
        validation_results_path: Path to validation results
    """
    logger.info("Running visualization")
    
    # Create command
    cmd = [
        "python", os.path.join(parent_dir, "phase_2_strategy", "visualize_strategy.py"),
        "--backtest", os.path.join(validation_results_path, "combined_validation_results.json"),
        "--output", os.path.join(args.output, "visualizations")
    ]
    
    # Run command
    logger.info(f"Running command: {' '.join(cmd)}")
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    # Check result
    if result.returncode != 0:
        logger.error(f"Visualization failed: {result.stderr}")
        raise RuntimeError("Visualization failed")
    
    logger.info(f"Visualization complete. Results saved to {os.path.join(args.output, 'visualizations')}")

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Run optimization and validation for trading strategies")
    parser.add_argument("--train-data", type=str, required=True, help="Path to training data CSV")
    parser.add_argument("--validation-data", type=str, required=True, help="Path to validation data CSV")
    parser.add_argument("--output", type=str, default="output", help="Output directory")
    parser.add_argument("--test-size", type=float, default=0.3, help="Proportion of training data to use for testing")
    parser.add_argument("--top-n", type=int, default=10, help="Number of top parameter combinations to return")
    parser.add_argument("--max-combinations", type=int, default=100, help="Maximum number of parameter combinations to test")
    parser.add_argument("--initial-capital", type=float, default=10000.0, help="Initial capital for backtesting")
    parser.add_argument("--skip-mean-reversion", action="store_true", help="Skip Mean Reversion optimization")
    parser.add_argument("--skip-momentum", action="store_true", help="Skip Momentum optimization")
    parser.add_argument("--skip-validation", action="store_true", help="Skip strategy validation")
    parser.add_argument("--skip-visualization", action="store_true", help="Skip visualization")
    parser.add_argument("--mean-reversion-params", type=str, help="Path to Mean Reversion parameters (for skipping optimization)")
    parser.add_argument("--momentum-params", type=str, help="Path to Momentum parameters (for skipping optimization)")
    
    args = parser.parse_args()
    
    # Create logs directory if it doesn't exist
    os.makedirs(os.path.join(parent_dir, "logs"), exist_ok=True)
    
    # Create output directory if it doesn't exist
    os.makedirs(args.output, exist_ok=True)
    
    try:
        # Run Mean Reversion optimization
        if not args.skip_mean_reversion:
            mean_reversion_params_path = run_mean_reversion_optimization(args)
        else:
            if not args.mean_reversion_params:
                logger.error("Mean Reversion parameters path must be provided when skipping optimization")
                return 1
            mean_reversion_params_path = args.mean_reversion_params
        
        # Run Momentum optimization
        if not args.skip_momentum:
            momentum_params_path = run_momentum_optimization(args)
        else:
            if not args.momentum_params:
                logger.error("Momentum parameters path must be provided when skipping optimization")
                return 1
            momentum_params_path = args.momentum_params
        
        # Run strategy validation
        if not args.skip_validation:
            validation_results_path = run_strategy_validation(args, mean_reversion_params_path, momentum_params_path)
        else:
            validation_results_path = os.path.join(args.output, "validation")
        
        # Run visualization
        if not args.skip_visualization:
            run_visualization(args, validation_results_path)
        
        logger.info("Optimization and validation completed successfully")
        
        return 0
    
    except Exception as e:
        logger.error(f"Error: {str(e)}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
