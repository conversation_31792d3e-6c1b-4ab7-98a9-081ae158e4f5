#!/usr/bin/env python3
"""
Data Collection Script for Synergy7 Trading System.

This script collects high-quality historical data for SOL/USD from multiple sources
and saves it in a standardized format for strategy optimization and backtesting.
"""

import os
import sys
import json
import logging
import argparse
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
import pandas as pd
import numpy as np
import requests
import ccxt
from tqdm import tqdm

# Add parent directory to path
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(os.path.join(parent_dir, "logs", "data_collection.log")),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("data_collection")

class DataCollector:
    """
    Data Collector for historical price data.
    
    This class collects historical price data from multiple sources and
    saves it in a standardized format.
    """
    
    def __init__(self, output_dir: str, api_keys: Dict[str, str] = None):
        """
        Initialize the data collector.
        
        Args:
            output_dir: Directory to save collected data
            api_keys: Dictionary of API keys for different sources
        """
        self.output_dir = output_dir
        self.api_keys = api_keys or {}
        
        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)
        
        # Initialize exchange clients
        self.exchanges = {}
        
        logger.info(f"Initialized data collector with output directory: {output_dir}")
    
    def _init_exchange(self, exchange_id: str) -> ccxt.Exchange:
        """
        Initialize exchange client.
        
        Args:
            exchange_id: Exchange ID
            
        Returns:
            Exchange client
        """
        if exchange_id in self.exchanges:
            return self.exchanges[exchange_id]
        
        # Get API keys
        api_key = self.api_keys.get(f"{exchange_id}_api_key")
        api_secret = self.api_keys.get(f"{exchange_id}_api_secret")
        
        # Initialize exchange
        exchange_class = getattr(ccxt, exchange_id)
        exchange = exchange_class({
            'apiKey': api_key,
            'secret': api_secret,
            'timeout': 30000,
            'enableRateLimit': True,
        })
        
        self.exchanges[exchange_id] = exchange
        
        return exchange
    
    def collect_from_ccxt(self, exchange_id: str, symbol: str, timeframe: str,
                         start_date: str, end_date: str) -> pd.DataFrame:
        """
        Collect historical data from CCXT-supported exchange.
        
        Args:
            exchange_id: Exchange ID (e.g., 'binance', 'coinbase')
            symbol: Trading symbol (e.g., 'SOL/USDT')
            timeframe: Timeframe (e.g., '1h', '1d')
            start_date: Start date (YYYY-MM-DD)
            end_date: End date (YYYY-MM-DD)
            
        Returns:
            DataFrame with historical data
        """
        logger.info(f"Collecting data from {exchange_id} for {symbol} ({timeframe}) from {start_date} to {end_date}")
        
        # Initialize exchange
        exchange = self._init_exchange(exchange_id)
        
        # Convert dates to timestamps
        start_timestamp = int(datetime.strptime(start_date, "%Y-%m-%d").timestamp() * 1000)
        end_timestamp = int(datetime.strptime(end_date, "%Y-%m-%d").timestamp() * 1000)
        
        # Initialize result
        all_candles = []
        
        # Fetch data in chunks
        current_timestamp = start_timestamp
        
        with tqdm(total=end_timestamp - start_timestamp) as pbar:
            while current_timestamp < end_timestamp:
                try:
                    # Fetch candles
                    candles = exchange.fetch_ohlcv(
                        symbol=symbol,
                        timeframe=timeframe,
                        since=current_timestamp,
                        limit=1000  # Maximum number of candles per request
                    )
                    
                    if not candles:
                        break
                    
                    # Add candles to result
                    all_candles.extend(candles)
                    
                    # Update current timestamp
                    current_timestamp = candles[-1][0] + 1
                    
                    # Update progress bar
                    pbar.update(current_timestamp - pbar.n)
                    
                    # Rate limiting
                    time.sleep(exchange.rateLimit / 1000)
                
                except Exception as e:
                    logger.error(f"Error fetching data from {exchange_id}: {str(e)}")
                    time.sleep(10)  # Wait before retrying
        
        # Convert to DataFrame
        df = pd.DataFrame(all_candles, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
        
        # Convert timestamp to datetime
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        
        # Set timestamp as index
        df.set_index('timestamp', inplace=True)
        
        # Remove duplicates
        df = df[~df.index.duplicated(keep='first')]
        
        # Sort by timestamp
        df.sort_index(inplace=True)
        
        logger.info(f"Collected {len(df)} data points from {exchange_id}")
        
        return df
    
    def collect_from_birdeye(self, symbol: str, timeframe: str,
                           start_date: str, end_date: str) -> pd.DataFrame:
        """
        Collect historical data from Birdeye API.
        
        Args:
            symbol: Token address or symbol (e.g., 'SOL')
            timeframe: Timeframe (e.g., '1h', '1d')
            start_date: Start date (YYYY-MM-DD)
            end_date: End date (YYYY-MM-DD)
            
        Returns:
            DataFrame with historical data
        """
        logger.info(f"Collecting data from Birdeye for {symbol} ({timeframe}) from {start_date} to {end_date}")
        
        # Get API key
        api_key = self.api_keys.get("birdeye_api_key", "a2679724762a47b58dde41b20fb55ce9")
        
        # Map symbol to token address if needed
        token_address = symbol
        if symbol == "SOL":
            token_address = "So11111111111111111111111111111111111111112"
        
        # Map timeframe to resolution
        resolution_map = {
            "1m": "1",
            "5m": "5",
            "15m": "15",
            "30m": "30",
            "1h": "60",
            "4h": "240",
            "1d": "D",
            "1w": "W"
        }
        resolution = resolution_map.get(timeframe, "60")
        
        # Convert dates to timestamps
        start_timestamp = int(datetime.strptime(start_date, "%Y-%m-%d").timestamp())
        end_timestamp = int(datetime.strptime(end_date, "%Y-%m-%d").timestamp())
        
        # Prepare request
        url = "https://public-api.birdeye.so/defi/history_price"
        headers = {
            "X-API-KEY": api_key,
            "Content-Type": "application/json"
        }
        params = {
            "address": token_address,
            "type": "sol",
            "resolution": resolution,
            "from": start_timestamp,
            "to": end_timestamp
        }
        
        try:
            # Make request
            response = requests.get(url, headers=headers, params=params)
            response.raise_for_status()
            
            # Parse response
            data = response.json()
            
            if "data" not in data or "items" not in data["data"]:
                logger.error(f"Invalid response from Birdeye: {data}")
                return pd.DataFrame()
            
            items = data["data"]["items"]
            
            # Convert to DataFrame
            df = pd.DataFrame(items)
            
            # Rename columns
            df.rename(columns={
                "time": "timestamp",
                "value": "close",
                "high": "high",
                "low": "low",
                "open": "open",
                "volume": "volume"
            }, inplace=True)
            
            # Convert timestamp to datetime
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s')
            
            # Set timestamp as index
            df.set_index('timestamp', inplace=True)
            
            # Remove duplicates
            df = df[~df.index.duplicated(keep='first')]
            
            # Sort by timestamp
            df.sort_index(inplace=True)
            
            logger.info(f"Collected {len(df)} data points from Birdeye")
            
            return df
        
        except Exception as e:
            logger.error(f"Error fetching data from Birdeye: {str(e)}")
            return pd.DataFrame()
    
    def collect_from_coingecko(self, symbol: str, vs_currency: str,
                             start_date: str, end_date: str) -> pd.DataFrame:
        """
        Collect historical data from CoinGecko API.
        
        Args:
            symbol: Coin ID (e.g., 'solana')
            vs_currency: Quote currency (e.g., 'usd')
            start_date: Start date (YYYY-MM-DD)
            end_date: End date (YYYY-MM-DD)
            
        Returns:
            DataFrame with historical data
        """
        logger.info(f"Collecting data from CoinGecko for {symbol}/{vs_currency} from {start_date} to {end_date}")
        
        # Map symbol to coin ID if needed
        coin_id = symbol.lower()
        if coin_id == "sol":
            coin_id = "solana"
        
        # Convert dates to timestamps
        start_timestamp = int(datetime.strptime(start_date, "%Y-%m-%d").timestamp())
        end_timestamp = int(datetime.strptime(end_date, "%Y-%m-%d").timestamp())
        
        # Prepare request
        url = f"https://api.coingecko.com/api/v3/coins/{coin_id}/market_chart/range"
        params = {
            "vs_currency": vs_currency,
            "from": start_timestamp,
            "to": end_timestamp
        }
        
        try:
            # Make request
            response = requests.get(url, params=params)
            response.raise_for_status()
            
            # Parse response
            data = response.json()
            
            if "prices" not in data or "total_volumes" not in data:
                logger.error(f"Invalid response from CoinGecko: {data}")
                return pd.DataFrame()
            
            # Extract prices and volumes
            prices = data["prices"]
            volumes = data["total_volumes"]
            
            # Create DataFrame
            df_prices = pd.DataFrame(prices, columns=['timestamp', 'close'])
            df_volumes = pd.DataFrame(volumes, columns=['timestamp', 'volume'])
            
            # Convert timestamp to datetime
            df_prices['timestamp'] = pd.to_datetime(df_prices['timestamp'], unit='ms')
            df_volumes['timestamp'] = pd.to_datetime(df_volumes['timestamp'], unit='ms')
            
            # Set timestamp as index
            df_prices.set_index('timestamp', inplace=True)
            df_volumes.set_index('timestamp', inplace=True)
            
            # Merge DataFrames
            df = pd.merge(df_prices, df_volumes, left_index=True, right_index=True)
            
            # Add missing columns
            df['open'] = df['close'].shift(1)
            df['high'] = df['close']
            df['low'] = df['close']
            
            # Remove rows with NaN values
            df.dropna(inplace=True)
            
            # Remove duplicates
            df = df[~df.index.duplicated(keep='first')]
            
            # Sort by timestamp
            df.sort_index(inplace=True)
            
            logger.info(f"Collected {len(df)} data points from CoinGecko")
            
            return df
        
        except Exception as e:
            logger.error(f"Error fetching data from CoinGecko: {str(e)}")
            return pd.DataFrame()
    
    def merge_data(self, dataframes: List[pd.DataFrame]) -> pd.DataFrame:
        """
        Merge data from multiple sources.
        
        Args:
            dataframes: List of DataFrames to merge
            
        Returns:
            Merged DataFrame
        """
        logger.info(f"Merging {len(dataframes)} DataFrames")
        
        # Filter out empty DataFrames
        dataframes = [df for df in dataframes if not df.empty]
        
        if not dataframes:
            logger.warning("No data to merge")
            return pd.DataFrame()
        
        # Start with the first DataFrame
        merged_df = dataframes[0].copy()
        
        # Merge with other DataFrames
        for df in dataframes[1:]:
            # Identify common timestamps
            common_timestamps = merged_df.index.intersection(df.index)
            
            # For common timestamps, use average values
            for col in ['open', 'high', 'low', 'close', 'volume']:
                if col in merged_df.columns and col in df.columns:
                    merged_df.loc[common_timestamps, col] = (
                        merged_df.loc[common_timestamps, col] + df.loc[common_timestamps, col]
                    ) / 2
            
            # Add timestamps that are in df but not in merged_df
            new_timestamps = df.index.difference(merged_df.index)
            if not new_timestamps.empty:
                merged_df = pd.concat([merged_df, df.loc[new_timestamps]])
        
        # Sort by timestamp
        merged_df.sort_index(inplace=True)
        
        # Ensure all required columns exist
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        for col in required_columns:
            if col not in merged_df.columns:
                logger.warning(f"Column '{col}' not found in merged data")
                merged_df[col] = np.nan
        
        # Forward fill missing values
        merged_df.fillna(method='ffill', inplace=True)
        
        logger.info(f"Merged DataFrame has {len(merged_df)} data points")
        
        return merged_df
    
    def resample_data(self, df: pd.DataFrame, timeframe: str) -> pd.DataFrame:
        """
        Resample data to the specified timeframe.
        
        Args:
            df: DataFrame to resample
            timeframe: Target timeframe (e.g., '1h', '1d')
            
        Returns:
            Resampled DataFrame
        """
        logger.info(f"Resampling data to {timeframe}")
        
        # Map timeframe to pandas frequency
        freq_map = {
            "1m": "1min",
            "5m": "5min",
            "15m": "15min",
            "30m": "30min",
            "1h": "1H",
            "4h": "4H",
            "1d": "1D",
            "1w": "1W"
        }
        freq = freq_map.get(timeframe, "1H")
        
        # Resample data
        resampled = df.resample(freq).agg({
            'open': 'first',
            'high': 'max',
            'low': 'min',
            'close': 'last',
            'volume': 'sum'
        })
        
        # Remove rows with NaN values
        resampled.dropna(inplace=True)
        
        logger.info(f"Resampled DataFrame has {len(resampled)} data points")
        
        return resampled
    
    def validate_data(self, df: pd.DataFrame) -> Tuple[bool, str]:
        """
        Validate data quality.
        
        Args:
            df: DataFrame to validate
            
        Returns:
            Tuple of (is_valid, message)
        """
        logger.info("Validating data quality")
        
        # Check if DataFrame is empty
        if df.empty:
            return False, "DataFrame is empty"
        
        # Check for required columns
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        for col in required_columns:
            if col not in df.columns:
                return False, f"Column '{col}' not found"
        
        # Check for missing values
        if df.isnull().any().any():
            return False, "DataFrame contains missing values"
        
        # Check for negative prices
        for col in ['open', 'high', 'low', 'close']:
            if (df[col] <= 0).any():
                return False, f"DataFrame contains non-positive {col} prices"
        
        # Check for high-low relationship
        if (df['high'] < df['low']).any():
            return False, "High price is less than low price"
        
        # Check for open-close relationship with high-low
        if ((df['open'] > df['high']) | (df['open'] < df['low']) |
            (df['close'] > df['high']) | (df['close'] < df['low'])).any():
            return False, "Open or close price is outside high-low range"
        
        # Check for large gaps
        timestamps = df.index.to_series()
        gaps = timestamps.diff().dt.total_seconds()
        expected_gap = pd.Timedelta(df.index.freq).total_seconds()
        
        if (gaps > expected_gap * 5).any():
            return False, "DataFrame contains large gaps"
        
        logger.info("Data validation passed")
        
        return True, "Data validation passed"
    
    def collect_data(self, symbol: str, timeframe: str, start_date: str, end_date: str,
                    sources: List[str] = None) -> pd.DataFrame:
        """
        Collect data from multiple sources and merge.
        
        Args:
            symbol: Trading symbol (e.g., 'SOL/USDT', 'SOL')
            timeframe: Timeframe (e.g., '1h', '1d')
            start_date: Start date (YYYY-MM-DD)
            end_date: End date (YYYY-MM-DD)
            sources: List of sources to collect from (default: all available)
            
        Returns:
            DataFrame with merged data
        """
        logger.info(f"Collecting data for {symbol} ({timeframe}) from {start_date} to {end_date}")
        
        # Default sources
        if sources is None:
            sources = ['binance', 'birdeye', 'coingecko']
        
        # Initialize result
        dataframes = []
        
        # Collect from each source
        for source in sources:
            try:
                if source == 'binance':
                    df = self.collect_from_ccxt('binance', 'SOL/USDT', timeframe, start_date, end_date)
                elif source == 'birdeye':
                    df = self.collect_from_birdeye('SOL', timeframe, start_date, end_date)
                elif source == 'coingecko':
                    df = self.collect_from_coingecko('solana', 'usd', start_date, end_date)
                    # Resample CoinGecko data to match timeframe
                    if not df.empty:
                        df = self.resample_data(df, timeframe)
                else:
                    logger.warning(f"Unknown source: {source}")
                    continue
                
                if not df.empty:
                    dataframes.append(df)
            
            except Exception as e:
                logger.error(f"Error collecting data from {source}: {str(e)}")
        
        # Merge data
        merged_df = self.merge_data(dataframes)
        
        # Validate data
        is_valid, message = self.validate_data(merged_df)
        if not is_valid:
            logger.warning(f"Data validation failed: {message}")
        
        return merged_df
    
    def save_data(self, df: pd.DataFrame, symbol: str, timeframe: str,
                 start_date: str, end_date: str) -> str:
        """
        Save data to CSV file.
        
        Args:
            df: DataFrame to save
            symbol: Trading symbol
            timeframe: Timeframe
            start_date: Start date
            end_date: End date
            
        Returns:
            Path to saved file
        """
        # Create filename
        filename = f"{symbol.replace('/', '_')}_{timeframe}_{start_date}_{end_date}.csv"
        filepath = os.path.join(self.output_dir, filename)
        
        # Save to CSV
        df.to_csv(filepath)
        
        logger.info(f"Saved data to {filepath}")
        
        return filepath


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Collect historical data for trading")
    parser.add_argument("--symbol", type=str, default="SOL/USDT", help="Trading symbol")
    parser.add_argument("--timeframe", type=str, default="1h", help="Timeframe")
    parser.add_argument("--start-date", type=str, default="2023-01-01", help="Start date (YYYY-MM-DD)")
    parser.add_argument("--end-date", type=str, default="2023-12-31", help="End date (YYYY-MM-DD)")
    parser.add_argument("--output", type=str, default="data", help="Output directory")
    parser.add_argument("--sources", type=str, nargs="+", default=["binance", "birdeye", "coingecko"], help="Data sources")
    parser.add_argument("--birdeye-api-key", type=str, help="Birdeye API key")
    
    args = parser.parse_args()
    
    # Create logs directory if it doesn't exist
    os.makedirs(os.path.join(parent_dir, "logs"), exist_ok=True)
    
    # Create API keys dictionary
    api_keys = {}
    if args.birdeye_api_key:
        api_keys["birdeye_api_key"] = args.birdeye_api_key
    
    # Create data collector
    collector = DataCollector(args.output, api_keys)
    
    # Collect data
    df = collector.collect_data(
        symbol=args.symbol,
        timeframe=args.timeframe,
        start_date=args.start_date,
        end_date=args.end_date,
        sources=args.sources
    )
    
    # Save data
    if not df.empty:
        filepath = collector.save_data(
            df=df,
            symbol=args.symbol,
            timeframe=args.timeframe,
            start_date=args.start_date,
            end_date=args.end_date
        )
        print(f"Data saved to {filepath}")
    else:
        print("No data collected")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
