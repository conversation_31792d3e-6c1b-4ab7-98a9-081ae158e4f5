#!/usr/bin/env python3
"""
Generate Sample Data for Visualization Testing.

This script generates sample backtest results, optimization results, and market regime
results for testing the visualization module.
"""

import os
import sys
import json
import logging
import argparse
import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta

# Add parent directory to path
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(os.path.join(parent_dir, "logs", "generate_sample_data.log")),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("generate_sample_data")

def generate_sample_backtest_results(output_path: str, start_date: str = "2023-01-01",
                                   end_date: str = "2023-12-31", initial_capital: float = 10000.0,
                                   symbol: str = "SOL/USD") -> Dict[str, Any]:
    """
    Generate sample backtest results.

    Args:
        output_path: Path to save results
        start_date: Start date
        end_date: End date
        initial_capital: Initial capital
        symbol: Trading symbol

    Returns:
        Dictionary with backtest results
    """
    logger.info(f"Generating sample backtest results from {start_date} to {end_date}")

    # Convert dates to datetime
    start_dt = pd.to_datetime(start_date)
    end_dt = pd.to_datetime(end_date)

    # Generate date range
    date_range = pd.date_range(start=start_dt, end=end_dt, freq='D')

    # Generate random returns
    np.random.seed(42)  # For reproducibility
    daily_returns = np.random.normal(0.001, 0.02, len(date_range))

    # Create portfolio DataFrame
    portfolio = pd.DataFrame(index=date_range)
    portfolio['returns'] = daily_returns

    # Calculate equity
    portfolio['equity'] = initial_capital * (1 + portfolio['returns']).cumprod()

    # Calculate drawdown
    portfolio['drawdown'] = portfolio['equity'] / portfolio['equity'].cummax() - 1

    # Generate trades
    trades = []

    # Generate about 50 trades
    num_trades = 50
    trade_dates = np.sort(np.random.choice(date_range[:-1], num_trades, replace=False))

    for i, entry_date in enumerate(trade_dates):
        # Generate random trade duration (1-10 days)
        duration = np.random.randint(1, 10)
        exit_date = entry_date + pd.Timedelta(days=duration)

        # Ensure exit date is within date range
        if exit_date > date_range[-1]:
            exit_date = date_range[-1]

        # Generate random entry and exit prices
        entry_price = np.random.uniform(20, 30)
        price_change = np.random.normal(0, 0.05)
        exit_price = entry_price * (1 + price_change)

        # Generate random position size
        position_size = np.random.uniform(0.1, 1.0)

        # Calculate profit/loss
        profit_loss = (exit_price - entry_price) * position_size
        profit_loss_pct = (exit_price - entry_price) / entry_price

        # Determine direction
        direction = "long" if np.random.random() > 0.3 else "short"

        # Adjust profit/loss for short positions
        if direction == "short":
            profit_loss = -profit_loss
            profit_loss_pct = -profit_loss_pct

        # Create trade
        trade = {
            "entry_date": pd.Timestamp(entry_date).strftime("%Y-%m-%d"),
            "exit_date": pd.Timestamp(exit_date).strftime("%Y-%m-%d"),
            "entry_price": entry_price,
            "exit_price": exit_price,
            "position": direction,
            "size": position_size,
            "profit_loss": profit_loss,
            "profit_loss_pct": profit_loss_pct
        }

        trades.append(trade)

    # Calculate performance metrics
    total_return = (portfolio['equity'].iloc[-1] / initial_capital) - 1
    sharpe_ratio = portfolio['returns'].mean() / portfolio['returns'].std() * np.sqrt(252)
    max_drawdown = portfolio['drawdown'].min()

    winning_trades = sum(1 for trade in trades if trade['profit_loss'] > 0)
    total_trades = len(trades)
    win_rate = winning_trades / total_trades if total_trades > 0 else 0

    gross_profit = sum(trade['profit_loss'] for trade in trades if trade['profit_loss'] > 0)
    gross_loss = sum(abs(trade['profit_loss']) for trade in trades if trade['profit_loss'] < 0)
    profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')

    # Convert portfolio index to string for JSON serialization
    portfolio_dict = {}
    for col in portfolio.columns:
        portfolio_dict[col] = {}
        for idx, val in zip(portfolio.index.strftime('%Y-%m-%d'), portfolio[col].values):
            portfolio_dict[col][idx] = val

    # Create backtest results
    results = {
        "strategy": "sample_strategy",
        "version": "1.0.0",
        "symbol": symbol,
        "initial_capital": initial_capital,
        "final_capital": portfolio['equity'].iloc[-1],
        "total_return": total_return,
        "sharpe_ratio": sharpe_ratio,
        "max_drawdown": max_drawdown,
        "win_rate": win_rate,
        "profit_factor": profit_factor,
        "total_trades": total_trades,
        "trades": trades,
        "portfolio": portfolio_dict
    }

    # Save results
    with open(output_path, 'w') as f:
        json.dump(results, f, indent=2, default=str)

    logger.info(f"Saved sample backtest results to {output_path}")

    return results

def generate_sample_optimization_results(output_path: str, num_combinations: int = 100) -> List[Dict[str, Any]]:
    """
    Generate sample optimization results.

    Args:
        output_path: Path to save results
        num_combinations: Number of parameter combinations

    Returns:
        List of optimization results
    """
    logger.info(f"Generating sample optimization results with {num_combinations} combinations")

    # Define parameter ranges
    param_ranges = {
        "lookback_period": [10, 15, 20, 25, 30],
        "std_dev_multiplier": [1.5, 2.0, 2.5, 3.0],
        "mean_period": [30, 50, 80, 100],
        "use_adaptive_params": [True, False],
        "position_sizing_method": ["risk_based", "percentage", "fixed"],
        "risk_per_trade": [0.005, 0.01, 0.02]
    }

    # Generate random parameter combinations
    np.random.seed(42)  # For reproducibility
    results = []

    for i in range(num_combinations):
        # Generate random parameters
        params = {}
        for param, values in param_ranges.items():
            params[param] = np.random.choice(values)

        # Generate random metrics
        train_sharpe = np.random.uniform(0.5, 2.5)
        test_sharpe = train_sharpe * np.random.uniform(0.7, 1.1)  # Test sharpe is usually worse

        train_return = np.random.uniform(0.1, 0.5)
        test_return = train_return * np.random.uniform(0.7, 1.1)

        train_drawdown = -np.random.uniform(0.05, 0.2)
        test_drawdown = train_drawdown * np.random.uniform(0.9, 1.3)  # Test drawdown is usually worse

        train_win_rate = np.random.uniform(0.4, 0.7)
        test_win_rate = train_win_rate * np.random.uniform(0.9, 1.1)

        train_profit_factor = np.random.uniform(1.2, 2.5)
        test_profit_factor = train_profit_factor * np.random.uniform(0.8, 1.1)

        # Create result
        result = {
            "params": params,
            "train_metrics": {
                "sharpe_ratio": train_sharpe,
                "total_return": train_return,
                "max_drawdown": train_drawdown,
                "win_rate": train_win_rate,
                "profit_factor": train_profit_factor,
                "total_trades": np.random.randint(50, 200)
            },
            "test_metrics": {
                "sharpe_ratio": test_sharpe,
                "total_return": test_return,
                "max_drawdown": test_drawdown,
                "win_rate": test_win_rate,
                "profit_factor": test_profit_factor,
                "total_trades": np.random.randint(20, 100)
            },
            "combined_score": (
                test_sharpe * 0.3 +
                test_return * 0.3 +
                (1 + test_drawdown) * 0.1 +
                test_win_rate * 0.2 +
                min(test_profit_factor, 5) / 5 * 0.1
            )
        }

        results.append(result)

    # Sort by combined score
    results.sort(key=lambda x: x["combined_score"], reverse=True)

    # Save results
    with open(output_path, 'w') as f:
        json.dump(results, f, indent=2, default=str)

    logger.info(f"Saved sample optimization results to {output_path}")

    return results

def generate_sample_market_regime_results(output_path: str, start_date: str = "2023-01-01",
                                        end_date: str = "2023-12-31") -> Dict[str, Any]:
    """
    Generate sample market regime results.

    Args:
        output_path: Path to save results
        start_date: Start date
        end_date: End date

    Returns:
        Dictionary with market regime results
    """
    logger.info(f"Generating sample market regime results from {start_date} to {end_date}")

    # Convert dates to datetime
    start_dt = pd.to_datetime(start_date)
    end_dt = pd.to_datetime(end_date)

    # Define regime types
    regime_types = ["trending_up", "trending_down", "ranging", "volatile", "choppy"]

    # Generate random regime periods
    np.random.seed(42)  # For reproducibility

    regimes = []
    transitions = []

    current_date = start_dt
    current_regime = np.random.choice(regime_types)
    regime_start = current_date

    while current_date < end_dt:
        # Random regime duration (7-30 days)
        duration = np.random.randint(7, 30)
        regime_end = current_date + pd.Timedelta(days=duration)

        # Ensure end date is within range
        if regime_end > end_dt:
            regime_end = end_dt

        # Record regime period
        regimes.append({
            "regime": current_regime,
            "start": pd.Timestamp(regime_start).strftime("%Y-%m-%d"),
            "end": pd.Timestamp(regime_end).strftime("%Y-%m-%d"),
            "duration": (regime_end - regime_start).days
        })

        # Move to next period
        current_date = regime_end
        regime_start = current_date

        # Choose next regime (different from current)
        next_regime = np.random.choice([r for r in regime_types if r != current_regime])

        # Record transition
        if current_date < end_dt:
            transitions.append({
                "from": current_regime,
                "to": next_regime,
                "time": pd.Timestamp(current_date).strftime("%Y-%m-%d")
            })

        current_regime = next_regime

    # Calculate summary statistics
    total_days = (end_dt - start_dt).days

    regime_summary = {}
    for regime_type in regime_types:
        regime_periods = [r for r in regimes if r["regime"] == regime_type]
        total_duration = sum(r["duration"] for r in regime_periods)

        regime_summary[regime_type] = {
            "count": len(regime_periods),
            "total_duration": total_duration,
            "avg_duration": total_duration / len(regime_periods) if regime_periods else 0,
            "pct_time": total_duration / total_days * 100 if total_days > 0 else 0
        }

    # Create results
    results = {
        "regimes": regimes,
        "transitions": transitions,
        "summary": regime_summary
    }

    # Save results
    with open(output_path, 'w') as f:
        json.dump(results, f, indent=2, default=str)

    logger.info(f"Saved sample market regime results to {output_path}")

    return results

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Generate sample data for visualization testing")
    parser.add_argument("--output", type=str, default="sample_data", help="Output directory")
    parser.add_argument("--start-date", type=str, default="2023-01-01", help="Start date (YYYY-MM-DD)")
    parser.add_argument("--end-date", type=str, default="2023-12-31", help="End date (YYYY-MM-DD)")
    parser.add_argument("--initial-capital", type=float, default=10000.0, help="Initial capital")
    parser.add_argument("--symbol", type=str, default="SOL/USD", help="Trading symbol")
    parser.add_argument("--num-combinations", type=int, default=100, help="Number of parameter combinations")

    args = parser.parse_args()

    # Create logs directory if it doesn't exist
    os.makedirs(os.path.join(parent_dir, "logs"), exist_ok=True)

    # Create output directory if it doesn't exist
    os.makedirs(args.output, exist_ok=True)

    try:
        # Generate sample backtest results
        backtest_path = os.path.join(args.output, "sample_backtest_results.json")
        generate_sample_backtest_results(
            backtest_path,
            start_date=args.start_date,
            end_date=args.end_date,
            initial_capital=args.initial_capital,
            symbol=args.symbol
        )

        # Generate sample optimization results
        optimization_path = os.path.join(args.output, "sample_optimization_results.json")
        generate_sample_optimization_results(
            optimization_path,
            num_combinations=args.num_combinations
        )

        # Generate sample market regime results
        regime_path = os.path.join(args.output, "sample_market_regime_results.json")
        generate_sample_market_regime_results(
            regime_path,
            start_date=args.start_date,
            end_date=args.end_date
        )

        logger.info("Sample data generation complete")

        return 0

    except Exception as e:
        logger.error(f"Error: {str(e)}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
