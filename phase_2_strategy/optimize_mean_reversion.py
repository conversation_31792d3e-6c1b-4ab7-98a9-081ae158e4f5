#!/usr/bin/env python3
"""
Mean Reversion Strategy Optimizer for Synergy7 Trading System.

This script optimizes the parameters of the Mean Reversion strategy using
grid search and cross-validation to find the best parameter combination.
"""

import os
import sys
import json
import logging
import numpy as np
import pandas as pd
from typing import Dict, Any, List, Tuple
from datetime import datetime
from itertools import product
import matplotlib.pyplot as plt
import seaborn as sns

# Add parent directory to path
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

# Import the Mean Reversion strategy
from phase_2_strategy.mean_reversion import MeanReversionStrategy

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(os.path.join(parent_dir, "logs", "optimize_mean_reversion.log")),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("optimize_mean_reversion")

class MeanReversionOptimizer:
    """Optimizer for Mean Reversion strategy."""
    
    def __init__(self, data_path: str, output_dir: str):
        """
        Initialize the optimizer.
        
        Args:
            data_path: Path to historical data
            output_dir: Directory to save optimization results
        """
        self.data_path = data_path
        self.output_dir = output_dir
        
        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)
        
        # Load historical data
        self.data = self._load_data(data_path)
        
        # Initialize parameter grid
        self.param_grid = {
            "lookback_period": [10, 15, 20, 25, 30],
            "std_dev_multiplier": [1.5, 2.0, 2.5, 3.0],
            "mean_period": [30, 50, 80, 100],
            "use_adaptive_params": [True, False],
            "position_sizing_method": ["risk_based", "percentage", "fixed"],
            "risk_per_trade": [0.005, 0.01, 0.02]
        }
        
        # Initialize results
        self.results = []
        
        logger.info(f"Initialized optimizer with data from {data_path}")
        logger.info(f"Output directory: {output_dir}")
    
    def _load_data(self, data_path: str) -> pd.DataFrame:
        """
        Load historical data from CSV file.
        
        Args:
            data_path: Path to CSV file
            
        Returns:
            DataFrame with historical data
        """
        try:
            # Load data
            df = pd.read_csv(data_path)
            
            # Convert timestamp to datetime
            if 'timestamp' in df.columns:
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                df.set_index('timestamp', inplace=True)
            
            # Ensure required columns exist
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in required_columns:
                if col not in df.columns:
                    logger.error(f"Required column '{col}' not found in data")
                    raise ValueError(f"Required column '{col}' not found in data")
            
            logger.info(f"Loaded {len(df)} data points from {data_path}")
            return df
        
        except Exception as e:
            logger.error(f"Error loading data: {str(e)}")
            raise
    
    def _split_data(self, test_size: float = 0.3) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        Split data into training and testing sets.
        
        Args:
            test_size: Proportion of data to use for testing
            
        Returns:
            Tuple of (train_data, test_data)
        """
        split_idx = int(len(self.data) * (1 - test_size))
        train_data = self.data.iloc[:split_idx].copy()
        test_data = self.data.iloc[split_idx:].copy()
        
        logger.info(f"Split data into {len(train_data)} training points and {len(test_data)} testing points")
        return train_data, test_data
    
    def optimize(self, test_size: float = 0.3, top_n: int = 10) -> List[Dict[str, Any]]:
        """
        Optimize strategy parameters using grid search.
        
        Args:
            test_size: Proportion of data to use for testing
            top_n: Number of top parameter combinations to return
            
        Returns:
            List of top parameter combinations with performance metrics
        """
        logger.info("Starting parameter optimization")
        
        # Split data
        train_data, test_data = self._split_data(test_size)
        
        # Generate parameter combinations
        param_combinations = list(product(
            self.param_grid["lookback_period"],
            self.param_grid["std_dev_multiplier"],
            self.param_grid["mean_period"],
            self.param_grid["use_adaptive_params"],
            self.param_grid["position_sizing_method"],
            self.param_grid["risk_per_trade"]
        ))
        
        logger.info(f"Testing {len(param_combinations)} parameter combinations")
        
        # Test each parameter combination
        for i, params in enumerate(param_combinations):
            lookback_period, std_dev_multiplier, mean_period, use_adaptive_params, position_sizing_method, risk_per_trade = params
            
            # Create strategy with parameters
            strategy = MeanReversionStrategy(
                lookback_period=lookback_period,
                std_dev_multiplier=std_dev_multiplier,
                mean_period=mean_period,
                use_adaptive_params=use_adaptive_params,
                position_sizing_method=position_sizing_method,
                risk_per_trade=risk_per_trade
            )
            
            # Backtest on training data
            train_results = strategy.backtest(train_data)
            
            # Backtest on testing data
            test_results = strategy.backtest(test_data)
            
            # Store results
            result = {
                "params": {
                    "lookback_period": lookback_period,
                    "std_dev_multiplier": std_dev_multiplier,
                    "mean_period": mean_period,
                    "use_adaptive_params": use_adaptive_params,
                    "position_sizing_method": position_sizing_method,
                    "risk_per_trade": risk_per_trade
                },
                "train_metrics": {
                    "sharpe_ratio": train_results["sharpe_ratio"],
                    "total_return": train_results["total_return"],
                    "max_drawdown": train_results["max_drawdown"],
                    "win_rate": train_results["win_rate"],
                    "profit_factor": train_results["profit_factor"],
                    "total_trades": train_results["total_trades"]
                },
                "test_metrics": {
                    "sharpe_ratio": test_results["sharpe_ratio"],
                    "total_return": test_results["total_return"],
                    "max_drawdown": test_results["max_drawdown"],
                    "win_rate": test_results["win_rate"],
                    "profit_factor": test_results["profit_factor"],
                    "total_trades": test_results["total_trades"]
                },
                "combined_score": (
                    test_results["sharpe_ratio"] * 0.3 +
                    test_results["total_return"] * 0.3 +
                    (1 + test_results["max_drawdown"]) * 0.1 +  # Convert drawdown to positive score
                    test_results["win_rate"] * 0.2 +
                    min(test_results["profit_factor"], 5) / 5 * 0.1  # Cap profit factor at 5
                )
            }
            
            self.results.append(result)
            
            # Log progress
            if (i + 1) % 10 == 0 or (i + 1) == len(param_combinations):
                logger.info(f"Tested {i + 1}/{len(param_combinations)} parameter combinations")
        
        # Sort results by combined score
        self.results.sort(key=lambda x: x["combined_score"], reverse=True)
        
        # Save all results
        with open(os.path.join(self.output_dir, "all_results.json"), "w") as f:
            json.dump(self.results, f, indent=2)
        
        # Return top N results
        top_results = self.results[:top_n]
        
        # Save top results
        with open(os.path.join(self.output_dir, "top_results.json"), "w") as f:
            json.dump(top_results, f, indent=2)
        
        logger.info(f"Optimization complete. Top combined score: {top_results[0]['combined_score']:.4f}")
        
        return top_results
    
    def visualize_results(self, top_n: int = 10) -> None:
        """
        Visualize optimization results.
        
        Args:
            top_n: Number of top results to visualize
        """
        if not self.results:
            logger.warning("No results to visualize")
            return
        
        # Get top N results
        top_results = self.results[:top_n]
        
        # Create directory for visualizations
        viz_dir = os.path.join(self.output_dir, "visualizations")
        os.makedirs(viz_dir, exist_ok=True)
        
        # Plot combined score vs. parameters
        for param in ["lookback_period", "std_dev_multiplier", "mean_period"]:
            plt.figure(figsize=(10, 6))
            
            # Extract parameter values and scores
            param_values = [result["params"][param] for result in self.results]
            scores = [result["combined_score"] for result in self.results]
            
            # Create scatter plot
            plt.scatter(param_values, scores, alpha=0.5)
            plt.xlabel(param)
            plt.ylabel("Combined Score")
            plt.title(f"Combined Score vs. {param}")
            plt.grid(True)
            
            # Save plot
            plt.savefig(os.path.join(viz_dir, f"score_vs_{param}.png"))
            plt.close()
        
        # Plot top N results comparison
        metrics = ["sharpe_ratio", "total_return", "max_drawdown", "win_rate", "profit_factor"]
        
        for metric in metrics:
            plt.figure(figsize=(12, 6))
            
            # Extract metric values
            train_values = [result["train_metrics"][metric] for result in top_results]
            test_values = [result["test_metrics"][metric] for result in top_results]
            labels = [f"Combo {i+1}" for i in range(len(top_results))]
            
            # Create bar chart
            x = np.arange(len(labels))
            width = 0.35
            
            plt.bar(x - width/2, train_values, width, label="Train")
            plt.bar(x + width/2, test_values, width, label="Test")
            
            plt.xlabel("Parameter Combination")
            plt.ylabel(metric)
            plt.title(f"{metric} Comparison for Top {top_n} Parameter Combinations")
            plt.xticks(x, labels)
            plt.legend()
            plt.grid(True)
            
            # Save plot
            plt.savefig(os.path.join(viz_dir, f"top_{top_n}_{metric}.png"))
            plt.close()
        
        logger.info(f"Saved visualizations to {viz_dir}")
    
    def run_best_strategy(self) -> Dict[str, Any]:
        """
        Run the best strategy on the entire dataset.
        
        Returns:
            Dict[str, Any]: Backtest results
        """
        if not self.results:
            logger.warning("No results available")
            return {}
        
        # Get best parameters
        best_params = self.results[0]["params"]
        
        logger.info(f"Running best strategy with parameters: {best_params}")
        
        # Create strategy with best parameters
        strategy = MeanReversionStrategy(**best_params)
        
        # Backtest on entire dataset
        results = strategy.backtest(self.data)
        
        # Save results
        with open(os.path.join(self.output_dir, "best_strategy_results.json"), "w") as f:
            json.dump(results, f, indent=2)
        
        logger.info(f"Best strategy results: Sharpe={results['sharpe_ratio']:.2f}, Return={results['total_return']:.2f}, Win Rate={results['win_rate']:.2f}")
        
        return results


def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Optimize Mean Reversion strategy")
    parser.add_argument("--data", type=str, required=True, help="Path to historical data CSV")
    parser.add_argument("--output", type=str, default="output/mean_reversion_optimization", help="Output directory")
    parser.add_argument("--test-size", type=float, default=0.3, help="Proportion of data to use for testing")
    parser.add_argument("--top-n", type=int, default=10, help="Number of top parameter combinations to return")
    
    args = parser.parse_args()
    
    # Create optimizer
    optimizer = MeanReversionOptimizer(args.data, args.output)
    
    # Run optimization
    top_results = optimizer.optimize(args.test_size, args.top_n)
    
    # Visualize results
    optimizer.visualize_results(args.top_n)
    
    # Run best strategy
    best_results = optimizer.run_best_strategy()
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
