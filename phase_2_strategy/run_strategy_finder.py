#!/usr/bin/env python3
"""
Run Strategy Finder for Synergy7 Trading System.

This script runs the entire strategy finding process, including:
1. Market regime detection
2. Mean Reversion strategy optimization
3. Momentum strategy optimization
4. Strategy selection based on market regime
5. Combined strategy backtesting
"""

import os
import sys
import json
import logging
import argparse
import numpy as np
import pandas as pd
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
import matplotlib.pyplot as plt
import seaborn as sns

# Add parent directory to path
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

# Import components
from phase_2_strategy.market_regime import MarketRegimeDetector
from phase_2_strategy.mean_reversion import MeanReversionStrategy
from phase_2_strategy.momentum_strategy import MomentumStrategy
from phase_2_strategy.risk_management import RiskManager
from phase_2_strategy.optimize_mean_reversion import MeanReversionOptimizer
from phase_2_strategy.optimize_momentum import MomentumOptimizer
from phase_2_strategy.strategy_finder import StrategyFinder

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(os.path.join(parent_dir, "logs", "run_strategy_finder.log")),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("run_strategy_finder")

def download_data(symbol: str, timeframe: str, start_date: str, end_date: str, output_path: str) -> str:
    """
    Download historical data for a symbol.
    
    Args:
        symbol: Trading symbol
        timeframe: Timeframe (e.g., "1h", "1d")
        start_date: Start date (YYYY-MM-DD)
        end_date: End date (YYYY-MM-DD)
        output_path: Output directory
        
    Returns:
        Path to downloaded data file
    """
    logger.info(f"Downloading data for {symbol} ({timeframe}) from {start_date} to {end_date}")
    
    # Create output directory if it doesn't exist
    os.makedirs(output_path, exist_ok=True)
    
    # Generate output file path
    output_file = os.path.join(output_path, f"{symbol}_{timeframe}_{start_date}_{end_date}.csv")
    
    # Check if file already exists
    if os.path.exists(output_file):
        logger.info(f"Data file already exists: {output_file}")
        return output_file
    
    # TODO: Implement data download logic
    # For now, we'll assume the data file already exists
    
    logger.info(f"Data downloaded to {output_file}")
    
    return output_file

def run_market_regime_detection(data_path: str, output_dir: str) -> Dict[str, Any]:
    """
    Run market regime detection.
    
    Args:
        data_path: Path to historical data
        output_dir: Output directory
        
    Returns:
        Market regime detection results
    """
    logger.info("Running market regime detection")
    
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Load data
    data = pd.read_csv(data_path)
    
    # Convert timestamp to datetime if present
    if 'timestamp' in data.columns:
        data['timestamp'] = pd.to_datetime(data['timestamp'])
        data.set_index('timestamp', inplace=True)
    
    # Create market regime detector
    detector = MarketRegimeDetector()
    
    # Initialize results
    results = {
        "regimes": [],
        "transitions": [],
        "summary": {}
    }
    
    # Analyze each window of data
    window_size = 50  # Use 50 data points for each analysis
    step_size = 10  # Step by 10 data points
    
    current_regime = None
    regime_start = None
    regime_count = {}
    
    for i in range(window_size, len(data), step_size):
        # Get data window
        window = data.iloc[i-window_size:i]
        
        # Detect regime
        regime, metrics = detector.detect_regime(window)
        
        # Check if regime has changed
        if regime != current_regime:
            # Record previous regime if it exists
            if current_regime is not None:
                regime_period = {
                    "regime": current_regime.value,
                    "start": regime_start,
                    "end": window.index[-1],
                    "duration": (window.index[-1] - regime_start).total_seconds() / 86400  # in days
                }
                
                results["regimes"].append(regime_period)
                
                # Record transition
                results["transitions"].append({
                    "from": current_regime.value,
                    "to": regime.value,
                    "time": window.index[-1]
                })
            
            # Start new regime
            current_regime = regime
            regime_start = window.index[-1]
            
            # Update regime count
            if regime.value not in regime_count:
                regime_count[regime.value] = 0
            regime_count[regime.value] += 1
    
    # Record final regime
    if current_regime is not None:
        regime_period = {
            "regime": current_regime.value,
            "start": regime_start,
            "end": data.index[-1],
            "duration": (data.index[-1] - regime_start).total_seconds() / 86400  # in days
        }
        
        results["regimes"].append(regime_period)
    
    # Calculate summary statistics
    total_days = (data.index[-1] - data.index[0]).total_seconds() / 86400
    
    regime_summary = {}
    for regime_period in results["regimes"]:
        regime = regime_period["regime"]
        duration = regime_period["duration"]
        
        if regime not in regime_summary:
            regime_summary[regime] = {
                "count": 0,
                "total_duration": 0,
                "avg_duration": 0,
                "pct_time": 0
            }
        
        regime_summary[regime]["count"] += 1
        regime_summary[regime]["total_duration"] += duration
    
    # Calculate average duration and percentage of time
    for regime, summary in regime_summary.items():
        summary["avg_duration"] = summary["total_duration"] / summary["count"]
        summary["pct_time"] = summary["total_duration"] / total_days * 100
    
    results["summary"] = regime_summary
    
    # Save results
    output_file = os.path.join(output_dir, "market_regime_results.json")
    with open(output_file, "w") as f:
        json.dump(results, f, indent=2, default=str)
    
    logger.info(f"Market regime detection complete. Results saved to {output_file}")
    
    return results

def run_mean_reversion_optimization(data_path: str, output_dir: str) -> Dict[str, Any]:
    """
    Run Mean Reversion strategy optimization.
    
    Args:
        data_path: Path to historical data
        output_dir: Output directory
        
    Returns:
        Optimization results
    """
    logger.info("Running Mean Reversion strategy optimization")
    
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Create optimizer
    optimizer = MeanReversionOptimizer(data_path, output_dir)
    
    # Run optimization
    top_results = optimizer.optimize(test_size=0.3, top_n=10)
    
    # Visualize results
    optimizer.visualize_results(top_n=10)
    
    # Run best strategy
    best_results = optimizer.run_best_strategy()
    
    logger.info(f"Mean Reversion optimization complete. Results saved to {output_dir}")
    
    return best_results

def run_momentum_optimization(data_path: str, output_dir: str) -> Dict[str, Any]:
    """
    Run Momentum strategy optimization.
    
    Args:
        data_path: Path to historical data
        output_dir: Output directory
        
    Returns:
        Optimization results
    """
    logger.info("Running Momentum strategy optimization")
    
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Create optimizer
    optimizer = MomentumOptimizer(data_path, output_dir)
    
    # Run optimization
    top_results = optimizer.optimize(test_size=0.3, top_n=10, max_combinations=100)
    
    # Visualize results
    optimizer.visualize_results(top_n=10)
    
    # Run best strategy
    best_results = optimizer.run_best_strategy()
    
    logger.info(f"Momentum optimization complete. Results saved to {output_dir}")
    
    return best_results

def run_combined_strategy_backtest(data_path: str, output_dir: str, 
                                 mean_reversion_params: Dict[str, Any],
                                 momentum_params: Dict[str, Any]) -> Dict[str, Any]:
    """
    Run combined strategy backtest.
    
    Args:
        data_path: Path to historical data
        output_dir: Output directory
        mean_reversion_params: Mean Reversion strategy parameters
        momentum_params: Momentum strategy parameters
        
    Returns:
        Backtest results
    """
    logger.info("Running combined strategy backtest")
    
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Create strategy finder
    finder = StrategyFinder(data_path, output_dir)
    
    # Override strategy parameters
    finder.config["mean_reversion"] = mean_reversion_params
    finder.config["momentum"] = momentum_params
    
    # Run strategy finder
    results = finder.run()
    
    logger.info(f"Combined strategy backtest complete. Results saved to {output_dir}")
    
    return results

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Run Strategy Finder")
    parser.add_argument("--data", type=str, help="Path to historical data CSV")
    parser.add_argument("--output", type=str, default="output/strategy_finder", help="Output directory")
    parser.add_argument("--symbol", type=str, default="SOL/USD", help="Trading symbol")
    parser.add_argument("--timeframe", type=str, default="1h", help="Timeframe")
    parser.add_argument("--start-date", type=str, default="2023-01-01", help="Start date (YYYY-MM-DD)")
    parser.add_argument("--end-date", type=str, default="2023-12-31", help="End date (YYYY-MM-DD)")
    parser.add_argument("--skip-download", action="store_true", help="Skip data download")
    parser.add_argument("--skip-regime", action="store_true", help="Skip market regime detection")
    parser.add_argument("--skip-mean-reversion", action="store_true", help="Skip Mean Reversion optimization")
    parser.add_argument("--skip-momentum", action="store_true", help="Skip Momentum optimization")
    parser.add_argument("--skip-combined", action="store_true", help="Skip combined strategy backtest")
    
    args = parser.parse_args()
    
    # Create output directory if it doesn't exist
    os.makedirs(args.output, exist_ok=True)
    
    # Create logs directory if it doesn't exist
    os.makedirs(os.path.join(parent_dir, "logs"), exist_ok=True)
    
    # Download data if needed
    if not args.skip_download:
        if args.data:
            logger.info(f"Using existing data file: {args.data}")
            data_path = args.data
        else:
            data_path = download_data(
                symbol=args.symbol,
                timeframe=args.timeframe,
                start_date=args.start_date,
                end_date=args.end_date,
                output_path=os.path.join(args.output, "data")
            )
    else:
        if not args.data:
            logger.error("Data path must be provided when skipping download")
            return 1
        data_path = args.data
    
    # Run market regime detection
    if not args.skip_regime:
        regime_results = run_market_regime_detection(
            data_path=data_path,
            output_dir=os.path.join(args.output, "market_regime")
        )
    
    # Run Mean Reversion optimization
    if not args.skip_mean_reversion:
        mean_reversion_results = run_mean_reversion_optimization(
            data_path=data_path,
            output_dir=os.path.join(args.output, "mean_reversion")
        )
    else:
        # Load existing results
        try:
            with open(os.path.join(args.output, "mean_reversion", "best_strategy_results.json"), "r") as f:
                mean_reversion_results = json.load(f)
        except:
            logger.warning("Could not load existing Mean Reversion results")
            mean_reversion_results = {}
    
    # Run Momentum optimization
    if not args.skip_momentum:
        momentum_results = run_momentum_optimization(
            data_path=data_path,
            output_dir=os.path.join(args.output, "momentum")
        )
    else:
        # Load existing results
        try:
            with open(os.path.join(args.output, "momentum", "best_strategy_results.json"), "r") as f:
                momentum_results = json.load(f)
        except:
            logger.warning("Could not load existing Momentum results")
            momentum_results = {}
    
    # Run combined strategy backtest
    if not args.skip_combined:
        # Get strategy parameters
        mean_reversion_params = {}
        momentum_params = {}
        
        # Load Mean Reversion parameters
        try:
            with open(os.path.join(args.output, "mean_reversion", "top_results.json"), "r") as f:
                top_results = json.load(f)
                mean_reversion_params = top_results[0]["params"]
        except:
            logger.warning("Could not load Mean Reversion parameters")
        
        # Load Momentum parameters
        try:
            with open(os.path.join(args.output, "momentum", "top_results.json"), "r") as f:
                top_results = json.load(f)
                momentum_params = top_results[0]["params"]
        except:
            logger.warning("Could not load Momentum parameters")
        
        # Run combined strategy backtest
        combined_results = run_combined_strategy_backtest(
            data_path=data_path,
            output_dir=os.path.join(args.output, "combined"),
            mean_reversion_params=mean_reversion_params,
            momentum_params=momentum_params
        )
    
    logger.info("Strategy finding process complete")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
