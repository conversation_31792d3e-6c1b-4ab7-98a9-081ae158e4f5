# Synergy7 Strategy Visualization Guide

This guide explains how to use the Synergy7 visualization tools to generate comprehensive visual statistics for strategy validation, parameter optimization, and performance analysis.

## Overview

The visualization module provides a wide range of visualizations for:

1. **Strategy Performance**: Equity curves, drawdowns, returns distribution, etc.
2. **Parameter Optimization**: Parameter sensitivity analysis, performance comparison, etc.
3. **Market Regime Analysis**: Performance by market regime, regime transitions, etc.

## Key Features

- **Incremental Visualization**: Call visualizations in small increments as needed
- **Comprehensive Statistics**: Detailed visual statistics for transparency and control
- **Impact Assessment**: Visualize the impact of different parameters and market regimes
- **Dark Mode Support**: Choose between light and dark mode for visualizations
- **Multiple Export Formats**: Save visualizations in PNG, PDF, and other formats

## Getting Started

### Generate Sample Data

To generate sample data for testing the visualization module:

```bash
python phase_2_strategy/generate_sample_data.py \
  --output sample_data \
  --start-date 2023-01-01 \
  --end-date 2023-12-31 \
  --initial-capital 10000.0 \
  --symbol SOL/USD \
  --num-combinations 100
```

This will generate:
- `sample_backtest_results.json`: Sample backtest results
- `sample_optimization_results.json`: Sample optimization results
- `sample_market_regime_results.json`: Sample market regime results

### Visualize Strategy Performance

To visualize strategy performance:

```bash
python phase_2_strategy/visualize_strategy.py \
  --backtest sample_data/sample_backtest_results.json \
  --output visualizations \
  --performance
```

This will generate:
- `equity_curve.png`: Equity curve
- `drawdown.png`: Drawdown chart
- `returns_distribution.png`: Returns distribution
- `monthly_returns_heatmap.png`: Monthly returns heatmap
- `rolling_sharpe_252.png`: Rolling Sharpe ratio (252 days)
- `underwater_equity.png`: Underwater equity curve

### Visualize Parameter Sensitivity

To visualize parameter sensitivity:

```bash
python phase_2_strategy/visualize_strategy.py \
  --optimization sample_data/sample_optimization_results.json \
  --output visualizations \
  --parameters
```

This will generate parameter sensitivity visualizations for each parameter:
- `sharpe_vs_lookback_period.png`: Sharpe ratio vs lookback period
- `return_vs_lookback_period.png`: Total return vs lookback period
- `win_rate_vs_lookback_period.png`: Win rate vs lookback period
- etc.

### Visualize Market Regime Performance

To visualize performance by market regime:

```bash
python phase_2_strategy/visualize_strategy.py \
  --backtest sample_data/sample_backtest_results.json \
  --regime sample_data/sample_market_regime_results.json \
  --output visualizations \
  --regimes
```

This will generate:
- `regime_performance.png`: Performance by market regime

### Visualize All

To visualize all aspects of strategy performance:

```bash
python phase_2_strategy/visualize_strategy.py \
  --backtest sample_data/sample_backtest_results.json \
  --optimization sample_data/sample_optimization_results.json \
  --regime sample_data/sample_market_regime_results.json \
  --output visualizations
```

## Available Visualizations

### Strategy Performance

1. **Equity Curve**: Shows the growth of the portfolio over time
   - `plot_equity_curve(portfolio_df, benchmark_df=None, title="Equity Curve", filename="equity_curve")`

2. **Drawdown**: Shows the drawdowns over time
   - `plot_drawdown(portfolio_df, title="Drawdown", filename="drawdown")`

3. **Returns Distribution**: Shows the distribution of returns with key statistics
   - `plot_returns_distribution(returns, title="Returns Distribution", filename="returns_distribution")`

4. **Monthly Returns Heatmap**: Shows monthly returns by year
   - `plot_monthly_returns_heatmap(returns, title="Monthly Returns", filename="monthly_returns_heatmap")`

5. **Rolling Sharpe Ratio**: Shows the rolling Sharpe ratio over time
   - `plot_rolling_sharpe(returns, window=252, title="Rolling Sharpe Ratio", filename="rolling_sharpe")`

6. **Underwater Equity**: Shows the drawdown-adjusted equity curve
   - `plot_underwater_equity(portfolio_df, title="Underwater Equity Curve", filename="underwater_equity")`

### Parameter Optimization

1. **Parameter Sensitivity**: Shows the sensitivity of performance metrics to parameter values
   - `plot_parameter_sensitivity(param_results, param_name, metric_name="sharpe_ratio", title=None, filename=None)`

### Market Regime Analysis

1. **Regime Performance**: Shows performance metrics by market regime
   - `plot_regime_performance(returns, regimes, title="Performance by Market Regime", filename="regime_performance")`

## Using the Visualization Module in Your Code

You can use the visualization module directly in your code:

```python
from phase_2_strategy.visualization import StrategyVisualizer

# Create visualizer
visualizer = StrategyVisualizer(output_dir="visualizations", dark_mode=False)

# Visualize equity curve
visualizer.plot_equity_curve(portfolio_df, title="My Strategy Equity Curve", filename="my_equity_curve")

# Visualize drawdown
visualizer.plot_drawdown(portfolio_df, title="My Strategy Drawdown", filename="my_drawdown")

# Visualize returns distribution
visualizer.plot_returns_distribution(returns, title="My Returns Distribution", filename="my_returns_distribution")
```

## Customizing Visualizations

The visualization module supports customization through:

1. **Dark Mode**: Set `dark_mode=True` when creating the visualizer
2. **Custom Titles**: Provide custom titles for each visualization
3. **Custom Filenames**: Provide custom filenames for each visualization
4. **Multiple Formats**: Save visualizations in multiple formats

## Interpreting Visualizations

### Equity Curve

- **Upward Trend**: Strategy is profitable
- **Steep Slope**: High returns
- **Flat Periods**: No returns
- **Downward Trend**: Losing periods

### Drawdown

- **Depth**: Maximum loss from peak
- **Duration**: Time to recover from drawdown
- **Frequency**: How often drawdowns occur

### Returns Distribution

- **Mean**: Average return
- **Median**: Middle return
- **Standard Deviation**: Volatility
- **Skewness**: Asymmetry of returns
- **Kurtosis**: Tail risk
- **Sharpe Ratio**: Risk-adjusted return
- **Sortino Ratio**: Downside risk-adjusted return

### Monthly Returns Heatmap

- **Green Cells**: Positive months
- **Red Cells**: Negative months
- **Patterns**: Seasonality or calendar effects

### Rolling Sharpe Ratio

- **Above 1**: Good risk-adjusted return
- **Below 0**: Negative risk-adjusted return
- **Trend**: Improving or deteriorating performance

### Underwater Equity

- **Gap Size**: Magnitude of drawdowns
- **Recovery Time**: Time to recover from drawdowns

### Parameter Sensitivity

- **Flat Line**: Parameter has little impact
- **Steep Line**: Parameter has significant impact
- **Peak**: Optimal parameter value
- **Train vs. Test Gap**: Potential overfitting

### Regime Performance

- **Consistent Performance**: Strategy works in all regimes
- **Regime Dependence**: Strategy works better in specific regimes
- **Negative Regimes**: Regimes to avoid

## Dependencies

- Python 3.7+
- pandas
- numpy
- matplotlib
- seaborn
- plotly

## Troubleshooting

### Missing Data

If visualizations show missing data:
- Check that the data format is correct
- Ensure that the data has the required columns
- Check for NaN values in the data

### Empty Visualizations

If visualizations are empty:
- Check that the data is not empty
- Ensure that the date range is correct
- Check that the data has the required columns

### Error Messages

If you encounter error messages:
- Check the logs for detailed error information
- Ensure that all dependencies are installed
- Verify that the data format is correct
