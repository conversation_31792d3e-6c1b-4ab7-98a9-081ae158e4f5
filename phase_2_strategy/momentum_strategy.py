"""
Enhanced Momentum Strategy for Synergy7 Trading System.

This module implements an enhanced momentum strategy with trend confirmation,
pullback entry, and improved exit rules.
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime

# Configure logging
logger = logging.getLogger(__name__)

class MomentumStrategy:
    """
    Enhanced Momentum Strategy.
    
    This strategy identifies trending markets and enters on pullbacks with
    multiple confirmation filters to improve win rate.
    """
    
    def __init__(self, **kwargs):
        """
        Initialize the strategy with configuration parameters.
        
        Args:
            **kwargs: Strategy parameters
        """
        self.name = "enhanced_momentum"
        self.version = "2.0.0"
        
        # Extract core strategy parameters
        self.symbol = kwargs.get("symbol", "SOL/USD")
        
        # Trend detection parameters
        self.ema_short_period = kwargs.get("ema_short_period", 20)
        self.ema_medium_period = kwargs.get("ema_medium_period", 50)
        self.ema_long_period = kwargs.get("ema_long_period", 200)
        self.adx_period = kwargs.get("adx_period", 14)
        self.adx_threshold = kwargs.get("adx_threshold", 25)
        
        # Entry parameters
        self.rsi_period = kwargs.get("rsi_period", 14)
        self.rsi_oversold = kwargs.get("rsi_oversold", 30)
        self.rsi_overbought = kwargs.get("rsi_overbought", 70)
        self.pullback_threshold = kwargs.get("pullback_threshold", 0.03)  # 3% pullback
        self.volume_ma_period = kwargs.get("volume_ma_period", 20)
        self.volume_threshold = kwargs.get("volume_threshold", 1.5)  # 1.5x average volume
        
        # Exit parameters
        self.take_profit_atr_multiple = kwargs.get("take_profit_atr_multiple", 3.0)
        self.stop_loss_atr_multiple = kwargs.get("stop_loss_atr_multiple", 1.5)
        self.trailing_stop_atr_multiple = kwargs.get("trailing_stop_atr_multiple", 2.0)
        self.atr_period = kwargs.get("atr_period", 14)
        self.exit_ema_period = kwargs.get("exit_ema_period", 10)
        
        # Risk parameters
        self.risk_per_trade = kwargs.get("risk_per_trade", 0.01)  # 1% risk per trade
        self.max_position_size_pct = kwargs.get("max_position_size_pct", 0.05)  # 5% of portfolio
        
        logger.info(f"Initialized {self.name} strategy with ema_short_period={self.ema_short_period}, "
                   f"ema_medium_period={self.ema_medium_period}, ema_long_period={self.ema_long_period}")
    
    def generate_signals(self, df: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        Generate trading signals based on momentum strategy.
        
        Args:
            df: DataFrame with OHLCV data
            **kwargs: Additional parameters
            
        Returns:
            DataFrame with signals
        """
        # Make a copy of the data
        signals = df.copy()
        
        # Check if we have enough data
        min_periods = max(self.ema_long_period, self.adx_period, self.rsi_period) + 50
        if len(signals) < min_periods:
            logger.warning(f"Not enough data for momentum calculation. Need at least {min_periods} data points.")
            return pd.DataFrame()
        
        # Calculate EMAs
        signals['ema_short'] = signals['close'].ewm(span=self.ema_short_period, adjust=False).mean()
        signals['ema_medium'] = signals['close'].ewm(span=self.ema_medium_period, adjust=False).mean()
        signals['ema_long'] = signals['close'].ewm(span=self.ema_long_period, adjust=False).mean()
        signals['exit_ema'] = signals['close'].ewm(span=self.exit_ema_period, adjust=False).mean()
        
        # Calculate ADX for trend strength
        signals = self._calculate_adx(signals)
        
        # Calculate RSI for entry timing
        signals = self._calculate_rsi(signals)
        
        # Calculate ATR for stop loss and take profit
        signals = self._calculate_atr(signals)
        
        # Calculate volume moving average
        signals['volume_ma'] = signals['volume'].rolling(window=self.volume_ma_period).mean()
        signals['volume_ratio'] = signals['volume'] / signals['volume_ma']
        
        # Calculate pullbacks
        signals['pct_change'] = signals['close'].pct_change()
        signals['pullback'] = 0.0
        
        # Calculate pullback size (cumulative negative returns)
        for i in range(1, len(signals)):
            if signals.iloc[i]['pct_change'] < 0:
                signals.iloc[i, signals.columns.get_loc('pullback')] = signals.iloc[i-1]['pullback'] + abs(signals.iloc[i]['pct_change'])
            else:
                signals.iloc[i, signals.columns.get_loc('pullback')] = 0.0
        
        # Initialize signal column
        signals['signal'] = 0
        signals['trend'] = 0
        
        # Identify trend
        signals.loc[(signals['ema_short'] > signals['ema_medium']) & 
                   (signals['ema_medium'] > signals['ema_long']) &
                   (signals['adx'] > self.adx_threshold), 'trend'] = 1
        
        signals.loc[(signals['ema_short'] < signals['ema_medium']) & 
                   (signals['ema_medium'] < signals['ema_long']) &
                   (signals['adx'] > self.adx_threshold), 'trend'] = -1
        
        # Generate long signals
        # Buy when in uptrend, on pullback, with RSI oversold and high volume
        signals.loc[(signals['trend'] == 1) & 
                   (signals['pullback'] >= self.pullback_threshold) &
                   (signals['rsi'] < self.rsi_oversold) &
                   (signals['volume_ratio'] > self.volume_threshold), 'signal'] = 1
        
        # Generate short signals
        # Sell when in downtrend, on pullback, with RSI overbought and high volume
        signals.loc[(signals['trend'] == -1) & 
                   (signals['pullback'] >= self.pullback_threshold) &
                   (signals['rsi'] > self.rsi_overbought) &
                   (signals['volume_ratio'] > self.volume_threshold), 'signal'] = -1
        
        # Generate exit signals
        # Exit long when price crosses below exit EMA
        signals.loc[(signals['close'] < signals['exit_ema']) & 
                   (signals['close'].shift(1) >= signals['exit_ema'].shift(1)), 'signal'] = -1
        
        # Exit short when price crosses above exit EMA
        signals.loc[(signals['close'] > signals['exit_ema']) & 
                   (signals['close'].shift(1) <= signals['exit_ema'].shift(1)), 'signal'] = 1
        
        # Calculate confidence based on trend strength and volume
        signals['confidence'] = (signals['adx'] / 100) * (signals['volume_ratio'] / self.volume_threshold).clip(0, 2) * 0.5
        
        # Generate position column for tracking changes
        signals['position'] = signals['signal'].diff()
        
        return signals
    
    def _calculate_adx(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate Average Directional Index (ADX).
        
        Args:
            df: DataFrame with OHLCV data
            
        Returns:
            DataFrame with ADX values
        """
        # Make a copy of the data
        data = df.copy()
        
        # Calculate True Range (TR)
        data['tr0'] = abs(data['high'] - data['low'])
        data['tr1'] = abs(data['high'] - data['close'].shift())
        data['tr2'] = abs(data['low'] - data['close'].shift())
        data['tr'] = data[['tr0', 'tr1', 'tr2']].max(axis=1)
        
        # Calculate Directional Movement (DM)
        data['up_move'] = data['high'] - data['high'].shift()
        data['down_move'] = data['low'].shift() - data['low']
        
        # Calculate Positive and Negative DM
        data['plus_dm'] = 0
        data.loc[(data['up_move'] > data['down_move']) & (data['up_move'] > 0), 'plus_dm'] = data['up_move']
        
        data['minus_dm'] = 0
        data.loc[(data['down_move'] > data['up_move']) & (data['down_move'] > 0), 'minus_dm'] = data['down_move']
        
        # Calculate Smoothed TR and DM
        period = self.adx_period
        
        # Calculate smoothed TR
        data['atr'] = data['tr'].rolling(window=period).mean()
        
        # Calculate smoothed +DM and -DM
        data['plus_di'] = 100 * (data['plus_dm'].rolling(window=period).mean() / data['atr'])
        data['minus_di'] = 100 * (data['minus_dm'].rolling(window=period).mean() / data['atr'])
        
        # Calculate Directional Index (DX)
        data['dx'] = 100 * (abs(data['plus_di'] - data['minus_di']) / (data['plus_di'] + data['minus_di']))
        
        # Calculate ADX
        data['adx'] = data['dx'].rolling(window=period).mean()
        
        return data
    
    def _calculate_rsi(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate Relative Strength Index (RSI).
        
        Args:
            df: DataFrame with OHLCV data
            
        Returns:
            DataFrame with RSI values
        """
        # Make a copy of the data
        data = df.copy()
        
        # Calculate price changes
        delta = data['close'].diff()
        
        # Calculate gains and losses
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)
        
        # Calculate average gains and losses
        avg_gain = gain.rolling(window=self.rsi_period).mean()
        avg_loss = loss.rolling(window=self.rsi_period).mean()
        
        # Calculate RS
        rs = avg_gain / avg_loss
        
        # Calculate RSI
        data['rsi'] = 100 - (100 / (1 + rs))
        
        return data
    
    def _calculate_atr(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate Average True Range (ATR).
        
        Args:
            df: DataFrame with OHLCV data
            
        Returns:
            DataFrame with ATR values
        """
        # Make a copy of the data
        data = df.copy()
        
        # Calculate True Range (TR)
        data['tr0'] = abs(data['high'] - data['low'])
        data['tr1'] = abs(data['high'] - data['close'].shift())
        data['tr2'] = abs(data['low'] - data['close'].shift())
        data['tr'] = data[['tr0', 'tr1', 'tr2']].max(axis=1)
        
        # Calculate ATR
        data['atr'] = data['tr'].rolling(window=self.atr_period).mean()
        
        return data
    
    def calculate_position_size(self, signals: pd.DataFrame, capital: float) -> pd.DataFrame:
        """
        Calculate position size based on risk management rules.
        
        Args:
            signals: DataFrame with signals
            capital: Available capital
            
        Returns:
            DataFrame with position sizes
        """
        # Make a copy of the signals
        result = signals.copy()
        
        # Initialize position size column
        result['position_size'] = 0.0
        
        # Calculate position size based on ATR
        for idx in result.index:
            if result.loc[idx, 'signal'] != 0 and not np.isnan(result.loc[idx, 'atr']):
                # Calculate risk amount
                risk_amount = capital * self.risk_per_trade
                
                # Calculate stop loss distance in price terms
                stop_distance = result.loc[idx, 'atr'] * self.stop_loss_atr_multiple
                
                # Calculate position size
                if stop_distance > 0:
                    position_size = risk_amount / stop_distance
                    
                    # Apply maximum limit
                    max_position_size = capital * self.max_position_size_pct / result.loc[idx, 'close']
                    position_size = min(position_size, max_position_size)
                    
                    result.loc[idx, 'position_size'] = position_size
        
        return result
    
    def backtest(self, data: pd.DataFrame, initial_capital: float = 10000.0) -> Dict[str, Any]:
        """
        Backtest the strategy on historical data.
        
        Args:
            data: DataFrame with OHLCV data
            initial_capital: Initial capital for backtesting
            
        Returns:
            Dictionary with backtest results
        """
        # Generate signals
        signals = self.generate_signals(data)
        
        if signals.empty:
            logger.warning("No signals generated for backtesting")
            return {
                "strategy": self.name,
                "version": self.version,
                "symbol": self.symbol,
                "initial_capital": initial_capital,
                "final_capital": initial_capital,
                "returns": 0.0,
                "sharpe_ratio": 0.0,
                "max_drawdown": 0.0,
                "win_rate": 0.0,
                "trades": []
            }
        
        # Calculate position sizes
        signals = self.calculate_position_size(signals, initial_capital)
        
        # Initialize portfolio and tracking variables
        portfolio = pd.DataFrame(index=signals.index)
        portfolio['holdings'] = 0.0
        portfolio['cash'] = initial_capital
        portfolio['total'] = initial_capital
        portfolio['returns'] = 0.0
        
        # Track trades
        trades = []
        current_position = 0
        entry_price = 0
        entry_date = None
        stop_loss = 0
        take_profit = 0
        
        # Simulate trading
        for i, idx in enumerate(portfolio.index):
            if i == 0:  # Skip first row
                continue
                
            # Get signal
            signal = signals.loc[idx, 'signal']
            position_size = signals.loc[idx, 'position_size']
            
            # Update portfolio based on signal
            if signal == 1 and current_position <= 0:  # Buy signal
                # Calculate shares to buy
                price = signals.loc[idx, 'close']
                shares = position_size
                
                # Calculate stop loss and take profit
                atr = signals.loc[idx, 'atr']
                stop_loss = price - (atr * self.stop_loss_atr_multiple)
                take_profit = price + (atr * self.take_profit_atr_multiple)
                
                # Update portfolio
                portfolio.loc[idx, 'holdings'] = shares * price
                portfolio.loc[idx, 'cash'] = portfolio.loc[portfolio.index[i-1], 'cash'] - shares * price
                
                # Record trade entry
                current_position = 1
                entry_price = price
                entry_date = idx
                
            elif signal == -1 and current_position >= 0:  # Sell signal
                # Calculate shares to sell
                price = signals.loc[idx, 'close']
                
                if current_position == 1:  # Exit long position
                    shares = portfolio.loc[portfolio.index[i-1], 'holdings'] / signals.loc[portfolio.index[i-1], 'close']
                    
                    # Record trade exit
                    exit_date = idx
                    exit_price = price
                    profit_loss = (exit_price - entry_price) * shares
                    profit_loss_pct = (exit_price - entry_price) / entry_price
                    
                    trades.append({
                        "entry_date": entry_date,
                        "exit_date": exit_date,
                        "entry_price": entry_price,
                        "exit_price": exit_price,
                        "position": "long",
                        "profit_loss": profit_loss,
                        "profit_loss_pct": profit_loss_pct
                    })
                    
                    # Update portfolio for exit
                    portfolio.loc[idx, 'holdings'] = 0
                    portfolio.loc[idx, 'cash'] = portfolio.loc[portfolio.index[i-1], 'cash'] + shares * price
                
                # Calculate new short position
                shares = position_size
                
                # Calculate stop loss and take profit for short
                atr = signals.loc[idx, 'atr']
                stop_loss = price + (atr * self.stop_loss_atr_multiple)
                take_profit = price - (atr * self.take_profit_atr_multiple)
                
                # Update portfolio for new short position
                portfolio.loc[idx, 'holdings'] = -shares * price
                portfolio.loc[idx, 'cash'] = portfolio.loc[idx, 'cash'] + shares * price
                
                # Record new trade entry
                current_position = -1
                entry_price = price
                entry_date = idx
            
            else:  # No signal or same direction
                # Check stop loss and take profit
                price = signals.loc[idx, 'close']
                
                if current_position == 1:  # Long position
                    # Check stop loss
                    if price <= stop_loss:
                        # Calculate shares
                        shares = portfolio.loc[portfolio.index[i-1], 'holdings'] / signals.loc[portfolio.index[i-1], 'close']
                        
                        # Record trade exit
                        exit_date = idx
                        exit_price = price
                        profit_loss = (exit_price - entry_price) * shares
                        profit_loss_pct = (exit_price - entry_price) / entry_price
                        
                        trades.append({
                            "entry_date": entry_date,
                            "exit_date": exit_date,
                            "entry_price": entry_price,
                            "exit_price": exit_price,
                            "position": "long",
                            "profit_loss": profit_loss,
                            "profit_loss_pct": profit_loss_pct,
                            "exit_reason": "stop_loss"
                        })
                        
                        # Update portfolio
                        portfolio.loc[idx, 'holdings'] = 0
                        portfolio.loc[idx, 'cash'] = portfolio.loc[portfolio.index[i-1], 'cash'] + shares * price
                        
                        # Reset position
                        current_position = 0
                    
                    # Check take profit
                    elif price >= take_profit:
                        # Calculate shares
                        shares = portfolio.loc[portfolio.index[i-1], 'holdings'] / signals.loc[portfolio.index[i-1], 'close']
                        
                        # Record trade exit
                        exit_date = idx
                        exit_price = price
                        profit_loss = (exit_price - entry_price) * shares
                        profit_loss_pct = (exit_price - entry_price) / entry_price
                        
                        trades.append({
                            "entry_date": entry_date,
                            "exit_date": exit_date,
                            "entry_price": entry_price,
                            "exit_price": exit_price,
                            "position": "long",
                            "profit_loss": profit_loss,
                            "profit_loss_pct": profit_loss_pct,
                            "exit_reason": "take_profit"
                        })
                        
                        # Update portfolio
                        portfolio.loc[idx, 'holdings'] = 0
                        portfolio.loc[idx, 'cash'] = portfolio.loc[portfolio.index[i-1], 'cash'] + shares * price
                        
                        # Reset position
                        current_position = 0
                    
                    else:
                        # Update holdings value
                        shares = portfolio.loc[portfolio.index[i-1], 'holdings'] / signals.loc[portfolio.index[i-1], 'close']
                        portfolio.loc[idx, 'holdings'] = shares * price
                        
                        # Update trailing stop loss
                        new_stop_loss = price - (signals.loc[idx, 'atr'] * self.trailing_stop_atr_multiple)
                        if new_stop_loss > stop_loss:
                            stop_loss = new_stop_loss
                        
                        # Carry forward cash
                        portfolio.loc[idx, 'cash'] = portfolio.loc[portfolio.index[i-1], 'cash']
                
                elif current_position == -1:  # Short position
                    # Check stop loss
                    if price >= stop_loss:
                        # Calculate shares
                        shares = -portfolio.loc[portfolio.index[i-1], 'holdings'] / signals.loc[portfolio.index[i-1], 'close']
                        
                        # Record trade exit
                        exit_date = idx
                        exit_price = price
                        profit_loss = (entry_price - exit_price) * shares
                        profit_loss_pct = (entry_price - exit_price) / entry_price
                        
                        trades.append({
                            "entry_date": entry_date,
                            "exit_date": exit_date,
                            "entry_price": entry_price,
                            "exit_price": exit_price,
                            "position": "short",
                            "profit_loss": profit_loss,
                            "profit_loss_pct": profit_loss_pct,
                            "exit_reason": "stop_loss"
                        })
                        
                        # Update portfolio
                        portfolio.loc[idx, 'holdings'] = 0
                        portfolio.loc[idx, 'cash'] = portfolio.loc[portfolio.index[i-1], 'cash'] - shares * price
                        
                        # Reset position
                        current_position = 0
                    
                    # Check take profit
                    elif price <= take_profit:
                        # Calculate shares
                        shares = -portfolio.loc[portfolio.index[i-1], 'holdings'] / signals.loc[portfolio.index[i-1], 'close']
                        
                        # Record trade exit
                        exit_date = idx
                        exit_price = price
                        profit_loss = (entry_price - exit_price) * shares
                        profit_loss_pct = (entry_price - exit_price) / entry_price
                        
                        trades.append({
                            "entry_date": entry_date,
                            "exit_date": exit_date,
                            "entry_price": entry_price,
                            "exit_price": exit_price,
                            "position": "short",
                            "profit_loss": profit_loss,
                            "profit_loss_pct": profit_loss_pct,
                            "exit_reason": "take_profit"
                        })
                        
                        # Update portfolio
                        portfolio.loc[idx, 'holdings'] = 0
                        portfolio.loc[idx, 'cash'] = portfolio.loc[portfolio.index[i-1], 'cash'] - shares * price
                        
                        # Reset position
                        current_position = 0
                    
                    else:
                        # Update holdings value
                        shares = -portfolio.loc[portfolio.index[i-1], 'holdings'] / signals.loc[portfolio.index[i-1], 'close']
                        portfolio.loc[idx, 'holdings'] = -shares * price
                        
                        # Update trailing stop loss
                        new_stop_loss = price + (signals.loc[idx, 'atr'] * self.trailing_stop_atr_multiple)
                        if new_stop_loss < stop_loss:
                            stop_loss = new_stop_loss
                        
                        # Carry forward cash
                        portfolio.loc[idx, 'cash'] = portfolio.loc[portfolio.index[i-1], 'cash']
                
                else:  # No position
                    # Carry forward cash
                    portfolio.loc[idx, 'cash'] = portfolio.loc[portfolio.index[i-1], 'cash']
            
            # Update total value and returns
            portfolio.loc[idx, 'total'] = portfolio.loc[idx, 'holdings'] + portfolio.loc[idx, 'cash']
            portfolio.loc[idx, 'returns'] = portfolio.loc[idx, 'total'] / portfolio.loc[portfolio.index[i-1], 'total'] - 1
        
        # Close any open position at the end
        if current_position != 0:
            exit_date = portfolio.index[-1]
            exit_price = signals.loc[exit_date, 'close']
            
            if current_position == 1:  # Long position
                shares = portfolio.loc[portfolio.index[-1], 'holdings'] / signals.loc[portfolio.index[-1], 'close']
                profit_loss = (exit_price - entry_price) * shares
                profit_loss_pct = (exit_price - entry_price) / entry_price
            else:  # Short position
                shares = -portfolio.loc[portfolio.index[-1], 'holdings'] / signals.loc[portfolio.index[-1], 'close']
                profit_loss = (entry_price - exit_price) * shares
                profit_loss_pct = (entry_price - exit_price) / entry_price
            
            trades.append({
                "entry_date": entry_date,
                "exit_date": exit_date,
                "entry_price": entry_price,
                "exit_price": exit_price,
                "position": "long" if current_position == 1 else "short",
                "profit_loss": profit_loss,
                "profit_loss_pct": profit_loss_pct,
                "exit_reason": "end_of_data"
            })
        
        # Calculate performance metrics
        total_return = portfolio['total'].iloc[-1] / initial_capital - 1
        daily_returns = portfolio['returns'].dropna()
        sharpe_ratio = daily_returns.mean() / daily_returns.std() * np.sqrt(252) if daily_returns.std() > 0 else 0
        
        # Calculate drawdown
        portfolio['cumulative_return'] = (1 + portfolio['returns']).cumprod()
        portfolio['drawdown'] = portfolio['cumulative_return'] / portfolio['cumulative_return'].cummax() - 1
        max_drawdown = portfolio['drawdown'].min()
        
        # Calculate win rate
        winning_trades = sum(1 for trade in trades if trade['profit_loss'] > 0)
        total_trades = len(trades)
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        # Calculate profit factor
        gross_profit = sum(trade['profit_loss'] for trade in trades if trade['profit_loss'] > 0)
        gross_loss = sum(abs(trade['profit_loss']) for trade in trades if trade['profit_loss'] < 0)
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')
        
        return {
            "strategy": self.name,
            "version": self.version,
            "symbol": self.symbol,
            "initial_capital": initial_capital,
            "final_capital": portfolio['total'].iloc[-1],
            "total_return": total_return,
            "sharpe_ratio": sharpe_ratio,
            "max_drawdown": max_drawdown,
            "win_rate": win_rate,
            "profit_factor": profit_factor,
            "total_trades": total_trades,
            "trades": trades,
            "portfolio": portfolio
        }
