#!/usr/bin/env python3
"""
Data Preparation Script for Synergy7 Trading System.

This script prepares the collected data for strategy optimization and backtesting
by calculating additional features and splitting the data into training and testing sets.
"""

import os
import sys
import json
import logging
import argparse
import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
import matplotlib.pyplot as plt
import seaborn as sns

# Add parent directory to path
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

# Import data collection module
from phase_2_strategy.data_collection import DataCollector

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(os.path.join(parent_dir, "logs", "prepare_data.log")),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("prepare_data")

class DataPreparation:
    """
    Data Preparation for strategy optimization and backtesting.
    
    This class prepares the collected data by calculating additional features
    and splitting the data into training and testing sets.
    """
    
    def __init__(self, data_path: str, output_dir: str):
        """
        Initialize the data preparation.
        
        Args:
            data_path: Path to raw data CSV
            output_dir: Directory to save prepared data
        """
        self.data_path = data_path
        self.output_dir = output_dir
        
        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)
        
        # Load data
        self.data = self._load_data(data_path)
        
        logger.info(f"Initialized data preparation with data from {data_path}")
        logger.info(f"Output directory: {output_dir}")
    
    def _load_data(self, data_path: str) -> pd.DataFrame:
        """
        Load data from CSV file.
        
        Args:
            data_path: Path to CSV file
            
        Returns:
            DataFrame with data
        """
        try:
            # Load data
            df = pd.read_csv(data_path)
            
            # Convert timestamp to datetime if it's a string
            if 'timestamp' in df.columns and isinstance(df['timestamp'].iloc[0], str):
                df['timestamp'] = pd.to_datetime(df['timestamp'])
            
            # Set timestamp as index if it exists
            if 'timestamp' in df.columns:
                df.set_index('timestamp', inplace=True)
            
            # Ensure required columns exist
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in required_columns:
                if col not in df.columns:
                    logger.error(f"Required column '{col}' not found in data")
                    raise ValueError(f"Required column '{col}' not found in data")
            
            logger.info(f"Loaded {len(df)} data points from {data_path}")
            return df
        
        except Exception as e:
            logger.error(f"Error loading data: {str(e)}")
            raise
    
    def calculate_features(self) -> pd.DataFrame:
        """
        Calculate additional features for strategy optimization.
        
        Returns:
            DataFrame with additional features
        """
        logger.info("Calculating additional features")
        
        # Make a copy of the data
        df = self.data.copy()
        
        # Calculate returns
        df['returns'] = df['close'].pct_change()
        
        # Calculate log returns
        df['log_returns'] = np.log(df['close'] / df['close'].shift(1))
        
        # Calculate moving averages
        for period in [5, 10, 20, 50, 100, 200]:
            df[f'ma_{period}'] = df['close'].rolling(window=period).mean()
        
        # Calculate exponential moving averages
        for period in [5, 10, 20, 50, 100, 200]:
            df[f'ema_{period}'] = df['close'].ewm(span=period, adjust=False).mean()
        
        # Calculate Bollinger Bands
        for period in [20, 50]:
            for std in [1.5, 2.0, 2.5]:
                # Calculate middle band (SMA)
                df[f'bb_middle_{period}_{std}'] = df['close'].rolling(window=period).mean()
                
                # Calculate standard deviation
                df[f'bb_std_{period}_{std}'] = df['close'].rolling(window=period).std()
                
                # Calculate upper and lower bands
                df[f'bb_upper_{period}_{std}'] = df[f'bb_middle_{period}_{std}'] + (df[f'bb_std_{period}_{std}'] * std)
                df[f'bb_lower_{period}_{std}'] = df[f'bb_middle_{period}_{std}'] - (df[f'bb_std_{period}_{std}'] * std)
                
                # Calculate %B
                df[f'bb_b_{period}_{std}'] = (df['close'] - df[f'bb_lower_{period}_{std}']) / (df[f'bb_upper_{period}_{std}'] - df[f'bb_lower_{period}_{std}'])
                
                # Calculate Bollinger Band width
                df[f'bb_width_{period}_{std}'] = (df[f'bb_upper_{period}_{std}'] - df[f'bb_lower_{period}_{std}']) / df[f'bb_middle_{period}_{std}']
        
        # Calculate RSI
        for period in [7, 14, 21]:
            # Calculate price changes
            delta = df['close'].diff()
            
            # Calculate gains and losses
            gain = delta.where(delta > 0, 0)
            loss = -delta.where(delta < 0, 0)
            
            # Calculate average gains and losses
            avg_gain = gain.rolling(window=period).mean()
            avg_loss = loss.rolling(window=period).mean()
            
            # Calculate RS
            rs = avg_gain / avg_loss
            
            # Calculate RSI
            df[f'rsi_{period}'] = 100 - (100 / (1 + rs))
        
        # Calculate MACD
        df['macd_12_26'] = df['close'].ewm(span=12, adjust=False).mean() - df['close'].ewm(span=26, adjust=False).mean()
        df['macd_signal_12_26_9'] = df['macd_12_26'].ewm(span=9, adjust=False).mean()
        df['macd_histogram_12_26_9'] = df['macd_12_26'] - df['macd_signal_12_26_9']
        
        # Calculate ATR
        for period in [7, 14, 21]:
            # Calculate True Range
            df['tr'] = np.maximum(
                df['high'] - df['low'],
                np.maximum(
                    abs(df['high'] - df['close'].shift(1)),
                    abs(df['low'] - df['close'].shift(1))
                )
            )
            
            # Calculate ATR
            df[f'atr_{period}'] = df['tr'].rolling(window=period).mean()
        
        # Calculate ADX
        for period in [7, 14, 21]:
            # Calculate +DM and -DM
            df['plus_dm'] = np.where(
                (df['high'] - df['high'].shift(1)) > (df['low'].shift(1) - df['low']),
                np.maximum(df['high'] - df['high'].shift(1), 0),
                0
            )
            df['minus_dm'] = np.where(
                (df['low'].shift(1) - df['low']) > (df['high'] - df['high'].shift(1)),
                np.maximum(df['low'].shift(1) - df['low'], 0),
                0
            )
            
            # Calculate +DI and -DI
            df[f'plus_di_{period}'] = 100 * df['plus_dm'].rolling(window=period).mean() / df[f'atr_{period}']
            df[f'minus_di_{period}'] = 100 * df['minus_dm'].rolling(window=period).mean() / df[f'atr_{period}']
            
            # Calculate DX
            df[f'dx_{period}'] = 100 * abs(df[f'plus_di_{period}'] - df[f'minus_di_{period}']) / (df[f'plus_di_{period}'] + df[f'minus_di_{period}'])
            
            # Calculate ADX
            df[f'adx_{period}'] = df[f'dx_{period}'].rolling(window=period).mean()
        
        # Calculate Choppiness Index
        for period in [14, 21]:
            # Calculate sum of ATR
            df[f'atr_sum_{period}'] = df['tr'].rolling(window=period).sum()
            
            # Calculate range
            df[f'range_{period}'] = df['high'].rolling(window=period).max() - df['low'].rolling(window=period).min()
            
            # Calculate Choppiness Index
            df[f'choppiness_{period}'] = 100 * np.log10(df[f'atr_sum_{period}'] / df[f'range_{period}']) / np.log10(period)
        
        # Calculate volume features
        df['volume_ma_20'] = df['volume'].rolling(window=20).mean()
        df['volume_ratio'] = df['volume'] / df['volume_ma_20']
        
        # Remove rows with NaN values
        df.dropna(inplace=True)
        
        logger.info(f"Calculated features. DataFrame has {len(df)} data points")
        
        return df
    
    def split_data(self, df: pd.DataFrame, test_size: float = 0.3) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        Split data into training and testing sets.
        
        Args:
            df: DataFrame to split
            test_size: Proportion of data to use for testing
            
        Returns:
            Tuple of (train_data, test_data)
        """
        logger.info(f"Splitting data with test_size={test_size}")
        
        # Calculate split index
        split_idx = int(len(df) * (1 - test_size))
        
        # Split data
        train_data = df.iloc[:split_idx].copy()
        test_data = df.iloc[split_idx:].copy()
        
        logger.info(f"Split data into {len(train_data)} training points and {len(test_data)} testing points")
        
        return train_data, test_data
    
    def visualize_data(self, df: pd.DataFrame) -> None:
        """
        Visualize data.
        
        Args:
            df: DataFrame to visualize
        """
        logger.info("Visualizing data")
        
        # Create directory for visualizations
        viz_dir = os.path.join(self.output_dir, "visualizations")
        os.makedirs(viz_dir, exist_ok=True)
        
        # Plot price chart
        plt.figure(figsize=(12, 6))
        plt.plot(df.index, df['close'])
        plt.title("SOL/USD Price Chart")
        plt.xlabel("Date")
        plt.ylabel("Price (USD)")
        plt.grid(True)
        plt.savefig(os.path.join(viz_dir, "price_chart.png"))
        plt.close()
        
        # Plot volume chart
        plt.figure(figsize=(12, 6))
        plt.bar(df.index, df['volume'], alpha=0.5)
        plt.title("SOL/USD Volume Chart")
        plt.xlabel("Date")
        plt.ylabel("Volume")
        plt.grid(True)
        plt.savefig(os.path.join(viz_dir, "volume_chart.png"))
        plt.close()
        
        # Plot returns distribution
        plt.figure(figsize=(10, 6))
        sns.histplot(df['returns'].dropna(), kde=True)
        plt.title("Returns Distribution")
        plt.xlabel("Returns")
        plt.ylabel("Frequency")
        plt.grid(True)
        plt.savefig(os.path.join(viz_dir, "returns_distribution.png"))
        plt.close()
        
        # Plot Bollinger Bands
        plt.figure(figsize=(12, 6))
        plt.plot(df.index, df['close'], label="Close")
        plt.plot(df.index, df['bb_upper_20_2.0'], label="Upper Band")
        plt.plot(df.index, df['bb_middle_20_2.0'], label="Middle Band")
        plt.plot(df.index, df['bb_lower_20_2.0'], label="Lower Band")
        plt.title("Bollinger Bands (20, 2.0)")
        plt.xlabel("Date")
        plt.ylabel("Price (USD)")
        plt.legend()
        plt.grid(True)
        plt.savefig(os.path.join(viz_dir, "bollinger_bands.png"))
        plt.close()
        
        # Plot RSI
        plt.figure(figsize=(12, 6))
        plt.plot(df.index, df['rsi_14'])
        plt.axhline(y=70, color='r', linestyle='-')
        plt.axhline(y=30, color='g', linestyle='-')
        plt.title("RSI (14)")
        plt.xlabel("Date")
        plt.ylabel("RSI")
        plt.grid(True)
        plt.savefig(os.path.join(viz_dir, "rsi.png"))
        plt.close()
        
        # Plot MACD
        plt.figure(figsize=(12, 6))
        plt.plot(df.index, df['macd_12_26'], label="MACD")
        plt.plot(df.index, df['macd_signal_12_26_9'], label="Signal")
        plt.bar(df.index, df['macd_histogram_12_26_9'], alpha=0.5, label="Histogram")
        plt.title("MACD (12, 26, 9)")
        plt.xlabel("Date")
        plt.ylabel("MACD")
        plt.legend()
        plt.grid(True)
        plt.savefig(os.path.join(viz_dir, "macd.png"))
        plt.close()
        
        # Plot ADX
        plt.figure(figsize=(12, 6))
        plt.plot(df.index, df['adx_14'], label="ADX")
        plt.plot(df.index, df['plus_di_14'], label="+DI")
        plt.plot(df.index, df['minus_di_14'], label="-DI")
        plt.axhline(y=25, color='r', linestyle='--')
        plt.title("ADX (14)")
        plt.xlabel("Date")
        plt.ylabel("ADX")
        plt.legend()
        plt.grid(True)
        plt.savefig(os.path.join(viz_dir, "adx.png"))
        plt.close()
        
        # Plot Choppiness Index
        plt.figure(figsize=(12, 6))
        plt.plot(df.index, df['choppiness_14'])
        plt.axhline(y=61.8, color='r', linestyle='--')
        plt.axhline(y=38.2, color='g', linestyle='--')
        plt.title("Choppiness Index (14)")
        plt.xlabel("Date")
        plt.ylabel("Choppiness Index")
        plt.grid(True)
        plt.savefig(os.path.join(viz_dir, "choppiness.png"))
        plt.close()
        
        logger.info(f"Saved visualizations to {viz_dir}")
    
    def prepare_data(self, test_size: float = 0.3, visualize: bool = True) -> Tuple[str, str]:
        """
        Prepare data for strategy optimization and backtesting.
        
        Args:
            test_size: Proportion of data to use for testing
            visualize: Whether to visualize data
            
        Returns:
            Tuple of (train_data_path, test_data_path)
        """
        logger.info("Preparing data")
        
        # Calculate features
        df = self.calculate_features()
        
        # Visualize data
        if visualize:
            self.visualize_data(df)
        
        # Split data
        train_data, test_data = self.split_data(df, test_size)
        
        # Save data
        train_path = os.path.join(self.output_dir, "train_data.csv")
        test_path = os.path.join(self.output_dir, "test_data.csv")
        
        train_data.to_csv(train_path)
        test_data.to_csv(test_path)
        
        logger.info(f"Saved training data to {train_path}")
        logger.info(f"Saved testing data to {test_path}")
        
        # Save full data
        full_path = os.path.join(self.output_dir, "full_data.csv")
        df.to_csv(full_path)
        
        logger.info(f"Saved full data to {full_path}")
        
        return train_path, test_path


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Prepare data for strategy optimization")
    parser.add_argument("--data", type=str, help="Path to raw data CSV")
    parser.add_argument("--output", type=str, default="data/prepared", help="Output directory")
    parser.add_argument("--test-size", type=float, default=0.3, help="Proportion of data to use for testing")
    parser.add_argument("--no-visualize", action="store_true", help="Disable data visualization")
    parser.add_argument("--collect", action="store_true", help="Collect data before preparation")
    parser.add_argument("--symbol", type=str, default="SOL/USDT", help="Trading symbol (for data collection)")
    parser.add_argument("--timeframe", type=str, default="1h", help="Timeframe (for data collection)")
    parser.add_argument("--start-date", type=str, default="2023-01-01", help="Start date (for data collection)")
    parser.add_argument("--end-date", type=str, default="2023-12-31", help="End date (for data collection)")
    
    args = parser.parse_args()
    
    # Create logs directory if it doesn't exist
    os.makedirs(os.path.join(parent_dir, "logs"), exist_ok=True)
    
    # Collect data if requested
    if args.collect:
        logger.info("Collecting data before preparation")
        
        # Create data collector
        collector = DataCollector(os.path.join(args.output, "raw"))
        
        # Collect data
        df = collector.collect_data(
            symbol=args.symbol,
            timeframe=args.timeframe,
            start_date=args.start_date,
            end_date=args.end_date
        )
        
        # Save data
        if not df.empty:
            data_path = collector.save_data(
                df=df,
                symbol=args.symbol,
                timeframe=args.timeframe,
                start_date=args.start_date,
                end_date=args.end_date
            )
            logger.info(f"Data collected and saved to {data_path}")
        else:
            logger.error("No data collected")
            return 1
    else:
        # Use provided data path
        if not args.data:
            logger.error("Data path must be provided when not collecting data")
            return 1
        data_path = args.data
    
    # Create data preparation
    preparation = DataPreparation(data_path, args.output)
    
    # Prepare data
    train_path, test_path = preparation.prepare_data(
        test_size=args.test_size,
        visualize=not args.no_visualize
    )
    
    logger.info("Data preparation complete")
    logger.info(f"Training data: {train_path}")
    logger.info(f"Testing data: {test_path}")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
