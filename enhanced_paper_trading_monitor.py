#!/usr/bin/env python3
"""
Enhanced Paper Trading Monitor with Real-Time Dashboard Integration.

This script runs continuous paper trading with our enhanced 4-phase strategy
and generates real-time data for dashboard monitoring.
"""

import asyncio
import json
import logging
import os
import sys
import time
from datetime import datetime, timed<PERSON>ta
from pathlib import Path
from typing import Dict, Any, List
import yaml

# Add project root to path
project_root = Path.cwd()
sys.path.append(str(project_root))

# Import enhanced components
from core.strategies.market_regime_detector import EnhancedMarketRegimeDetector
from core.strategies.probabilistic_regime import ProbabilisticRegimeDetector
from core.data.whale_signal_generator import WhaleSignalGenerator
from core.signals.whale_signal_processor import WhaleSignalProcessor
from core.risk.var_calculator import VaRCalculator
from core.risk.portfolio_risk_manager import PortfolioRiskManager
from core.risk.position_sizer import EnhancedPositionSizer
from core.analytics.strategy_attribution import StrategyAttributionTracker
from core.analytics.performance_analyzer import PerformanceAnalyzer
from core.strategies.adaptive_weight_manager import AdaptiveWeightManager
from core.strategies.strategy_selector import StrategySelector
# Simple Telegram integration
import httpx

class SimpleTelegramBot:
    """Simple Telegram bot for notifications."""

    def __init__(self):
        self.bot_token = os.environ.get('TELEGRAM_BOT_TOKEN', '')
        self.chat_id = os.environ.get('TELEGRAM_CHAT_ID', '')
        self.enabled = bool(self.bot_token and self.chat_id)

    async def send_message(self, message: str) -> bool:
        """Send message to Telegram."""
        if not self.enabled:
            logger.info(f"Telegram not configured, would send: {message}")
            return False

        try:
            async with httpx.AsyncClient() as client:
                url = f"https://api.telegram.org/bot{self.bot_token}/sendMessage"
                data = {
                    "chat_id": self.chat_id,
                    "text": message,
                    "parse_mode": "Markdown"
                }
                response = await client.post(url, json=data)
                response.raise_for_status()
                logger.info(f"Telegram message sent: {message}")
                return True
        except Exception as e:
            logger.error(f"Error sending Telegram message: {str(e)}")
            return False

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/enhanced_paper_trading.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class EnhancedPaperTradingMonitor:
    """Enhanced paper trading monitor with real-time dashboard integration."""

    def __init__(self):
        """Initialize the enhanced paper trading monitor."""
        self.config = self.load_config()
        self.is_running = False
        self.cycle_count = 0
        self.start_time = None

        # Trading metrics
        self.trading_metrics = {
            'total_cycles': 0,
            'successful_cycles': 0,
            'total_signals': 0,
            'whale_signals': 0,
            'regime_changes': 0,
            'strategy_selections': 0,
            'avg_var': 0.0,
            'avg_cvar': 0.0,
            'performance_attribution': {},
            'adaptive_weights': {},
            'current_regime': 'unknown',
            'regime_confidence': 0.0
        }

        # Initialize components
        self.initialize_components()

        # Create output directories
        self.setup_output_directories()

        logger.info("Enhanced Paper Trading Monitor initialized")

    def load_config(self) -> Dict[str, Any]:
        """Load configuration from config.yaml and environment variables."""
        try:
            # Load base config
            with open('config.yaml', 'r') as f:
                config = yaml.safe_load(f)

            # Override with environment variables
            config.setdefault('trading', {})
            config['trading']['mode'] = os.environ.get('TRADING_MODE', 'paper_trading')
            config['trading']['paper_trading_balance'] = float(os.environ.get('PAPER_TRADING_BALANCE', '1000'))

            # Enhanced features configuration
            config.setdefault('enhanced_features', {})
            config['enhanced_features']['market_regime_enabled'] = os.environ.get('MARKET_REGIME_ENABLED', 'true').lower() == 'true'
            config['enhanced_features']['whale_watching_enabled'] = os.environ.get('WHALE_WATCHING_ENABLED', 'true').lower() == 'true'
            config['enhanced_features']['var_enabled'] = os.environ.get('VAR_ENABLED', 'true').lower() == 'true'
            config['enhanced_features']['adaptive_weighting_enabled'] = os.environ.get('ADAPTIVE_WEIGHTING_ENABLED', 'true').lower() == 'true'

            return config

        except Exception as e:
            logger.error(f"Error loading configuration: {str(e)}")
            return {}

    def initialize_components(self):
        """Initialize all enhanced trading components."""
        try:
            # Phase 1: Enhanced Market Regime Detection & Whale Watching
            self.market_regime_detector = EnhancedMarketRegimeDetector()
            self.probabilistic_regime = ProbabilisticRegimeDetector()
            self.whale_signal_generator = WhaleSignalGenerator()
            self.whale_signal_processor = WhaleSignalProcessor()
            logger.info("✅ Phase 1 components initialized")

            # Phase 2: Advanced Risk Management
            self.var_calculator = VaRCalculator()
            self.portfolio_risk_manager = PortfolioRiskManager()
            self.position_sizer = EnhancedPositionSizer()
            logger.info("✅ Phase 2 components initialized")

            # Phase 3: Strategy Performance Attribution
            self.strategy_attribution = StrategyAttributionTracker()
            self.performance_analyzer = PerformanceAnalyzer()
            logger.info("✅ Phase 3 components initialized")

            # Phase 4: Adaptive Strategy Weighting
            self.adaptive_weight_manager = AdaptiveWeightManager()
            self.strategy_selector = StrategySelector()
            logger.info("✅ Phase 4 components initialized")

            # Communication
            self.telegram_bot = SimpleTelegramBot()
            logger.info("✅ Communication components initialized")

        except Exception as e:
            logger.error(f"Error initializing components: {str(e)}")
            raise

    def setup_output_directories(self):
        """Create output directories for monitoring data."""
        directories = [
            'output/paper_trading',
            'output/paper_trading/cycles',
            'output/paper_trading/metrics',
            'output/paper_trading/performance',
            'output/paper_trading/dashboard'
        ]

        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)

        logger.info("Output directories created")

    async def run_trading_cycle(self) -> Dict[str, Any]:
        """Run one complete enhanced trading cycle."""
        cycle_start = datetime.now()
        cycle_data = {
            'cycle_number': self.cycle_count + 1,
            'timestamp': cycle_start.isoformat(),
            'phase_results': {}
        }

        try:
            # Phase 1: Market Regime Detection & Whale Watching
            logger.info(f"📊 Running enhanced trading cycle {self.cycle_count + 1}")

            # Market regime detection
            regime_result = self.market_regime_detector.detect_regime()
            prob_regime_result = self.probabilistic_regime.detect_regime()

            cycle_data['phase_results']['regime_detection'] = {
                'regime': regime_result.get('regime', 'unknown'),
                'confidence': regime_result.get('confidence', 0.0),
                'probabilistic_regime': prob_regime_result
            }

            # Update trading metrics
            self.trading_metrics['current_regime'] = regime_result.get('regime', 'unknown')
            self.trading_metrics['regime_confidence'] = regime_result.get('confidence', 0.0)

            logger.info(f"📊 Detected regime: {regime_result.get('regime', 'unknown')} (confidence: {regime_result.get('confidence', 0.0):.3f})")

            # Whale signal generation
            whale_signals = await self.whale_signal_generator.generate_signals()
            processed_signals = self.whale_signal_processor.process_signals(whale_signals)

            cycle_data['phase_results']['whale_signals'] = {
                'raw_signals': len(whale_signals),
                'processed_signals': len(processed_signals),
                'signals': processed_signals
            }

            self.trading_metrics['whale_signals'] += len(whale_signals)
            logger.info(f"🐋 Generated {len(whale_signals)} whale signals")

            # Phase 2: Advanced Risk Management
            risk_metrics = self.var_calculator.calculate_portfolio_var()
            portfolio_risk = self.portfolio_risk_manager.assess_portfolio_risk()

            cycle_data['phase_results']['risk_management'] = {
                'var_metrics': risk_metrics,
                'portfolio_risk': portfolio_risk
            }

            # Update trading metrics
            if risk_metrics:
                self.trading_metrics['avg_var'] = risk_metrics.get('var_95', 0.0)
                self.trading_metrics['avg_cvar'] = risk_metrics.get('cvar_95', 0.0)

            logger.info(f"📈 Risk metrics - VaR: {risk_metrics.get('var_95', 0.0):.4f}, CVaR: {risk_metrics.get('cvar_95', 0.0):.4f}")

            # Phase 3: Strategy Performance Attribution
            attribution_data = self.strategy_attribution.update_attribution()
            performance_analysis = self.performance_analyzer.analyze_performance()

            cycle_data['phase_results']['performance_attribution'] = {
                'attribution': attribution_data,
                'performance': performance_analysis
            }

            # Phase 4: Adaptive Strategy Weighting
            current_regime = regime_result.get('regime', 'ranging')
            adaptive_weights = self.adaptive_weight_manager.update_weights(current_regime)
            selected_strategies = self.strategy_selector.select_strategies(current_regime, adaptive_weights)

            cycle_data['phase_results']['adaptive_weighting'] = {
                'weights': adaptive_weights,
                'selected_strategies': selected_strategies,
                'regime': current_regime
            }

            # Update trading metrics
            self.trading_metrics['adaptive_weights'] = adaptive_weights
            self.trading_metrics['strategy_selections'] += len(selected_strategies)

            logger.info(f"⚖️  Adaptive weights: {adaptive_weights}")
            logger.info(f"🎯 Selected {len(selected_strategies)} strategies for trading")

            # Calculate cycle duration
            cycle_end = datetime.now()
            cycle_data['duration_seconds'] = (cycle_end - cycle_start).total_seconds()
            cycle_data['status'] = 'completed'

            # Update counters
            self.cycle_count += 1
            self.trading_metrics['total_cycles'] += 1
            self.trading_metrics['successful_cycles'] += 1

            logger.info(f"✅ Cycle {self.cycle_count} complete - Regime: {current_regime}, Strategies: {len(selected_strategies)}, VaR: {risk_metrics.get('var_95', 0.0):.4f}")

            return cycle_data

        except Exception as e:
            logger.error(f"Error in trading cycle: {str(e)}")
            cycle_data['status'] = 'error'
            cycle_data['error'] = str(e)
            return cycle_data

    def save_cycle_data(self, cycle_data: Dict[str, Any]):
        """Save cycle data for dashboard consumption."""
        try:
            # Save individual cycle data
            cycle_file = f"output/paper_trading/cycles/cycle_{cycle_data['cycle_number']:04d}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(cycle_file, 'w') as f:
                json.dump(cycle_data, f, indent=2, default=str)

            # Save latest cycle data for dashboard
            latest_file = "output/paper_trading/dashboard/latest_cycle.json"
            with open(latest_file, 'w') as f:
                json.dump(cycle_data, f, indent=2, default=str)

            # Save trading metrics summary
            metrics_file = "output/paper_trading/dashboard/trading_metrics.json"
            metrics_data = {
                'timestamp': datetime.now().isoformat(),
                'session_start': self.start_time.isoformat() if self.start_time else None,
                'session_duration_minutes': (datetime.now() - self.start_time).total_seconds() / 60 if self.start_time else 0,
                'metrics': self.trading_metrics
            }

            with open(metrics_file, 'w') as f:
                json.dump(metrics_data, f, indent=2, default=str)

            logger.debug(f"Cycle data saved: {cycle_file}")

        except Exception as e:
            logger.error(f"Error saving cycle data: {str(e)}")

    async def run_continuous_monitoring(self, cycle_interval_minutes: int = 5):
        """Run continuous paper trading monitoring."""
        try:
            self.is_running = True
            self.start_time = datetime.now()

            logger.info("🚀 Starting enhanced paper trading monitoring...")
            logger.info(f"📊 Cycle interval: {cycle_interval_minutes} minutes")

            # Send startup notification
            await self.telegram_bot.send_message(
                f"🚀 Enhanced Paper Trading Monitor Started\n"
                f"📊 Cycle interval: {cycle_interval_minutes} minutes\n"
                f"🕐 Started at: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}"
            )

            while self.is_running:
                # Run trading cycle
                cycle_data = await self.run_trading_cycle()

                # Save data for dashboard
                self.save_cycle_data(cycle_data)

                # Wait for next cycle
                if self.is_running:
                    logger.info(f"⏳ Waiting {cycle_interval_minutes} minutes before next cycle...")
                    await asyncio.sleep(cycle_interval_minutes * 60)

        except KeyboardInterrupt:
            logger.info("Monitoring stopped by user")
        except Exception as e:
            logger.error(f"Error in continuous monitoring: {str(e)}")
        finally:
            self.is_running = False

            # Send completion notification
            if self.start_time:
                duration = datetime.now() - self.start_time
                await self.telegram_bot.send_message(
                    f"🏁 Enhanced Paper Trading Monitor Stopped\n"
                    f"📊 Total cycles: {self.trading_metrics['total_cycles']}\n"
                    f"⏱️ Duration: {duration}\n"
                    f"🕐 Ended at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                )

    def stop_monitoring(self):
        """Stop the monitoring process."""
        self.is_running = False
        logger.info("Monitoring stop requested")

async def main():
    """Main function to run enhanced paper trading monitoring."""
    import argparse

    parser = argparse.ArgumentParser(description='Enhanced Paper Trading Monitor')
    parser.add_argument('--interval', type=int, default=5, help='Cycle interval in minutes (default: 5)')
    parser.add_argument('--duration', type=int, help='Total duration in minutes (optional)')

    args = parser.parse_args()

    # Create and start monitor
    monitor = EnhancedPaperTradingMonitor()

    try:
        if args.duration:
            logger.info(f"Running for {args.duration} minutes")
            # Run for specified duration
            monitoring_task = asyncio.create_task(monitor.run_continuous_monitoring(args.interval))
            await asyncio.sleep(args.duration * 60)
            monitor.stop_monitoring()
            await monitoring_task
        else:
            logger.info("Running continuously (Ctrl+C to stop)")
            await monitor.run_continuous_monitoring(args.interval)

    except KeyboardInterrupt:
        logger.info("Stopping monitoring...")
        monitor.stop_monitoring()

if __name__ == "__main__":
    asyncio.run(main())
