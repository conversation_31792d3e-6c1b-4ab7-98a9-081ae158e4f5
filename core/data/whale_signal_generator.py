"""
Enhanced Whale Signal Generator for Synergy7 Trading System.

This module implements whale watching functionality with signal generation capabilities,
confidence scoring, and integration with the trading strategy pipeline.
"""

import logging
import asyncio
import httpx
import json
import os
import numpy as np
import pandas as pd
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from collections import defaultdict, deque

# Configure logging
logger = logging.getLogger(__name__)

class WhaleSignalGenerator:
    """
    Enhanced whale signal generator that tracks whale activities and generates trading signals.

    This class monitors whale transactions, analyzes patterns, and generates confidence-scored
    trading signals based on whale behavior.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the whale signal generator.

        Args:
            config: Configuration dictionary with whale_watching section
        """
        # Get whale watching configuration
        whale_config = config.get("whale_watching", {})

        # Basic configuration
        self.enabled = whale_config.get("enabled", True)
        self.min_transaction_threshold_usd = whale_config.get("min_transaction_threshold_usd", 100000)
        self.whale_confidence_weight = whale_config.get("whale_confidence_weight", 0.3)
        self.whale_signal_decay_hours = whale_config.get("whale_signal_decay_hours", 6)
        self.whale_discovery_interval = whale_config.get("whale_discovery_interval", 3600)
        self.whale_activity_lookback_hours = whale_config.get("whale_activity_lookback_hours", 24)

        # Signal filters
        signal_filters = whale_config.get("whale_signal_filters", {})
        self.min_whale_count = signal_filters.get("min_whale_count", 3)
        self.min_transaction_volume = signal_filters.get("min_transaction_volume", 500000)

        # API configuration
        api_config = config.get("apis", {}).get("helius", {})
        self.api_key = api_config.get("api_key", "")
        self.base_url = f"https://mainnet.helius-rpc.com/?api-key={self.api_key}"

        # HTTP client
        self.http_client = httpx.AsyncClient(timeout=30.0)

        # Whale data
        self.known_whales = self._load_known_whales()
        self.whale_transactions = deque(maxlen=1000)  # Store recent transactions
        self.whale_signals = {}  # Current whale signals by token
        self.whale_activity_scores = defaultdict(float)  # Activity scores by token

        # Signal history
        self.signal_history = deque(maxlen=500)
        self.last_discovery_time = None

        logger.info(f"Initialized whale signal generator with {len(self.known_whales)} known whales")

    def _load_known_whales(self, file_path: str = None) -> set:
        """
        Load known whale addresses from file.

        Args:
            file_path: Path to the known whales file

        Returns:
            Set of known whale addresses
        """
        if file_path is None:
            file_path = os.path.join("data", "known_whales.json")

        try:
            if os.path.exists(file_path):
                with open(file_path, 'r') as f:
                    data = json.load(f)
                    whales = set(data.get('whales', []))
                    logger.info(f"Loaded {len(whales)} known whale addresses")
                    return whales
        except Exception as e:
            logger.warning(f"Failed to load known whales: {str(e)}")

        # Return default whale addresses if file doesn't exist
        return {
            "9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM",  # Example whale address
            "5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1",  # Example whale address
            "7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU"   # Example whale address
        }

    def _save_known_whales(self, file_path: str = None) -> None:
        """
        Save known whale addresses to file.

        Args:
            file_path: Path to save the known whales file
        """
        if file_path is None:
            file_path = os.path.join("data", "known_whales.json")

        try:
            # Ensure directory exists
            os.makedirs(os.path.dirname(file_path), exist_ok=True)

            with open(file_path, 'w') as f:
                json.dump({'whales': list(self.known_whales)}, f, indent=2)
            logger.debug(f"Saved {len(self.known_whales)} known whale addresses")
        except Exception as e:
            logger.error(f"Failed to save known whales: {str(e)}")

    async def discover_new_whales(self, min_balance: float = 1000000.0) -> List[str]:
        """
        Discover new whale addresses based on token balances.

        Args:
            min_balance: Minimum balance in USD to be considered a whale

        Returns:
            List of new whale addresses
        """
        if not self.enabled or not self.api_key:
            return []

        try:
            # Use USDC as an example token to find whales
            usdc_mint = "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"

            payload = {
                "jsonrpc": "2.0",
                "id": "whale-discovery",
                "method": "getTokenLargestAccounts",
                "params": [usdc_mint]
            }

            response = await self.http_client.post(self.base_url, json=payload)
            response.raise_for_status()
            data = response.json()

            new_whales = []
            if 'result' in data and 'value' in data['result']:
                for account in data['result']['value'][:20]:  # Top 20 accounts
                    try:
                        # Get account owner
                        account_payload = {
                            "jsonrpc": "2.0",
                            "id": "account-info",
                            "method": "getAccountInfo",
                            "params": [account.get('address'), {"encoding": "jsonParsed"}]
                        }

                        account_response = await self.http_client.post(self.base_url, json=account_payload)
                        account_response.raise_for_status()
                        account_data = account_response.json()

                        if 'result' in account_data and account_data['result'] and 'value' in account_data['result']:
                            owner = account_data['result']['value'].get('owner')
                            if owner and owner not in self.known_whales:
                                self.known_whales.add(owner)
                                new_whales.append(owner)
                    except Exception as e:
                        logger.warning(f"Error processing account {account.get('address')}: {str(e)}")
                        continue

            # Save updated known whales
            self._save_known_whales()
            self.last_discovery_time = datetime.now()

            logger.info(f"Discovered {len(new_whales)} new whale addresses")
            return new_whales

        except Exception as e:
            logger.error(f"Failed to discover new whales: {str(e)}")
            return []

    async def get_whale_transactions(self, hours_lookback: int = None) -> List[Dict[str, Any]]:
        """
        Get recent whale transactions.

        Args:
            hours_lookback: Hours to look back for transactions

        Returns:
            List of whale transaction data
        """
        if hours_lookback is None:
            hours_lookback = self.whale_activity_lookback_hours

        if not self.enabled or not self.api_key:
            return self._get_mock_whale_transactions()

        try:
            whale_txs = []
            cutoff_time = datetime.now() - timedelta(hours=hours_lookback)

            for whale_address in list(self.known_whales)[:10]:  # Limit to first 10 whales
                try:
                    payload = {
                        "jsonrpc": "2.0",
                        "id": "whale-transactions",
                        "method": "getSignaturesForAddress",
                        "params": [
                            whale_address,
                            {
                                "limit": 10,
                                "commitment": "confirmed"
                            }
                        ]
                    }

                    response = await self.http_client.post(self.base_url, json=payload)
                    response.raise_for_status()
                    data = response.json()

                    if 'result' in data:
                        for sig_info in data['result']:
                            if sig_info.get('blockTime'):
                                tx_time = datetime.fromtimestamp(sig_info['blockTime'])
                                if tx_time > cutoff_time:
                                    whale_txs.append({
                                        'signature': sig_info.get('signature'),
                                        'whale_address': whale_address,
                                        'timestamp': tx_time.isoformat(),
                                        'block_time': sig_info.get('blockTime')
                                    })

                except Exception as e:
                    logger.warning(f"Error getting transactions for whale {whale_address}: {str(e)}")
                    continue

            return whale_txs

        except Exception as e:
            logger.error(f"Failed to get whale transactions: {str(e)}")
            return self._get_mock_whale_transactions()

    def _get_mock_whale_transactions(self) -> List[Dict[str, Any]]:
        """
        Get mock whale transactions for testing.

        Returns:
            List of mock whale transaction data
        """
        mock_transactions = []
        current_time = datetime.now()

        # Generate some mock transactions
        for i in range(5):
            mock_transactions.append({
                'signature': f'mock_signature_{i}',
                'whale_address': f'mock_whale_{i}',
                'timestamp': (current_time - timedelta(hours=i)).isoformat(),
                'token_address': 'So11111111111111111111111111111111111111112',  # SOL
                'amount': 1000 + i * 500,
                'value_usd': 25000 + i * 12500,
                'transaction_type': 'buy' if i % 2 == 0 else 'sell'
            })

        return mock_transactions

    def calculate_whale_activity_score(self, token_address: str, transactions: List[Dict[str, Any]]) -> float:
        """
        Calculate whale activity score for a token.

        Args:
            token_address: Token address to analyze
            transactions: List of whale transactions

        Returns:
            Activity score (0.0 to 1.0)
        """
        try:
            # Filter transactions for this token
            token_txs = [tx for tx in transactions if tx.get('token_address') == token_address]

            if not token_txs:
                return 0.0

            # Calculate metrics
            total_volume = sum(tx.get('value_usd', 0) for tx in token_txs)
            unique_whales = len(set(tx.get('whale_address') for tx in token_txs))
            buy_volume = sum(tx.get('value_usd', 0) for tx in token_txs if tx.get('transaction_type') == 'buy')
            sell_volume = sum(tx.get('value_usd', 0) for tx in token_txs if tx.get('transaction_type') == 'sell')

            # Calculate score components
            volume_score = min(1.0, total_volume / self.min_transaction_volume)
            whale_count_score = min(1.0, unique_whales / self.min_whale_count)

            # Net buying pressure
            net_buying = (buy_volume - sell_volume) / max(total_volume, 1)
            buying_pressure_score = max(0.0, net_buying)

            # Recency score (more recent transactions get higher score)
            current_time = datetime.now()
            recency_scores = []
            for tx in token_txs:
                try:
                    tx_time = datetime.fromisoformat(tx['timestamp'].replace('Z', '+00:00'))
                    hours_ago = (current_time - tx_time).total_seconds() / 3600
                    recency_score = max(0.0, 1.0 - (hours_ago / self.whale_signal_decay_hours))
                    recency_scores.append(recency_score)
                except:
                    recency_scores.append(0.5)  # Default score

            avg_recency_score = np.mean(recency_scores) if recency_scores else 0.0

            # Combine scores
            activity_score = (
                volume_score * 0.3 +
                whale_count_score * 0.3 +
                buying_pressure_score * 0.2 +
                avg_recency_score * 0.2
            )

            return min(1.0, activity_score)

        except Exception as e:
            logger.warning(f"Error calculating whale activity score: {str(e)}")
            return 0.0

    def generate_whale_signals(self, transactions: List[Dict[str, Any]]) -> Dict[str, Dict[str, Any]]:
        """
        Generate whale-based trading signals.

        Args:
            transactions: List of whale transactions

        Returns:
            Dictionary of signals by token address
        """
        signals = {}

        try:
            # Group transactions by token
            token_transactions = defaultdict(list)
            for tx in transactions:
                token_addr = tx.get('token_address')
                if token_addr:
                    token_transactions[token_addr].append(tx)

            # Generate signals for each token
            for token_address, token_txs in token_transactions.items():
                activity_score = self.calculate_whale_activity_score(token_address, token_txs)

                if activity_score > 0.1:  # Minimum threshold for signal generation
                    # Calculate signal strength and direction
                    buy_volume = sum(tx.get('value_usd', 0) for tx in token_txs if tx.get('transaction_type') == 'buy')
                    sell_volume = sum(tx.get('value_usd', 0) for tx in token_txs if tx.get('transaction_type') == 'sell')
                    total_volume = buy_volume + sell_volume

                    if total_volume > 0:
                        net_buying_ratio = (buy_volume - sell_volume) / total_volume

                        # Determine signal direction
                        if net_buying_ratio > 0.2:  # Net buying
                            signal_direction = 1  # Buy signal
                        elif net_buying_ratio < -0.2:  # Net selling
                            signal_direction = -1  # Sell signal
                        else:
                            signal_direction = 0  # Neutral

                        # Calculate confidence
                        confidence = activity_score * abs(net_buying_ratio) * self.whale_confidence_weight
                        confidence = min(1.0, confidence)

                        # Create signal
                        signal = {
                            'token_address': token_address,
                            'signal_direction': signal_direction,
                            'signal_strength': activity_score,
                            'confidence': confidence,
                            'whale_count': len(set(tx.get('whale_address') for tx in token_txs)),
                            'total_volume_usd': total_volume,
                            'net_buying_ratio': net_buying_ratio,
                            'timestamp': datetime.now().isoformat(),
                            'decay_time': (datetime.now() + timedelta(hours=self.whale_signal_decay_hours)).isoformat()
                        }

                        signals[token_address] = signal

                        # Update activity scores
                        self.whale_activity_scores[token_address] = activity_score

            # Store signals
            self.whale_signals.update(signals)

            # Add to signal history
            if signals:
                self.signal_history.append({
                    'timestamp': datetime.now().isoformat(),
                    'signals': signals.copy()
                })

            return signals

        except Exception as e:
            logger.error(f"Error generating whale signals: {str(e)}")
            return {}

    def get_current_whale_signal(self, token_address: str) -> Optional[Dict[str, Any]]:
        """
        Get current whale signal for a specific token.

        Args:
            token_address: Token address to get signal for

        Returns:
            Current whale signal or None if no signal exists
        """
        signal = self.whale_signals.get(token_address)

        if signal:
            # Check if signal has expired
            try:
                decay_time = datetime.fromisoformat(signal['decay_time'].replace('Z', '+00:00'))
                if datetime.now() > decay_time:
                    # Signal has expired, remove it
                    del self.whale_signals[token_address]
                    return None
            except:
                # If we can't parse decay time, assume signal is still valid
                pass

        return signal

    async def update_whale_signals(self) -> Dict[str, Dict[str, Any]]:
        """
        Update whale signals by fetching recent transactions and analyzing them.

        Returns:
            Dictionary of updated signals
        """
        try:
            # Discover new whales periodically
            if (self.last_discovery_time is None or
                (datetime.now() - self.last_discovery_time).total_seconds() > self.whale_discovery_interval):
                await self.discover_new_whales()

            # Get recent whale transactions
            transactions = await self.get_whale_transactions()

            # Generate signals
            signals = self.generate_whale_signals(transactions)

            logger.debug(f"Updated whale signals for {len(signals)} tokens")
            return signals

        except Exception as e:
            logger.error(f"Error updating whale signals: {str(e)}")
            return {}

    def get_signal_summary(self) -> Dict[str, Any]:
        """
        Get summary of current whale signals.

        Returns:
            Dictionary with signal summary
        """
        try:
            active_signals = len(self.whale_signals)
            total_whales = len(self.known_whales)

            # Calculate average confidence
            confidences = [signal.get('confidence', 0.0) for signal in self.whale_signals.values()]
            avg_confidence = np.mean(confidences) if confidences else 0.0

            # Count signals by direction
            buy_signals = sum(1 for signal in self.whale_signals.values() if signal.get('signal_direction', 0) > 0)
            sell_signals = sum(1 for signal in self.whale_signals.values() if signal.get('signal_direction', 0) < 0)
            neutral_signals = active_signals - buy_signals - sell_signals

            return {
                'enabled': self.enabled,
                'active_signals': active_signals,
                'total_whales': total_whales,
                'average_confidence': avg_confidence,
                'buy_signals': buy_signals,
                'sell_signals': sell_signals,
                'neutral_signals': neutral_signals,
                'last_update': datetime.now().isoformat(),
                'signal_history_length': len(self.signal_history)
            }

        except Exception as e:
            logger.error(f"Error getting signal summary: {str(e)}")
            return {'enabled': self.enabled, 'error': str(e)}

    async def close(self):
        """Close the HTTP client session."""
        try:
            await self.http_client.aclose()
        except Exception as e:
            logger.warning(f"Error closing HTTP client: {str(e)}")
