#!/usr/bin/env python3
"""
Momentum Strategy Optimizer for Synergy7 Trading System

This module provides optimization functionality for the momentum strategy,
including parameter tuning, walk-forward optimization, and performance evaluation.
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Union, Tuple
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import ParameterGrid
import json
import os

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("momentum_optimizer")

class MomentumOptimizer:
    """
    Optimizer for the momentum strategy.
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize the momentum optimizer.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config or {}
        
        # Default parameters
        self.window_size_range = self.config.get("window_size_range", [5, 10, 15, 20, 25, 30])
        self.threshold_range = self.config.get("threshold_range", [0.005, 0.01, 0.015, 0.02, 0.025, 0.03])
        self.smoothing_factor_range = self.config.get("smoothing_factor_range", [0.1, 0.2, 0.3, 0.4, 0.5])
        self.max_value_range = self.config.get("max_value_range", [0.05, 0.1, 0.15, 0.2, 0.25])
        
        # Optimization settings
        self.train_test_split = self.config.get("train_test_split", 0.7)
        self.walk_forward_window = self.config.get("walk_forward_window", 30)  # days
        self.walk_forward_step = self.config.get("walk_forward_step", 7)  # days
        self.min_trades = self.config.get("min_trades", 10)
        self.metrics = self.config.get("metrics", ["sharpe_ratio", "profit_factor", "win_rate", "max_drawdown"])
        
        # Output settings
        self.output_dir = self.config.get("output_dir", "output/momentum_optimizer")
        os.makedirs(self.output_dir, exist_ok=True)
        
        logger.info("Initialized MomentumOptimizer")
    
    def calculate_momentum(self,
                          price_data: pd.DataFrame,
                          window_size: int,
                          threshold: float,
                          smoothing_factor: float,
                          max_value: float) -> pd.DataFrame:
        """
        Calculate momentum signals for a given set of parameters.
        
        Args:
            price_data: DataFrame containing price data with 'close' column
            window_size: Window size for momentum calculation
            threshold: Threshold for signal generation
            smoothing_factor: Smoothing factor for momentum calculation
            max_value: Maximum momentum value
            
        Returns:
            DataFrame with momentum signals
        """
        # Calculate returns
        price_data = price_data.copy()
        price_data["returns"] = price_data["close"].pct_change()
        
        # Calculate momentum
        price_data["momentum"] = price_data["returns"].rolling(window=window_size).mean()
        
        # Apply smoothing
        price_data["momentum_smooth"] = price_data["momentum"].ewm(alpha=smoothing_factor).mean()
        
        # Apply max value cap
        price_data["momentum_capped"] = price_data["momentum_smooth"].clip(-max_value, max_value)
        
        # Generate signals
        price_data["signal"] = 0
        price_data.loc[price_data["momentum_capped"] > threshold, "signal"] = 1
        price_data.loc[price_data["momentum_capped"] < -threshold, "signal"] = -1
        
        # Generate entry/exit points
        price_data["position"] = price_data["signal"].shift(1)
        price_data["entry_exit"] = price_data["position"].diff()
        
        return price_data
    
    def calculate_performance(self, signals: pd.DataFrame) -> Dict[str, float]:
        """
        Calculate performance metrics for a set of signals.
        
        Args:
            signals: DataFrame containing signals and price data
            
        Returns:
            Dictionary of performance metrics
        """
        # Calculate strategy returns
        signals["strategy_returns"] = signals["returns"] * signals["position"]
        
        # Calculate cumulative returns
        signals["cumulative_returns"] = (1 + signals["strategy_returns"]).cumprod()
        
        # Calculate drawdown
        signals["drawdown"] = 1 - signals["cumulative_returns"] / signals["cumulative_returns"].cummax()
        
        # Calculate trade statistics
        trades = signals[signals["entry_exit"] != 0].copy()
        trades["trade_type"] = trades["entry_exit"].apply(lambda x: "entry" if x > 0 else "exit")
        
        # Ensure we have enough trades
        if len(trades) < self.min_trades:
            return {
                "sharpe_ratio": -999,
                "profit_factor": 0,
                "win_rate": 0,
                "max_drawdown": 1,
                "total_return": -1,
                "num_trades": len(trades) // 2,
                "avg_trade_return": 0
            }
        
        # Calculate Sharpe ratio
        sharpe_ratio = np.sqrt(252) * signals["strategy_returns"].mean() / signals["strategy_returns"].std() if signals["strategy_returns"].std() > 0 else 0
        
        # Calculate profit factor
        winning_trades = signals[signals["strategy_returns"] > 0]["strategy_returns"].sum()
        losing_trades = abs(signals[signals["strategy_returns"] < 0]["strategy_returns"].sum())
        profit_factor = winning_trades / losing_trades if losing_trades > 0 else winning_trades
        
        # Calculate win rate
        win_count = len(signals[signals["strategy_returns"] > 0])
        total_trades = len(signals[signals["strategy_returns"] != 0])
        win_rate = win_count / total_trades if total_trades > 0 else 0
        
        # Calculate max drawdown
        max_drawdown = signals["drawdown"].max()
        
        # Calculate total return
        total_return = signals["cumulative_returns"].iloc[-1] - 1
        
        # Calculate average trade return
        avg_trade_return = signals["strategy_returns"].mean() * 100  # as percentage
        
        return {
            "sharpe_ratio": sharpe_ratio,
            "profit_factor": profit_factor,
            "win_rate": win_rate,
            "max_drawdown": max_drawdown,
            "total_return": total_return,
            "num_trades": total_trades // 2,  # divide by 2 to count round trips
            "avg_trade_return": avg_trade_return
        }
    
    def optimize_parameters(self, price_data: pd.DataFrame) -> Dict[str, Any]:
        """
        Optimize momentum strategy parameters using grid search.
        
        Args:
            price_data: DataFrame containing price data with 'close' column
            
        Returns:
            Dictionary containing optimal parameters and performance metrics
        """
        # Define parameter grid
        param_grid = {
            "window_size": self.window_size_range,
            "threshold": self.threshold_range,
            "smoothing_factor": self.smoothing_factor_range,
            "max_value": self.max_value_range
        }
        
        # Split data into train and test sets
        split_idx = int(len(price_data) * self.train_test_split)
        train_data = price_data.iloc[:split_idx].copy()
        test_data = price_data.iloc[split_idx:].copy()
        
        logger.info(f"Optimizing parameters using {len(train_data)} training samples and {len(test_data)} test samples")
        
        # Initialize results
        results = []
        
        # Grid search
        grid = ParameterGrid(param_grid)
        total_combinations = len(grid)
        
        logger.info(f"Testing {total_combinations} parameter combinations")
        
        for i, params in enumerate(grid):
            # Calculate signals on training data
            signals = self.calculate_momentum(
                train_data,
                window_size=params["window_size"],
                threshold=params["threshold"],
                smoothing_factor=params["smoothing_factor"],
                max_value=params["max_value"]
            )
            
            # Calculate performance metrics
            performance = self.calculate_performance(signals)
            
            # Store results
            results.append({
                "params": params,
                "train_performance": performance
            })
            
            if (i + 1) % 10 == 0:
                logger.info(f"Processed {i + 1}/{total_combinations} parameter combinations")
        
        # Sort results by Sharpe ratio
        results.sort(key=lambda x: x["train_performance"]["sharpe_ratio"], reverse=True)
        
        # Test top 5 parameter sets on test data
        top_results = results[:5]
        
        for result in top_results:
            params = result["params"]
            
            # Calculate signals on test data
            signals = self.calculate_momentum(
                test_data,
                window_size=params["window_size"],
                threshold=params["threshold"],
                smoothing_factor=params["smoothing_factor"],
                max_value=params["max_value"]
            )
            
            # Calculate performance metrics
            performance = self.calculate_performance(signals)
            
            # Store test performance
            result["test_performance"] = performance
        
        # Sort by test Sharpe ratio
        top_results.sort(key=lambda x: x["test_performance"]["sharpe_ratio"], reverse=True)
        
        # Get best parameters
        best_result = top_results[0]
        best_params = best_result["params"]
        
        logger.info(f"Best parameters: {best_params}")
        logger.info(f"Train performance: {best_result['train_performance']}")
        logger.info(f"Test performance: {best_result['test_performance']}")
        
        # Save results
        self.save_optimization_results(top_results)
        
        return {
            "best_params": best_params,
            "train_performance": best_result["train_performance"],
            "test_performance": best_result["test_performance"],
            "all_results": results
        }
    
    def walk_forward_optimization(self, price_data: pd.DataFrame) -> Dict[str, Any]:
        """
        Perform walk-forward optimization of momentum strategy parameters.
        
        Args:
            price_data: DataFrame containing price data with 'close' column
            
        Returns:
            Dictionary containing optimal parameters for each period
        """
        # Ensure datetime index
        if not isinstance(price_data.index, pd.DatetimeIndex):
            logger.warning("Price data index is not DatetimeIndex, attempting to convert")
            try:
                price_data = price_data.set_index(pd.to_datetime(price_data.index))
            except:
                logger.error("Failed to convert index to DatetimeIndex")
                return {"error": "Price data index must be DatetimeIndex"}
        
        # Initialize results
        wfo_results = []
        
        # Define walk-forward windows
        start_date = price_data.index[0]
        end_date = price_data.index[-1]
        
        current_date = start_date
        while current_date + timedelta(days=self.walk_forward_window) <= end_date:
            # Define train and test periods
            train_end = current_date + timedelta(days=int(self.walk_forward_window * self.train_test_split))
            test_end = current_date + timedelta(days=self.walk_forward_window)
            
            train_data = price_data.loc[current_date:train_end].copy()
            test_data = price_data.loc[train_end:test_end].copy()
            
            logger.info(f"Walk-forward window: {current_date.date()} to {test_end.date()}")
            logger.info(f"Train period: {current_date.date()} to {train_end.date()}")
            logger.info(f"Test period: {train_end.date()} to {test_end.date()}")
            
            # Skip if not enough data
            if len(train_data) < 30 or len(test_data) < 10:
                logger.warning(f"Insufficient data for period {current_date.date()} to {test_end.date()}")
                current_date += timedelta(days=self.walk_forward_step)
                continue
            
            # Optimize parameters on training data
            optimization_result = self.optimize_parameters(train_data)
            
            # Store results
            wfo_results.append({
                "period": {
                    "start": current_date,
                    "end": test_end
                },
                "best_params": optimization_result["best_params"],
                "train_performance": optimization_result["train_performance"],
                "test_performance": optimization_result["test_performance"]
            })
            
            # Move to next period
            current_date += timedelta(days=self.walk_forward_step)
        
        # Save walk-forward optimization results
        self.save_wfo_results(wfo_results)
        
        return {
            "wfo_results": wfo_results
        }
    
    def save_optimization_results(self, results: List[Dict[str, Any]]) -> None:
        """
        Save optimization results to file.
        
        Args:
            results: List of optimization results
        """
        output_file = os.path.join(self.output_dir, f"optimization_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        
        with open(output_file, "w") as f:
            json.dump(results, f, indent=2, default=str)
        
        logger.info(f"Saved optimization results to {output_file}")
    
    def save_wfo_results(self, results: List[Dict[str, Any]]) -> None:
        """
        Save walk-forward optimization results to file.
        
        Args:
            results: List of walk-forward optimization results
        """
        output_file = os.path.join(self.output_dir, f"wfo_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        
        with open(output_file, "w") as f:
            json.dump(results, f, indent=2, default=str)
        
        logger.info(f"Saved walk-forward optimization results to {output_file}")
    
    def plot_optimization_results(self, results: List[Dict[str, Any]]) -> None:
        """
        Plot optimization results.
        
        Args:
            results: List of optimization results
        """
        # Extract parameters and performance metrics
        params = [r["params"] for r in results]
        train_performance = [r["train_performance"] for r in results]
        
        # Create DataFrame
        df = pd.DataFrame({
            "window_size": [p["window_size"] for p in params],
            "threshold": [p["threshold"] for p in params],
            "smoothing_factor": [p["smoothing_factor"] for p in params],
            "max_value": [p["max_value"] for p in params],
            "sharpe_ratio": [p["sharpe_ratio"] for p in train_performance],
            "profit_factor": [p["profit_factor"] for p in train_performance],
            "win_rate": [p["win_rate"] for p in train_performance],
            "max_drawdown": [p["max_drawdown"] for p in train_performance],
            "total_return": [p["total_return"] for p in train_performance]
        })
        
        # Plot parameter distributions
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        
        sns.boxplot(x="window_size", y="sharpe_ratio", data=df, ax=axes[0, 0])
        axes[0, 0].set_title("Sharpe Ratio by Window Size")
        
        sns.boxplot(x="threshold", y="sharpe_ratio", data=df, ax=axes[0, 1])
        axes[0, 1].set_title("Sharpe Ratio by Threshold")
        
        sns.boxplot(x="smoothing_factor", y="sharpe_ratio", data=df, ax=axes[1, 0])
        axes[1, 0].set_title("Sharpe Ratio by Smoothing Factor")
        
        sns.boxplot(x="max_value", y="sharpe_ratio", data=df, ax=axes[1, 1])
        axes[1, 1].set_title("Sharpe Ratio by Max Value")
        
        plt.tight_layout()
        
        # Save plot
        output_file = os.path.join(self.output_dir, f"optimization_plot_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png")
        plt.savefig(output_file)
        
        logger.info(f"Saved optimization plot to {output_file}")
        
        plt.close()
