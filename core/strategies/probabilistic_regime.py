"""
Probabilistic Market Regime Detection using Hidden Markov Models.

This module implements advanced probabilistic regime detection using Hidden Markov Models
and other machine learning techniques for more accurate regime identification.
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from sklearn.mixture import GaussianMixture
from sklearn.preprocessing import StandardScaler
import warnings

# Suppress sklearn warnings
warnings.filterwarnings('ignore', category=UserWarning)

# Configure logging
logger = logging.getLogger(__name__)

class ProbabilisticRegimeDetector:
    """
    Probabilistic regime detector using Hidden Markov Models and Gaussian Mixture Models.
    
    This class provides advanced regime detection capabilities with probabilistic outputs
    and confidence scoring for regime transitions.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the probabilistic regime detector.
        
        Args:
            config: Configuration dictionary with market_regime.ml_models section
        """
        # Get ML models configuration
        regime_config = config.get("market_regime", {})
        ml_config = regime_config.get("ml_models", {})
        
        # Basic configuration
        self.enabled = ml_config.get("hmm_enabled", True)
        self.n_states = ml_config.get("hmm_states", 4)
        self.lookback_days = ml_config.get("hmm_lookback_days", 30)
        
        # Model parameters
        self.min_training_samples = max(50, self.n_states * 10)
        self.retrain_interval_hours = ml_config.get("retrain_interval_hours", 24)
        self.confidence_threshold = regime_config.get("regime_confidence_threshold", 0.7)
        
        # Models
        self.gmm_model = None
        self.scaler = StandardScaler()
        self.last_training_time = None
        self.is_trained = False
        
        # Feature configuration
        self.features = [
            'returns', 'volatility', 'volume_ratio', 'rsi', 'bb_position', 
            'adx', 'momentum', 'mean_reversion_signal'
        ]
        
        # Historical data
        self.feature_history = []
        self.regime_history = []
        self.confidence_history = []
        
        logger.info(f"Initialized probabilistic regime detector with {self.n_states} states")
    
    def _calculate_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate features for regime detection.
        
        Args:
            df: DataFrame with OHLCV data
            
        Returns:
            DataFrame with calculated features
        """
        try:
            # Make a copy of the data
            features_df = df.copy()
            
            # Returns and volatility
            features_df['returns'] = features_df['close'].pct_change()
            features_df['volatility'] = features_df['returns'].rolling(window=20).std()
            
            # Volume features
            features_df['volume_ma'] = features_df['volume'].rolling(window=20).mean()
            features_df['volume_ratio'] = features_df['volume'] / features_df['volume_ma']
            
            # RSI
            delta = features_df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            features_df['rsi'] = 100 - (100 / (1 + rs))
            
            # Bollinger Bands position
            bb_ma = features_df['close'].rolling(window=20).mean()
            bb_std = features_df['close'].rolling(window=20).std()
            bb_upper = bb_ma + (bb_std * 2)
            bb_lower = bb_ma - (bb_std * 2)
            features_df['bb_position'] = (features_df['close'] - bb_lower) / (bb_upper - bb_lower)
            
            # ADX calculation (simplified)
            high_low = features_df['high'] - features_df['low']
            high_close = np.abs(features_df['high'] - features_df['close'].shift())
            low_close = np.abs(features_df['low'] - features_df['close'].shift())
            tr = np.maximum(high_low, np.maximum(high_close, low_close))
            
            plus_dm = np.where((features_df['high'] - features_df['high'].shift()) > 
                              (features_df['low'].shift() - features_df['low']), 
                              np.maximum(features_df['high'] - features_df['high'].shift(), 0), 0)
            minus_dm = np.where((features_df['low'].shift() - features_df['low']) > 
                               (features_df['high'] - features_df['high'].shift()), 
                               np.maximum(features_df['low'].shift() - features_df['low'], 0), 0)
            
            tr_smooth = pd.Series(tr).rolling(window=14).mean()
            plus_di = 100 * (pd.Series(plus_dm).rolling(window=14).mean() / tr_smooth)
            minus_di = 100 * (pd.Series(minus_dm).rolling(window=14).mean() / tr_smooth)
            dx = 100 * np.abs(plus_di - minus_di) / (plus_di + minus_di)
            features_df['adx'] = dx.rolling(window=14).mean()
            
            # Momentum
            features_df['momentum'] = features_df['close'] / features_df['close'].shift(10) - 1
            
            # Mean reversion signal
            sma_20 = features_df['close'].rolling(window=20).mean()
            features_df['mean_reversion_signal'] = (features_df['close'] - sma_20) / sma_20
            
            # Fill NaN values
            features_df = features_df.fillna(method='bfill').fillna(0)
            
            return features_df
        
        except Exception as e:
            logger.error(f"Error calculating features: {str(e)}")
            return df
    
    def _prepare_training_data(self, features_df: pd.DataFrame) -> np.ndarray:
        """
        Prepare training data for the model.
        
        Args:
            features_df: DataFrame with calculated features
            
        Returns:
            Numpy array with training features
        """
        try:
            # Select feature columns
            feature_columns = [col for col in self.features if col in features_df.columns]
            
            if not feature_columns:
                logger.warning("No valid features found for training")
                return np.array([])
            
            # Extract features
            X = features_df[feature_columns].values
            
            # Remove any remaining NaN or infinite values
            X = np.nan_to_num(X, nan=0.0, posinf=1e6, neginf=-1e6)
            
            return X
        
        except Exception as e:
            logger.error(f"Error preparing training data: {str(e)}")
            return np.array([])
    
    def train_model(self, df: pd.DataFrame) -> bool:
        """
        Train the probabilistic regime detection model.
        
        Args:
            df: DataFrame with OHLCV data
            
        Returns:
            True if training was successful, False otherwise
        """
        if not self.enabled:
            return False
        
        try:
            # Check if we have enough data
            if len(df) < self.min_training_samples:
                logger.warning(f"Not enough data for training: {len(df)} < {self.min_training_samples}")
                return False
            
            # Calculate features
            features_df = self._calculate_features(df)
            
            # Prepare training data
            X = self._prepare_training_data(features_df)
            
            if X.size == 0:
                logger.warning("No valid training data available")
                return False
            
            # Scale features
            X_scaled = self.scaler.fit_transform(X)
            
            # Train Gaussian Mixture Model
            self.gmm_model = GaussianMixture(
                n_components=self.n_states,
                covariance_type='full',
                max_iter=100,
                random_state=42
            )
            
            self.gmm_model.fit(X_scaled)
            
            # Mark as trained
            self.is_trained = True
            self.last_training_time = datetime.now()
            
            logger.info(f"Successfully trained probabilistic regime model with {len(X)} samples")
            return True
        
        except Exception as e:
            logger.error(f"Error training model: {str(e)}")
            return False
    
    def predict_regime_probabilities(self, df: pd.DataFrame) -> Dict[str, float]:
        """
        Predict regime probabilities using the trained model.
        
        Args:
            df: DataFrame with OHLCV data
            
        Returns:
            Dictionary with regime probabilities
        """
        if not self.enabled or not self.is_trained or self.gmm_model is None:
            return {}
        
        try:
            # Calculate features for the latest data point
            features_df = self._calculate_features(df)
            
            # Prepare prediction data (last row only)
            X = self._prepare_training_data(features_df.tail(1))
            
            if X.size == 0:
                return {}
            
            # Scale features
            X_scaled = self.scaler.transform(X)
            
            # Predict probabilities
            probabilities = self.gmm_model.predict_proba(X_scaled)[0]
            
            # Map to regime names
            regime_names = ['trending_up', 'trending_down', 'ranging', 'volatile'][:self.n_states]
            regime_probs = {}
            
            for i, prob in enumerate(probabilities):
                if i < len(regime_names):
                    regime_probs[regime_names[i]] = float(prob)
            
            # Add choppy regime if not enough confidence in any regime
            max_prob = max(regime_probs.values()) if regime_probs else 0.0
            if max_prob < self.confidence_threshold:
                regime_probs['choppy'] = 1.0 - max_prob
            
            return regime_probs
        
        except Exception as e:
            logger.error(f"Error predicting regime probabilities: {str(e)}")
            return {}
    
    def should_retrain(self) -> bool:
        """
        Check if the model should be retrained.
        
        Returns:
            True if retraining is needed, False otherwise
        """
        if not self.is_trained or self.last_training_time is None:
            return True
        
        time_since_training = datetime.now() - self.last_training_time
        return time_since_training > timedelta(hours=self.retrain_interval_hours)
    
    def update_history(self, regime_probs: Dict[str, float], confidence: float) -> None:
        """
        Update historical regime data.
        
        Args:
            regime_probs: Dictionary with regime probabilities
            confidence: Confidence score for the prediction
        """
        try:
            # Store regime probabilities
            self.regime_history.append(regime_probs.copy())
            self.confidence_history.append(confidence)
            
            # Maintain history size (keep last 1000 entries)
            max_history = 1000
            if len(self.regime_history) > max_history:
                self.regime_history.pop(0)
                self.confidence_history.pop(0)
        
        except Exception as e:
            logger.warning(f"Error updating history: {str(e)}")
    
    def get_regime_stability(self, lookback: int = 10) -> float:
        """
        Calculate regime stability over the last N predictions.
        
        Args:
            lookback: Number of recent predictions to analyze
            
        Returns:
            Stability score (0.0 to 1.0)
        """
        if len(self.regime_history) < lookback:
            return 0.0
        
        try:
            recent_regimes = []
            for regime_probs in self.regime_history[-lookback:]:
                if regime_probs:
                    primary_regime = max(regime_probs.keys(), key=lambda k: regime_probs[k])
                    recent_regimes.append(primary_regime)
            
            if not recent_regimes:
                return 0.0
            
            # Calculate stability as the frequency of the most common regime
            from collections import Counter
            regime_counts = Counter(recent_regimes)
            most_common_count = regime_counts.most_common(1)[0][1]
            stability = most_common_count / len(recent_regimes)
            
            return stability
        
        except Exception as e:
            logger.warning(f"Error calculating regime stability: {str(e)}")
            return 0.0
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        Get information about the current model state.
        
        Returns:
            Dictionary with model information
        """
        return {
            "enabled": self.enabled,
            "is_trained": self.is_trained,
            "n_states": self.n_states,
            "last_training_time": self.last_training_time.isoformat() if self.last_training_time else None,
            "should_retrain": self.should_retrain(),
            "history_length": len(self.regime_history),
            "features": self.features
        }
