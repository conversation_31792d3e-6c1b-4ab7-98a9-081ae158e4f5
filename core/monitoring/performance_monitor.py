"""
Performance Monitoring System for Synergy7 Enhanced Trading System.

This module provides comprehensive performance monitoring including system metrics,
trading performance, and real-time alerting capabilities.
"""

import os
import sys
import asyncio
import logging
import json
import psutil
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from pathlib import Path
import threading
from collections import deque, defaultdict

# Configure specialized logger
logger = logging.getLogger('performance')

class PerformanceMonitor:
    """
    Comprehensive performance monitoring system for trading operations.
    """

    def __init__(self, config: Dict[str, Any]):
        """Initialize the performance monitor."""
        self.config = config
        self.monitoring_enabled = config.get('monitoring', {}).get('enabled', True)
        self.update_interval = config.get('monitoring', {}).get('update_interval', 60)

        # Performance metrics storage
        self.system_metrics = deque(maxlen=1440)  # 24 hours at 1-minute intervals
        self.trading_metrics = deque(maxlen=1440)
        self.risk_metrics = deque(maxlen=1440)
        self.strategy_metrics = defaultdict(lambda: deque(maxlen=1440))

        # Alert thresholds
        self.alert_thresholds = {
            'memory_usage_pct': 85,
            'cpu_usage_pct': 80,
            'disk_usage_pct': 90,
            'error_rate_pct': 5,
            'api_response_time_ms': 5000,
            'var_breach_threshold': 0.02,
            'drawdown_threshold': -0.1,
            'daily_loss_threshold': -0.05
        }

        # Monitoring state
        self.is_monitoring = False
        self.last_alert_times = defaultdict(lambda: datetime.min)
        self.alert_cooldown = timedelta(minutes=15)  # Prevent spam

        logger.info("Performance Monitor initialized")

    def collect_system_metrics(self) -> Dict[str, Any]:
        """Collect system performance metrics."""
        try:
            # Memory metrics
            memory = psutil.virtual_memory()
            memory_metrics = {
                'total_gb': round(memory.total / (1024**3), 2),
                'available_gb': round(memory.available / (1024**3), 2),
                'used_pct': memory.percent,
                'free_pct': round((memory.available / memory.total) * 100, 2)
            }

            # CPU metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_metrics = {
                'usage_pct': cpu_percent,
                'cores': psutil.cpu_count(),
                'load_avg': os.getloadavg() if hasattr(os, 'getloadavg') else [0, 0, 0]
            }

            # Disk metrics
            disk = psutil.disk_usage('.')
            disk_metrics = {
                'total_gb': round(disk.total / (1024**3), 2),
                'free_gb': round(disk.free / (1024**3), 2),
                'used_pct': round((disk.used / disk.total) * 100, 2)
            }

            # Network metrics
            network = psutil.net_io_counters()
            network_metrics = {
                'bytes_sent': network.bytes_sent,
                'bytes_recv': network.bytes_recv,
                'packets_sent': network.packets_sent,
                'packets_recv': network.packets_recv
            }

            # Process metrics
            process = psutil.Process()
            process_metrics = {
                'cpu_percent': process.cpu_percent(),
                'memory_mb': round(process.memory_info().rss / (1024**2), 2),
                'num_threads': process.num_threads(),
                'open_files': len(process.open_files())
            }

            system_metrics = {
                'timestamp': datetime.now().isoformat(),
                'memory': memory_metrics,
                'cpu': cpu_metrics,
                'disk': disk_metrics,
                'network': network_metrics,
                'process': process_metrics
            }

            # Store metrics
            self.system_metrics.append(system_metrics)

            # Log metrics
            logger.info(f"System Metrics - CPU: {cpu_percent:.1f}%, Memory: {memory.percent:.1f}%, Disk: {disk_metrics['used_pct']:.1f}%")

            return system_metrics

        except Exception as e:
            logger.error(f"Error collecting system metrics: {str(e)}")
            return {}

    def collect_trading_metrics(self, trading_data: Dict[str, Any]) -> Dict[str, Any]:
        """Collect trading performance metrics."""
        try:
            trading_metrics = {
                'timestamp': datetime.now().isoformat(),
                'total_trades': trading_data.get('total_trades', 0),
                'successful_trades': trading_data.get('successful_trades', 0),
                'failed_trades': trading_data.get('failed_trades', 0),
                'total_pnl': trading_data.get('total_pnl', 0.0),
                'daily_pnl': trading_data.get('daily_pnl', 0.0),
                'win_rate': trading_data.get('win_rate', 0.0),
                'avg_trade_duration': trading_data.get('avg_trade_duration', 0.0),
                'active_positions': trading_data.get('active_positions', 0),
                'portfolio_value': trading_data.get('portfolio_value', 0.0)
            }

            # Calculate derived metrics
            if trading_metrics['total_trades'] > 0:
                trading_metrics['success_rate'] = (trading_metrics['successful_trades'] / trading_metrics['total_trades']) * 100
                trading_metrics['avg_pnl_per_trade'] = trading_metrics['total_pnl'] / trading_metrics['total_trades']
            else:
                trading_metrics['success_rate'] = 0.0
                trading_metrics['avg_pnl_per_trade'] = 0.0

            # Store metrics
            self.trading_metrics.append(trading_metrics)

            # Log metrics
            logger.info(f"Trading Metrics - Trades: {trading_metrics['total_trades']}, PnL: {trading_metrics['daily_pnl']:.4f}, Win Rate: {trading_metrics['win_rate']:.2%}")

            return trading_metrics

        except Exception as e:
            logger.error(f"Error collecting trading metrics: {str(e)}")
            return {}

    def collect_risk_metrics(self, risk_data: Dict[str, Any]) -> Dict[str, Any]:
        """Collect risk management metrics."""
        try:
            risk_metrics = {
                'timestamp': datetime.now().isoformat(),
                'portfolio_var': risk_data.get('portfolio_var', 0.0),
                'portfolio_cvar': risk_data.get('portfolio_cvar', 0.0),
                'max_position_size': risk_data.get('max_position_size', 0.0),
                'correlation_exposure': risk_data.get('correlation_exposure', 0.0),
                'daily_drawdown': risk_data.get('daily_drawdown', 0.0),
                'max_drawdown': risk_data.get('max_drawdown', 0.0),
                'risk_limit_breaches': risk_data.get('risk_limit_breaches', 0),
                'volatility': risk_data.get('volatility', 0.0)
            }

            # Store metrics
            self.risk_metrics.append(risk_metrics)

            # Log metrics
            logger.info(f"Risk Metrics - VaR: {risk_metrics['portfolio_var']:.4f}, CVaR: {risk_metrics['portfolio_cvar']:.4f}, Drawdown: {risk_metrics['daily_drawdown']:.4f}")

            return risk_metrics

        except Exception as e:
            logger.error(f"Error collecting risk metrics: {str(e)}")
            return {}

    def collect_strategy_metrics(self, strategy_data: Dict[str, Any]) -> Dict[str, Any]:
        """Collect individual strategy performance metrics."""
        try:
            strategy_metrics = {}

            for strategy_name, data in strategy_data.items():
                metrics = {
                    'timestamp': datetime.now().isoformat(),
                    'strategy_name': strategy_name,
                    'total_trades': data.get('total_trades', 0),
                    'net_pnl': data.get('net_pnl', 0.0),
                    'win_rate': data.get('win_rate', 0.0),
                    'sharpe_ratio': data.get('sharpe_ratio', 0.0),
                    'max_drawdown': data.get('max_drawdown', 0.0),
                    'current_weight': data.get('current_weight', 0.0),
                    'recent_pnl_7d': data.get('recent_pnl_7d', 0.0),
                    'volatility': data.get('volatility', 0.0)
                }

                strategy_metrics[strategy_name] = metrics
                self.strategy_metrics[strategy_name].append(metrics)

                # Log strategy metrics
                logger.info(f"Strategy {strategy_name} - PnL: {metrics['net_pnl']:.4f}, Weight: {metrics['current_weight']:.3f}, Sharpe: {metrics['sharpe_ratio']:.3f}")

            return strategy_metrics

        except Exception as e:
            logger.error(f"Error collecting strategy metrics: {str(e)}")
            return {}

    async def check_alert_conditions(self, system_metrics: Dict[str, Any],
                                   trading_metrics: Dict[str, Any],
                                   risk_metrics: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Check for alert conditions and generate alerts."""
        try:
            alerts = []
            current_time = datetime.now()

            # System alerts
            if system_metrics:
                # Memory usage alert
                memory_usage = system_metrics.get('memory', {}).get('used_pct', 0)
                if memory_usage > self.alert_thresholds['memory_usage_pct']:
                    if current_time - self.last_alert_times['memory'] > self.alert_cooldown:
                        alerts.append({
                            'type': 'system',
                            'severity': 'high' if memory_usage > 95 else 'medium',
                            'message': f"High memory usage: {memory_usage:.1f}%",
                            'metric': 'memory_usage',
                            'value': memory_usage,
                            'threshold': self.alert_thresholds['memory_usage_pct']
                        })
                        self.last_alert_times['memory'] = current_time

                # CPU usage alert
                cpu_usage = system_metrics.get('cpu', {}).get('usage_pct', 0)
                if cpu_usage > self.alert_thresholds['cpu_usage_pct']:
                    if current_time - self.last_alert_times['cpu'] > self.alert_cooldown:
                        alerts.append({
                            'type': 'system',
                            'severity': 'high' if cpu_usage > 95 else 'medium',
                            'message': f"High CPU usage: {cpu_usage:.1f}%",
                            'metric': 'cpu_usage',
                            'value': cpu_usage,
                            'threshold': self.alert_thresholds['cpu_usage_pct']
                        })
                        self.last_alert_times['cpu'] = current_time

                # Disk usage alert
                disk_usage = system_metrics.get('disk', {}).get('used_pct', 0)
                if disk_usage > self.alert_thresholds['disk_usage_pct']:
                    if current_time - self.last_alert_times['disk'] > self.alert_cooldown:
                        alerts.append({
                            'type': 'system',
                            'severity': 'critical' if disk_usage > 95 else 'high',
                            'message': f"High disk usage: {disk_usage:.1f}%",
                            'metric': 'disk_usage',
                            'value': disk_usage,
                            'threshold': self.alert_thresholds['disk_usage_pct']
                        })
                        self.last_alert_times['disk'] = current_time

            # Risk alerts
            if risk_metrics:
                # VaR breach alert
                portfolio_var = risk_metrics.get('portfolio_var', 0)
                if portfolio_var > self.alert_thresholds['var_breach_threshold']:
                    if current_time - self.last_alert_times['var'] > self.alert_cooldown:
                        alerts.append({
                            'type': 'risk',
                            'severity': 'critical',
                            'message': f"Portfolio VaR breach: {portfolio_var:.4f}",
                            'metric': 'portfolio_var',
                            'value': portfolio_var,
                            'threshold': self.alert_thresholds['var_breach_threshold']
                        })
                        self.last_alert_times['var'] = current_time

                # Drawdown alert
                daily_drawdown = risk_metrics.get('daily_drawdown', 0)
                if daily_drawdown < self.alert_thresholds['drawdown_threshold']:
                    if current_time - self.last_alert_times['drawdown'] > self.alert_cooldown:
                        alerts.append({
                            'type': 'risk',
                            'severity': 'high',
                            'message': f"High drawdown: {daily_drawdown:.4f}",
                            'metric': 'daily_drawdown',
                            'value': daily_drawdown,
                            'threshold': self.alert_thresholds['drawdown_threshold']
                        })
                        self.last_alert_times['drawdown'] = current_time

            # Trading alerts
            if trading_metrics:
                # Daily loss alert
                daily_pnl = trading_metrics.get('daily_pnl', 0)
                if daily_pnl < self.alert_thresholds['daily_loss_threshold']:
                    if current_time - self.last_alert_times['daily_loss'] > self.alert_cooldown:
                        alerts.append({
                            'type': 'trading',
                            'severity': 'high',
                            'message': f"High daily loss: {daily_pnl:.4f}",
                            'metric': 'daily_pnl',
                            'value': daily_pnl,
                            'threshold': self.alert_thresholds['daily_loss_threshold']
                        })
                        self.last_alert_times['daily_loss'] = current_time

            return alerts

        except Exception as e:
            logger.error(f"Error checking alert conditions: {str(e)}")
            return []

    def get_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive performance summary."""
        try:
            current_time = datetime.now()

            # Latest metrics
            latest_system = self.system_metrics[-1] if self.system_metrics else {}
            latest_trading = self.trading_metrics[-1] if self.trading_metrics else {}
            latest_risk = self.risk_metrics[-1] if self.risk_metrics else {}

            # Calculate trends (last hour vs previous hour)
            hour_ago = current_time - timedelta(hours=1)

            summary = {
                'timestamp': current_time.isoformat(),
                'monitoring_status': 'active' if self.is_monitoring else 'inactive',
                'system_health': {
                    'memory_usage_pct': latest_system.get('memory', {}).get('used_pct', 0),
                    'cpu_usage_pct': latest_system.get('cpu', {}).get('usage_pct', 0),
                    'disk_usage_pct': latest_system.get('disk', {}).get('used_pct', 0),
                    'process_memory_mb': latest_system.get('process', {}).get('memory_mb', 0)
                },
                'trading_performance': {
                    'total_trades': latest_trading.get('total_trades', 0),
                    'daily_pnl': latest_trading.get('daily_pnl', 0),
                    'win_rate': latest_trading.get('win_rate', 0),
                    'active_positions': latest_trading.get('active_positions', 0),
                    'portfolio_value': latest_trading.get('portfolio_value', 0)
                },
                'risk_metrics': {
                    'portfolio_var': latest_risk.get('portfolio_var', 0),
                    'portfolio_cvar': latest_risk.get('portfolio_cvar', 0),
                    'daily_drawdown': latest_risk.get('daily_drawdown', 0),
                    'max_drawdown': latest_risk.get('max_drawdown', 0)
                },
                'data_points': {
                    'system_metrics': len(self.system_metrics),
                    'trading_metrics': len(self.trading_metrics),
                    'risk_metrics': len(self.risk_metrics),
                    'strategy_metrics': {name: len(metrics) for name, metrics in self.strategy_metrics.items()}
                }
            }

            return summary

        except Exception as e:
            logger.error(f"Error generating performance summary: {str(e)}")
            return {}

    async def start_monitoring(self):
        """Start the performance monitoring loop."""
        if self.is_monitoring:
            logger.warning("Performance monitoring is already running")
            return

        self.is_monitoring = True
        logger.info("Starting performance monitoring...")

        try:
            while self.is_monitoring:
                # Collect system metrics
                system_metrics = self.collect_system_metrics()

                # Sleep for update interval
                await asyncio.sleep(self.update_interval)

        except Exception as e:
            logger.error(f"Error in monitoring loop: {str(e)}")
        finally:
            self.is_monitoring = False
            logger.info("Performance monitoring stopped")

    def stop_monitoring(self):
        """Stop the performance monitoring."""
        self.is_monitoring = False
        logger.info("Stopping performance monitoring...")

    def export_metrics(self, filepath: str, hours: int = 24) -> bool:
        """Export metrics to file for analysis."""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)

            # Filter metrics by time
            filtered_system = [m for m in self.system_metrics
                             if datetime.fromisoformat(m['timestamp']) > cutoff_time]
            filtered_trading = [m for m in self.trading_metrics
                              if datetime.fromisoformat(m['timestamp']) > cutoff_time]
            filtered_risk = [m for m in self.risk_metrics
                           if datetime.fromisoformat(m['timestamp']) > cutoff_time]

            export_data = {
                'export_timestamp': datetime.now().isoformat(),
                'time_range_hours': hours,
                'system_metrics': filtered_system,
                'trading_metrics': filtered_trading,
                'risk_metrics': filtered_risk,
                'strategy_metrics': {name: list(metrics)[-hours*60:]
                                   for name, metrics in self.strategy_metrics.items()}
            }

            with open(filepath, 'w') as f:
                json.dump(export_data, f, indent=2)

            logger.info(f"Metrics exported to {filepath}")
            return True

        except Exception as e:
            logger.error(f"Error exporting metrics: {str(e)}")
            return False

class IntegratedMonitoringService:
    """
    Integrated monitoring service that combines performance, risk, and system monitoring.
    """

    def __init__(self, config: Dict[str, Any]):
        """Initialize the integrated monitoring service."""
        self.config = config
        self.enabled = config.get('monitoring', {}).get('enabled', True)

        # Initialize monitoring components
        self.performance_monitor = PerformanceMonitor(config)

        # Import and initialize other monitors
        try:
            from .risk_alerts import RiskAlertManager
            from .system_metrics import SystemMetricsMonitor

            self.risk_alert_manager = RiskAlertManager(config)
            self.system_metrics_monitor = SystemMetricsMonitor(config)
        except ImportError as e:
            logger.warning(f"Could not import monitoring components: {str(e)}")
            self.risk_alert_manager = None
            self.system_metrics_monitor = None

        # Monitoring state
        self.is_running = False
        self.monitoring_tasks = []

        logger.info("Integrated Monitoring Service initialized")

    async def start_monitoring(self):
        """Start all monitoring services."""
        if self.is_running:
            logger.warning("Monitoring service is already running")
            return

        if not self.enabled:
            logger.info("Monitoring is disabled in configuration")
            return

        self.is_running = True
        logger.info("Starting integrated monitoring service...")

        try:
            # Start performance monitoring
            performance_task = asyncio.create_task(self.performance_monitor.start_monitoring())
            self.monitoring_tasks.append(performance_task)

            # Start system metrics monitoring if available
            if self.system_metrics_monitor:
                system_task = asyncio.create_task(self._system_monitoring_loop())
                self.monitoring_tasks.append(system_task)

            logger.info("All monitoring services started successfully")

        except Exception as e:
            logger.error(f"Error starting monitoring services: {str(e)}")
            self.is_running = False

    async def _system_monitoring_loop(self):
        """System monitoring loop."""
        try:
            while self.is_running:
                # Run system monitoring cycle
                cycle_results = await self.system_metrics_monitor.run_monitoring_cycle()

                # Process any alerts
                if cycle_results.get('alerts'):
                    for alert in cycle_results['alerts']:
                        logger.warning(f"System Alert: {alert['message']}")

                # Wait for next cycle
                await asyncio.sleep(self.system_metrics_monitor.update_interval)

        except Exception as e:
            logger.error(f"Error in system monitoring loop: {str(e)}")

    async def monitor_trading_cycle(self, trading_data: Dict[str, Any],
                                  risk_data: Dict[str, Any],
                                  strategy_data: Dict[str, Any]) -> Dict[str, Any]:
        """Monitor a complete trading cycle."""
        try:
            monitoring_results = {
                'timestamp': datetime.now().isoformat(),
                'performance_metrics': {},
                'risk_alerts': [],
                'system_status': {}
            }

            # Collect performance metrics
            if trading_data:
                monitoring_results['performance_metrics']['trading'] = self.performance_monitor.collect_trading_metrics(trading_data)

            if risk_data:
                monitoring_results['performance_metrics']['risk'] = self.performance_monitor.collect_risk_metrics(risk_data)

            if strategy_data:
                monitoring_results['performance_metrics']['strategies'] = self.performance_monitor.collect_strategy_metrics(strategy_data)

            # Check risk alerts
            if self.risk_alert_manager and risk_data:
                risk_alerts = await self.risk_alert_manager.monitor_risk_metrics(risk_data)
                monitoring_results['risk_alerts'] = [
                    {
                        'type': alert.alert_type.value,
                        'severity': alert.severity.value,
                        'message': alert.message,
                        'timestamp': alert.timestamp.isoformat()
                    }
                    for alert in risk_alerts
                ]

            # Get system status
            if self.system_metrics_monitor:
                monitoring_results['system_status'] = self.system_metrics_monitor.get_metrics_summary()

            return monitoring_results

        except Exception as e:
            logger.error(f"Error monitoring trading cycle: {str(e)}")
            return {}

    def get_comprehensive_status(self) -> Dict[str, Any]:
        """Get comprehensive monitoring status."""
        try:
            status = {
                'timestamp': datetime.now().isoformat(),
                'monitoring_enabled': self.enabled,
                'monitoring_running': self.is_running,
                'components': {}
            }

            # Performance monitor status
            status['components']['performance'] = self.performance_monitor.get_performance_summary()

            # Risk alert manager status
            if self.risk_alert_manager:
                status['components']['risk_alerts'] = self.risk_alert_manager.get_alert_summary()

            # System metrics status
            if self.system_metrics_monitor:
                status['components']['system_metrics'] = self.system_metrics_monitor.get_metrics_summary()

            return status

        except Exception as e:
            logger.error(f"Error getting comprehensive status: {str(e)}")
            return {}

    async def stop_monitoring(self):
        """Stop all monitoring services."""
        if not self.is_running:
            logger.warning("Monitoring service is not running")
            return

        self.is_running = False
        logger.info("Stopping integrated monitoring service...")

        try:
            # Stop performance monitoring
            self.performance_monitor.stop_monitoring()

            # Cancel all monitoring tasks
            for task in self.monitoring_tasks:
                if not task.done():
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass

            self.monitoring_tasks.clear()
            logger.info("All monitoring services stopped successfully")

        except Exception as e:
            logger.error(f"Error stopping monitoring services: {str(e)}")

    def export_all_metrics(self, base_filepath: str, hours: int = 24) -> Dict[str, bool]:
        """Export all metrics to files."""
        try:
            export_results = {}

            # Export performance metrics
            perf_file = f"{base_filepath}_performance.json"
            export_results['performance'] = self.performance_monitor.export_metrics(perf_file, hours)

            # Export system metrics if available
            if self.system_metrics_monitor:
                system_file = f"{base_filepath}_system.json"
                # Note: Would need to implement export_metrics in SystemMetricsMonitor
                export_results['system'] = True  # Placeholder

            # Export risk alerts if available
            if self.risk_alert_manager:
                risk_file = f"{base_filepath}_risk_alerts.json"
                # Note: Would need to implement export_alerts in RiskAlertManager
                export_results['risk_alerts'] = True  # Placeholder

            return export_results

        except Exception as e:
            logger.error(f"Error exporting all metrics: {str(e)}")
            return {}
