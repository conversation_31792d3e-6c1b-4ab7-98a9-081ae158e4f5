#!/usr/bin/env python3
"""
Test Trade Notification

This script tests the enhanced trade notification for winning trades.
"""

import os
import sys
import asyncio
import logging
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("test_trade_notification")

# Add parent directory to path for imports
parent_dir = os.path.dirname(os.path.abspath(__file__))
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

# Import trading alerts module
from phase_4_deployment.utils.trading_alerts import TradingAlerts

async def test_trade_notification():
    """Test trade notification for winning trades."""
    # Create trading alerts instance
    telegram_bot_token = "**********************************************"
    telegram_chat_id = "5135869709"

    alerts = TradingAlerts(telegram_bot_token, telegram_chat_id)

    # Set initial wallet balance
    alerts.update_wallet_balance(50.0, is_initial=True)

    # Create buy trade
    buy_trade = {
        'action': 'BUY',
        'market': 'SOL-USDC',
        'price': 150.25,
        'size': 0.25,
        'confidence': 0.85,
        'timestamp': datetime.now().isoformat()
    }

    # Send buy trade notification
    logger.info("Sending buy trade notification...")
    await alerts.send_trade_notification(buy_trade)

    # Wait for notification to be sent
    await asyncio.sleep(2)

    # Create sell trade (profitable)
    sell_trade = {
        'action': 'SELL',
        'market': 'SOL-USDC',
        'price': 155.75,
        'size': 0.25,
        'confidence': 0.78,
        'timestamp': (datetime.now() + timedelta(hours=2)).isoformat()
    }

    # Send sell trade notification
    logger.info("Sending sell trade notification (profitable)...")
    await alerts.send_trade_notification(sell_trade)

    # Wait for notification to be sent
    await asyncio.sleep(2)

    # Close HTTP client
    await alerts.close()

    logger.info("Trade notification test completed. Please check your Telegram for messages.")

async def main():
    """Main function."""
    logger.info("Testing trade notification...")

    await test_trade_notification()

    logger.info("Test completed.")

if __name__ == "__main__":
    asyncio.run(main())
