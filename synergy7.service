[Unit]
Description=Synergy7 Enhanced Trading System
After=network.target
Wants=network.target

[Service]
Type=simple
User=ubuntu
Group=ubuntu
WorkingDirectory=/Users/<USER>/HedgeFund
Environment=PATH=/Users/<USER>/HedgeFund/venv/bin/python
ExecStart=/Users/<USER>/HedgeFund/venv/bin/python phase_4_deployment/unified_runner.py --mode production
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=synergy7

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/Users/<USER>/HedgeFund

[Install]
WantedBy=multi-user.target
