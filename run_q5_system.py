#!/usr/bin/env python3
"""
[DEPRECATED] Run Synergy7 Trading System with Fallback Implementation

This script runs the Synergy7 Trading System using the fallback implementation
of solana_tx_utils.

WARNING: This entry point is DEPRECATED. Please use phase_4_deployment/unified_runner.py
         for all production deployments.
"""

import warnings
warnings.warn(
    "This entry point (run_q5_system.py) is deprecated. "
    "Please use phase_4_deployment/unified_runner.py for all production deployments.",
    DeprecationWarning,
    stacklevel=2
)

import os
import sys
import argparse
import logging
import yaml
from pathlib import Path
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="[%(asctime)s] [%(levelname)s] %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger("q5_system")

def load_config():
    """Load configuration from config.yaml."""
    config_path = Path("config.yaml")
    if not config_path.exists():
        logger.error(f"Config file not found: {config_path}")
        return None

    with open(config_path, "r") as f:
        config = yaml.safe_load(f)

    return config

def load_environment():
    """Load environment variables from .env file."""
    env_path = Path(".env")
    if not env_path.exists():
        logger.warning(f"Environment file not found: {env_path}")
        return False

    load_dotenv(env_path)

    # Check required environment variables
    required_vars = ["HELIUS_API_KEY", "BIRDEYE_API_KEY", "WALLET_ADDRESS"]
    missing_vars = []

    for var in required_vars:
        if not os.environ.get(var):
            missing_vars.append(var)

    if missing_vars:
        logger.error(f"Missing required environment variables: {', '.join(missing_vars)}")
        return False

    return True

def run_paper_trading():
    """Run the system in paper trading mode."""
    logger.info("Starting Q5 System in PAPER TRADING mode...")

    # Set environment variables
    os.environ["TRADING_ENABLED"] = "false"
    os.environ["PAPER_TRADING"] = "true"
    os.environ["BACKTESTING_ENABLED"] = "false"

    # Import and run the paper trading module
    try:
        # First, try to import shared.solana_utils.tx_utils_fallback to ensure it's available
        import shared.solana_utils.tx_utils_fallback
        logger.info("Using fallback implementation for Solana transactions")

        # Run the paper trading module
        logger.info("Running paper trading module...")
        os.system("python start_live_trading_local.py")

        return True
    except ImportError as e:
        logger.error(f"Failed to import shared.solana_utils.tx_utils_fallback: {e}")
        return False

def run_backtest():
    """Run the system in backtest mode."""
    logger.info("Starting Q5 System in BACKTEST mode...")

    # Set environment variables
    os.environ["TRADING_ENABLED"] = "false"
    os.environ["PAPER_TRADING"] = "false"
    os.environ["BACKTESTING_ENABLED"] = "true"

    # Import and run the backtest module
    try:
        # First, try to import shared.solana_utils.tx_utils_fallback to ensure it's available
        import shared.solana_utils.tx_utils_fallback
        logger.info("Using fallback implementation for Solana transactions")

        # Run the backtest module
        logger.info("Running backtest module...")
        os.system("python start_live_trading_local.py")

        return True
    except ImportError as e:
        logger.error(f"Failed to import shared.solana_utils.tx_utils_fallback: {e}")
        return False

def run_live_trading():
    """Run the system in live trading mode."""
    logger.info("Starting Q5 System in LIVE TRADING mode...")

    # Set environment variables
    os.environ["TRADING_ENABLED"] = "true"
    os.environ["PAPER_TRADING"] = "false"
    os.environ["BACKTESTING_ENABLED"] = "false"

    # Import and run the live trading module
    try:
        # First, try to import shared.solana_utils.tx_utils_fallback to ensure it's available
        import shared.solana_utils.tx_utils_fallback
        logger.info("Using fallback implementation for Solana transactions")

        # Run the live trading module
        logger.info("Running live trading module...")
        os.system("python start_live_trading_local.py")

        return True
    except ImportError as e:
        logger.error(f"Failed to import shared.solana_utils.tx_utils_fallback: {e}")
        return False

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Run Q5 Trading System")
    parser.add_argument("--mode", choices=["paper", "backtest", "live"], default="paper",
                        help="Trading mode (default: paper)")

    args = parser.parse_args()

    # Load configuration
    config = load_config()
    if not config:
        return 1

    # Load environment variables
    if not load_environment():
        return 1

    # Run the system in the specified mode
    if args.mode == "paper":
        success = run_paper_trading()
    elif args.mode == "backtest":
        success = run_backtest()
    elif args.mode == "live":
        success = run_live_trading()
    else:
        logger.error(f"Unknown mode: {args.mode}")
        return 1

    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
