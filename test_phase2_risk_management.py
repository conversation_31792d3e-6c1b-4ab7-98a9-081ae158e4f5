#!/usr/bin/env python3
"""
Test script for Phase 2: Advanced Risk Management.

This script tests the VaR calculator, portfolio risk manager, and enhanced position sizer
to ensure they work correctly with the configuration-driven architecture.
"""

import os
import sys
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import yaml
import re

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import our new modules
from core.risk.var_calculator import VaRCalculator
from core.risk.portfolio_risk_manager import PortfolioRiskManager
from core.risk.position_sizer import EnhancedPositionSizer

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_test_config():
    """Load test configuration with environment variable substitution."""
    try:
        # Set test environment variables
        os.environ.setdefault('VAR_ENABLED', 'true')
        os.environ.setdefault('CVAR_ENABLED', 'true')
        os.environ.setdefault('POSITION_SIZING_METHOD', 'var_based')
        os.environ.setdefault('REGIME_BASED_SIZING', 'true')
        
        # Load and process config
        with open('config.yaml', 'r') as f:
            config_text = f.read()
        
        # Simple environment variable substitution
        def replace_env_var(match):
            var_expr = match.group(1)
            if ':-' in var_expr:
                var_name, default_value = var_expr.split(':-', 1)
                return os.environ.get(var_name, default_value)
            else:
                return os.environ.get(var_expr, '')
        
        config_text = re.sub(r'\$\{([^}]+)\}', replace_env_var, config_text)
        config = yaml.safe_load(config_text)
        
        # Convert string values to appropriate types
        def convert_types(obj):
            if isinstance(obj, dict):
                return {k: convert_types(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_types(item) for item in obj]
            elif isinstance(obj, str):
                if obj.lower() == 'true':
                    return True
                elif obj.lower() == 'false':
                    return False
                else:
                    try:
                        if '.' in obj:
                            return float(obj)
                        else:
                            return int(obj)
                    except ValueError:
                        return obj
            return obj
        
        config = convert_types(config)
        return config
    except Exception as e:
        logger.error(f"Error loading config: {str(e)}")
        return {}

def generate_test_portfolio_data(assets=5, days=100):
    """Generate synthetic portfolio data for testing."""
    logger.info(f"Generating portfolio data for {assets} assets over {days} days...")
    
    portfolio_data = {}
    np.random.seed(42)
    
    for i in range(assets):
        asset_name = f"ASSET_{i+1}"
        
        # Generate dates
        dates = pd.date_range(start=datetime.now() - timedelta(days=days), periods=days*24, freq='H')
        
        # Generate correlated price data
        base_price = 25.0 + i * 5.0
        prices = [base_price]
        volumes = []
        
        # Create some correlation between assets
        correlation_factor = 0.3 if i > 0 else 0.0
        
        for j in range(len(dates) - 1):
            # Base return
            base_return = np.random.normal(0.0001, 0.02)
            
            # Add correlation with first asset
            if i > 0 and len(portfolio_data) > 0:
                first_asset_return = (portfolio_data['ASSET_1']['close'].iloc[j+1] / 
                                    portfolio_data['ASSET_1']['close'].iloc[j] - 1)
                correlated_return = base_return + correlation_factor * first_asset_return
            else:
                correlated_return = base_return
            
            new_price = prices[-1] * (1 + correlated_return)
            prices.append(max(0.1, new_price))
            
            # Generate volume
            base_volume = 1000000 * (i + 1)
            volume_change = np.random.normal(0, 0.3)
            volume = base_volume * (1 + volume_change)
            volumes.append(max(100000, volume))
        
        volumes.append(volumes[-1])
        
        # Create OHLCV data
        df = pd.DataFrame({
            'timestamp': dates,
            'open': prices,
            'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
            'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
            'close': prices,
            'volume': volumes
        })
        
        # Ensure OHLC consistency
        df['high'] = df[['open', 'close', 'high']].max(axis=1)
        df['low'] = df[['open', 'close', 'low']].min(axis=1)
        
        portfolio_data[asset_name] = df
    
    logger.info(f"Generated portfolio data for {len(portfolio_data)} assets")
    return portfolio_data

def generate_test_positions(portfolio_data, total_value=100000):
    """Generate test portfolio positions."""
    positions = {}
    
    for i, asset_name in enumerate(portfolio_data.keys()):
        # Random position sizes
        weight = np.random.uniform(0.1, 0.3)
        position_value = total_value * weight
        
        current_price = portfolio_data[asset_name]['close'].iloc[-1]
        position_size = position_value / current_price
        
        positions[asset_name] = {
            'size': position_size,
            'value': position_value,
            'entry_price': current_price * 0.95,  # Simulate some profit
            'current_price': current_price
        }
    
    return positions

def test_var_calculator(config, portfolio_data):
    """Test the VaR calculator."""
    logger.info("Testing VaR Calculator...")
    
    try:
        # Initialize calculator
        calculator = VaRCalculator(config)
        
        # Test with single asset
        test_asset = list(portfolio_data.keys())[0]
        test_data = portfolio_data[test_asset]
        
        # Calculate returns
        returns = calculator.calculate_returns(test_data)
        logger.info(f"Calculated {len(returns)} returns for {test_asset}")
        
        # Test historical VaR
        hist_var = calculator.historical_var(returns, 0.95)
        logger.info(f"Historical VaR (95%): {hist_var:.4f}")
        
        # Test parametric VaR
        param_var = calculator.parametric_var(returns, 0.95)
        logger.info(f"Parametric VaR (95%): {param_var:.4f}")
        
        # Test Monte Carlo VaR
        mc_var = calculator.monte_carlo_var(returns, 0.95, 1000)
        logger.info(f"Monte Carlo VaR (95%): {mc_var:.4f}")
        
        # Test CVaR
        cvar = calculator.conditional_var(returns, 0.95)
        logger.info(f"Conditional VaR (95%): {cvar:.4f}")
        
        # Test comprehensive VaR
        comprehensive = calculator.calculate_comprehensive_var(returns)
        logger.info(f"Comprehensive VaR calculated with {len(comprehensive.get('methods', {}))} confidence levels")
        
        # Test portfolio VaR
        portfolio_returns = {}
        weights = {}
        for asset, data in portfolio_data.items():
            portfolio_returns[asset] = calculator.calculate_returns(data)
            weights[asset] = 1.0 / len(portfolio_data)  # Equal weights
        
        portfolio_var = calculator.calculate_portfolio_var(portfolio_returns, weights)
        logger.info(f"Portfolio VaR: {portfolio_var.get('portfolio_var', 0):.4f}")
        
        # Test VaR limits
        limit_check = calculator.check_var_limits(portfolio_var.get('portfolio_var', 0))
        logger.info(f"VaR limit check: {limit_check}")
        
        return True
    
    except Exception as e:
        logger.error(f"Error testing VaR calculator: {str(e)}")
        return False

def test_portfolio_risk_manager(config, portfolio_data):
    """Test the portfolio risk manager."""
    logger.info("Testing Portfolio Risk Manager...")
    
    try:
        # Initialize manager
        manager = PortfolioRiskManager(config)
        
        # Generate test positions
        positions = generate_test_positions(portfolio_data)
        logger.info(f"Generated {len(positions)} test positions")
        
        # Update positions
        manager.update_positions(positions)
        
        # Calculate correlation matrix
        correlation_matrix = manager.calculate_correlation_matrix(portfolio_data)
        logger.info(f"Calculated correlation matrix: {correlation_matrix.shape}")
        
        # Identify correlated positions
        correlated_groups = manager.identify_correlated_positions(correlation_matrix)
        logger.info(f"Identified {len(correlated_groups)} correlated groups")
        
        # Calculate portfolio risk metrics
        risk_metrics = manager.calculate_portfolio_risk_metrics(portfolio_data)
        logger.info(f"Portfolio risk metrics calculated")
        logger.info(f"Portfolio value: ${risk_metrics.get('portfolio_value', 0):,.2f}")
        logger.info(f"Position count: {risk_metrics.get('position_count', 0)}")
        
        # Check risk limits
        violations = manager.check_risk_limits()
        logger.info(f"Risk limit violations: {len(violations)}")
        
        # Get risk summary
        summary = manager.get_risk_summary()
        logger.info(f"Risk summary: {summary}")
        
        return True
    
    except Exception as e:
        logger.error(f"Error testing portfolio risk manager: {str(e)}")
        return False

def test_enhanced_position_sizer(config, portfolio_data):
    """Test the enhanced position sizer."""
    logger.info("Testing Enhanced Position Sizer...")
    
    try:
        # Initialize sizer
        sizer = EnhancedPositionSizer(config)
        
        # Test data
        test_asset = list(portfolio_data.keys())[0]
        test_data = portfolio_data[test_asset]
        account_balance = 100000.0
        
        # Generate current positions and correlation data
        current_positions = generate_test_positions(portfolio_data, account_balance)
        
        # Test enhanced position sizing
        result = sizer.calculate_enhanced_position_size(
            price_data=test_data,
            account_balance=account_balance,
            market=test_asset,
            signal_strength=0.8,
            market_regime="trending_up",
            current_positions=current_positions,
            correlation_data=portfolio_data
        )
        
        logger.info(f"Enhanced position size result:")
        logger.info(f"  Position size: {result['position_size']:.4f}")
        logger.info(f"  Position value: ${result['position_value']:,.2f}")
        logger.info(f"  Base position size: {result['base_position_size']:.4f}")
        logger.info(f"  Position VaR: {result['position_var']:.4f}")
        logger.info(f"  Market regime: {result['market_regime']}")
        logger.info(f"  Regime multiplier: {result['regime_multiplier']:.2f}")
        logger.info(f"  VaR-based: {result['var_based']}")
        
        # Test different regimes
        regimes = ["trending_up", "trending_down", "ranging", "volatile", "choppy"]
        for regime in regimes:
            regime_result = sizer.calculate_enhanced_position_size(
                price_data=test_data,
                account_balance=account_balance,
                market=test_asset,
                signal_strength=1.0,
                market_regime=regime
            )
            logger.info(f"Regime {regime}: position size {regime_result['position_size']:.4f}")
        
        # Test backward compatibility
        compat_result = sizer.calculate_position_size(test_data, account_balance, test_asset)
        logger.info(f"Backward compatibility result: {compat_result['position_size']:.4f}")
        
        return True
    
    except Exception as e:
        logger.error(f"Error testing enhanced position sizer: {str(e)}")
        return False

def main():
    """Main test function."""
    logger.info("Starting Phase 2 Risk Management Tests...")
    
    # Load configuration
    config = load_test_config()
    if not config:
        logger.error("Failed to load configuration")
        return False
    
    # Generate test portfolio data
    portfolio_data = generate_test_portfolio_data(assets=5, days=60)
    
    # Test results
    results = {}
    
    # Test VaR calculator
    results['var_calculator'] = test_var_calculator(config, portfolio_data)
    
    # Test portfolio risk manager
    results['portfolio_risk_manager'] = test_portfolio_risk_manager(config, portfolio_data)
    
    # Test enhanced position sizer
    results['enhanced_position_sizer'] = test_enhanced_position_sizer(config, portfolio_data)
    
    # Print results summary
    logger.info("\n" + "="*50)
    logger.info("PHASE 2 RISK MANAGEMENT TEST RESULTS")
    logger.info("="*50)
    
    all_passed = True
    for test_name, passed in results.items():
        status = "PASS" if passed else "FAIL"
        logger.info(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    logger.info("="*50)
    overall_status = "ALL TESTS PASSED" if all_passed else "SOME TESTS FAILED"
    logger.info(f"Overall Status: {overall_status}")
    logger.info("="*50)
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
