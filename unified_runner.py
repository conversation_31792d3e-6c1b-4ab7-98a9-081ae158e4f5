#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
[DEPRECATED] Legacy Unified Runner for Synergy7 System

This is a legacy entry point for the Synergy7 System trading platform.
It coordinates strategy execution, wallet management, risk controls,
and monitoring across the entire system.

WARNING: This entry point is DEPRECATED. Please use phase_4_deployment/unified_runner.py
         for all production deployments.
"""

import warnings
warnings.warn(
    "This entry point (unified_runner.py) is deprecated. "
    "Please use phase_4_deployment/unified_runner.py for all production deployments.",
    DeprecationWarning,
    stacklevel=2
)

import os
import sys
import yaml
import json
import logging
import asyncio
import signal
import argparse
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("logs/unified_runner.log")
    ]
)
logger = logging.getLogger("q5.unified_runner")

# Load environment variables
load_dotenv()

# Import system components
# These will be dynamically imported based on configuration

class UnifiedRunner:
    """
    Main orchestrator for the Q5 System trading platform.
    """

    def __init__(self, config_path: str = "config.yaml"):
        """
        Initialize the unified runner.

        Args:
            config_path: Path to the configuration file
        """
        self.config = self._load_config(config_path)
        self.mode = self._determine_mode()

        # Create necessary directories
        self._setup_directories()

        # Component instances
        self.strategies = []
        self.wallet_tracker = None
        self.risk_manager = None
        self.monitoring = None

        # State management
        self.running = False
        self.tasks = []

        logger.info(f"Initialized Q5 System in {self.mode} mode")

    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Load configuration from YAML file."""
        try:
            with open(config_path, "r") as f:
                config = yaml.safe_load(f)

                # Set defaults for missing values
                config.setdefault("mode", {})
                config["mode"].setdefault("live_trading", False)
                config["mode"].setdefault("paper_trading", False)
                config["mode"].setdefault("backtesting", False)
                config["mode"].setdefault("simulation", False)

                return config
        except Exception as e:
            logger.error(f"Error loading config: {e}")
            return {"mode": {"simulation": True}}

    def _determine_mode(self) -> str:
        """Determine the operating mode based on configuration and environment."""
        # Environment variables override config file
        env_trading = os.getenv("TRADING_ENABLED", "").lower() == "true"
        env_paper = os.getenv("PAPER_TRADING", "").lower() == "true"
        env_backtest = os.getenv("BACKTESTING_ENABLED", "").lower() == "true"

        # Check config
        config_live = self.config.get("mode", {}).get("live_trading", False)
        config_paper = self.config.get("mode", {}).get("paper_trading", False)
        config_backtest = self.config.get("mode", {}).get("backtesting", False)
        config_sim = self.config.get("mode", {}).get("simulation", False)

        # Determine mode with priority
        if env_trading or config_live:
            return "live_trading"
        elif env_paper or config_paper:
            return "paper_trading"
        elif env_backtest or config_backtest:
            return "backtesting"
        elif config_sim:
            return "simulation"
        else:
            return "simulation"  # Default fallback

    def _setup_directories(self) -> None:
        """Create necessary directories for logs and data."""
        directories = [
            "logs",
            "phase_0_env_setup/data",
            "phase_0_env_setup/data/historical",
            "phase_0_env_setup/data/raw_events",
            "phase_1_strategy_runner/outputs",
            "phase_2_backtest_engine/output",
            "phase_3_rl_agent_training/output",
            "phase_4_deployment/wallet_sync/data"
        ]

        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)

    async def load_components(self) -> None:
        """Load and initialize system components based on mode."""
        try:
            # Load wallet tracker
            from phase_4_deployment.wallet_sync.state_tracker import WalletStateTracker
            self.wallet_tracker = WalletStateTracker(config_path="config.yaml")
            logger.info("Loaded wallet state tracker")

            # Load strategies based on configuration
            await self._load_strategies()

            # Load risk manager
            # This would be imported from the appropriate module
            # self.risk_manager = RiskManager(self.config)

            # Load monitoring
            # This would be imported from the appropriate module
            # self.monitoring = MonitoringSystem(self.config)

            logger.info("All components loaded successfully")
        except Exception as e:
            logger.error(f"Error loading components: {e}")
            raise

    async def _load_strategies(self) -> None:
        """Load and initialize trading strategies."""
        strategy_configs = self.config.get("strategies", [])

        for strategy_config in strategy_configs:
            if not strategy_config.get("enabled", False):
                continue

            strategy_name = strategy_config.get("name")
            strategy_params = strategy_config.get("params", {})

            try:
                # Import strategy dynamically
                module_path = f"phase_1_strategy_runner.strategies.{strategy_name}"
                module = __import__(module_path, fromlist=["Strategy"])
                strategy_class = getattr(module, "Strategy")

                # Initialize strategy
                strategy_instance = strategy_class(**strategy_params)

                self.strategies.append({
                    "name": strategy_name,
                    "instance": strategy_instance,
                    "params": strategy_params
                })

                logger.info(f"Loaded strategy: {strategy_name}")
            except Exception as e:
                logger.error(f"Error loading strategy {strategy_name}: {e}")

    async def start(self) -> None:
        """Start the unified runner."""
        logger.info(f"Starting Q5 System in {self.mode} mode")

        # Set running flag
        self.running = True

        # Register signal handlers
        self._register_signal_handlers()

        # Load components
        await self.load_components()

        # Start components based on mode
        if self.mode == "live_trading" or self.mode == "paper_trading":
            await self._start_live_mode()
        elif self.mode == "backtesting":
            await self._start_backtest_mode()
        elif self.mode == "simulation":
            await self._start_simulation_mode()

        logger.info("Q5 System started successfully")

    def _register_signal_handlers(self) -> None:
        """Register signal handlers for graceful shutdown."""
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

    def _signal_handler(self, sig, frame) -> None:
        """Handle termination signals."""
        logger.info(f"Received signal {sig}, shutting down...")
        asyncio.create_task(self.stop())

    async def _start_live_mode(self) -> None:
        """Start components in live trading mode."""
        # Start wallet tracker
        wallet_task = asyncio.create_task(self.wallet_tracker.run())
        self.tasks.append(wallet_task)

        # Start strategy runner
        # This would start the strategy execution loop

        # Start monitoring
        # This would start the monitoring system

        logger.info(f"Started {self.mode} mode components")

    async def _start_backtest_mode(self) -> None:
        """Start components in backtest mode."""
        # Import and run backtester
        from phase_2_backtest_engine.unified_backtest_mod import UnifiedBacktester

        backtester = UnifiedBacktester(config_path="config.yaml")
        results = backtester.run()

        # Print summary
        metrics = results.get("metrics", {})
        logger.info("===== Backtest Results =====")
        logger.info(f"Total Return: {metrics.get('total_return', 0):.2%}")
        logger.info(f"Annualized Return: {metrics.get('annualized_return', 0):.2%}")
        logger.info(f"Sharpe Ratio: {metrics.get('sharpe_ratio', 0):.2f}")
        logger.info(f"Max Drawdown: {metrics.get('max_drawdown', 0):.2%}")
        logger.info(f"Win Rate: {metrics.get('win_rate', 0):.2%}")
        logger.info(f"Total Trades: {metrics.get('total_trades', 0)}")
        logger.info(f"Final Equity: ${metrics.get('final_equity', 0):.2f}")
        logger.info("============================")

        # Signal completion
        await self.stop()

    async def _start_simulation_mode(self) -> None:
        """Start components in simulation mode."""
        logger.info("Starting simulation mode")

        # This would run a simplified simulation
        # For now, just wait for a while then exit
        await asyncio.sleep(5)

        logger.info("Simulation completed")
        await self.stop()

    async def stop(self) -> None:
        """Stop the unified runner and all components."""
        if not self.running:
            return

        logger.info("Stopping Q5 System...")

        # Set running flag
        self.running = False

        # Cancel all tasks
        for task in self.tasks:
            if not task.done():
                task.cancel()

        # Clean up resources
        if self.wallet_tracker:
            # Save state before exit
            self.wallet_tracker._save_state()

        logger.info("Q5 System stopped")

async def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Q5 System Unified Runner")
    parser.add_argument("--config", default="config.yaml", help="Path to configuration file")
    parser.add_argument("--mode", choices=["live", "paper", "backtest", "simulation"],
                        help="Override operating mode")
    args = parser.parse_args()

    # Create runner
    runner = UnifiedRunner(config_path=args.config)

    # Override mode if specified
    if args.mode:
        mode_map = {
            "live": "live_trading",
            "paper": "paper_trading",
            "backtest": "backtesting",
            "simulation": "simulation"
        }
        runner.mode = mode_map.get(args.mode, runner.mode)

    # Start runner
    await runner.start()

    # Keep running until stopped
    while runner.running:
        await asyncio.sleep(1)

if __name__ == "__main__":
    asyncio.run(main())
