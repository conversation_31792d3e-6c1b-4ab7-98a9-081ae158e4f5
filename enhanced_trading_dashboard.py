#!/usr/bin/env python3
"""
Enhanced Trading Dashboard for Real-Time Paper Trading Monitoring.

This dashboard displays real-time data from our enhanced 4-phase trading system
including market regime detection, whale signals, risk management, and adaptive weighting.
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import json
import os
import sys
from datetime import datetime, timedelta
from pathlib import Path
import time

# Configure page
st.set_page_config(
    page_title="Synergy7 Enhanced Trading Dashboard",
    page_icon="📈",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Add project root to path
project_root = Path.cwd()
sys.path.append(str(project_root))

def load_latest_cycle_data():
    """Load the latest trading cycle data."""
    try:
        # Try live trading data first, then paper trading
        for data_type in ['live_trading', 'paper_trading']:
            latest_file = Path(f"output/{data_type}/dashboard/latest_cycle.json")
            if latest_file.exists():
                with open(latest_file, 'r') as f:
                    data = json.load(f)
                    data['data_source'] = data_type
                    return data
        return None
    except Exception as e:
        st.error(f"Error loading cycle data: {str(e)}")
        return None

def load_trading_metrics():
    """Load trading metrics summary."""
    try:
        # Try live trading data first, then paper trading
        for data_type in ['live_trading', 'paper_trading']:
            metrics_file = Path(f"output/{data_type}/dashboard/trading_metrics.json")
            if metrics_file.exists():
                with open(metrics_file, 'r') as f:
                    data = json.load(f)
                    data['data_source'] = data_type
                    return data
        return None
    except Exception as e:
        st.error(f"Error loading trading metrics: {str(e)}")
        return None

def load_historical_cycles():
    """Load historical cycle data for charts."""
    try:
        cycles = []

        # Try live trading data first, then paper trading
        for data_type in ['live_trading', 'paper_trading']:
            cycles_dir = Path(f"output/{data_type}/cycles")
            if cycles_dir.exists():
                cycle_files = sorted(cycles_dir.glob("cycle_*.json"))

                # Load last 20 cycles for performance
                for cycle_file in cycle_files[-20:]:
                    try:
                        with open(cycle_file, 'r') as f:
                            cycle_data = json.load(f)
                            cycle_data['data_source'] = data_type
                            cycles.append(cycle_data)
                    except Exception as e:
                        st.warning(f"Error loading {cycle_file}: {str(e)}")

                if cycles:  # If we found data, use it
                    break

        return cycles
    except Exception as e:
        st.error(f"Error loading historical cycles: {str(e)}")
        return []

def display_session_overview(metrics_data):
    """Display session overview metrics."""
    if not metrics_data:
        st.warning("No trading metrics available")
        return

    metrics = metrics_data.get('metrics', {})

    # Key metrics row
    col1, col2, col3, col4, col5 = st.columns(5)

    with col1:
        st.metric(
            "Total Cycles",
            metrics.get('total_cycles', 0),
            delta=None
        )

    with col2:
        success_rate = (metrics.get('successful_cycles', 0) / max(metrics.get('total_cycles', 1), 1)) * 100
        st.metric(
            "Success Rate",
            f"{success_rate:.1f}%",
            delta=None
        )

    with col3:
        st.metric(
            "Current Regime",
            metrics.get('current_regime', 'Unknown').title(),
            delta=f"{metrics.get('regime_confidence', 0.0):.3f} confidence"
        )

    with col4:
        st.metric(
            "Avg VaR",
            f"{metrics.get('avg_var', 0.0):.4f}",
            delta=None
        )

    with col5:
        session_duration = metrics_data.get('session_duration_minutes', 0)
        st.metric(
            "Session Duration",
            f"{session_duration:.1f} min",
            delta=None
        )

def display_current_cycle(cycle_data):
    """Display current cycle information."""
    if not cycle_data:
        st.warning("No current cycle data available")
        return

    st.subheader("📊 Current Cycle Status")

    # Cycle overview
    col1, col2, col3 = st.columns(3)

    with col1:
        st.info(f"**Cycle #{cycle_data.get('cycle_number', 'N/A')}**")
        st.info(f"**Status**: {cycle_data.get('status', 'Unknown').title()}")

    with col2:
        timestamp = cycle_data.get('timestamp', '')
        if timestamp:
            cycle_time = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            st.info(f"**Started**: {cycle_time.strftime('%H:%M:%S')}")

        duration = cycle_data.get('duration_seconds', 0)
        st.info(f"**Duration**: {duration:.2f}s")

    with col3:
        phase_results = cycle_data.get('phase_results', {})
        completed_phases = len([p for p in phase_results.values() if p])
        st.info(f"**Phases Completed**: {completed_phases}/4")

    # Phase results
    phase_results = cycle_data.get('phase_results', {})

    # Phase 1: Market Regime & Whale Signals
    if 'regime_detection' in phase_results:
        regime_data = phase_results['regime_detection']
        col1, col2 = st.columns(2)

        with col1:
            st.markdown("**🎯 Market Regime Detection**")
            regime = regime_data.get('regime', 'Unknown')
            confidence = regime_data.get('confidence', 0.0)
            st.success(f"Regime: **{regime.title()}** (Confidence: {confidence:.3f})")

        with col2:
            whale_data = phase_results.get('whale_signals', {})
            raw_signals = whale_data.get('raw_signals', 0)
            processed_signals = whale_data.get('processed_signals', 0)
            st.markdown("**🐋 Whale Signals**")
            st.info(f"Raw: {raw_signals} | Processed: {processed_signals}")

    # Phase 2: Risk Management
    if 'risk_management' in phase_results:
        risk_data = phase_results['risk_management']
        var_metrics = risk_data.get('var_metrics', {})

        col1, col2 = st.columns(2)
        with col1:
            st.markdown("**📈 Risk Metrics**")
            var_95 = var_metrics.get('var_95', 0.0)
            cvar_95 = var_metrics.get('cvar_95', 0.0)
            st.info(f"VaR (95%): {var_95:.4f}")
            st.info(f"CVaR (95%): {cvar_95:.4f}")

        with col2:
            portfolio_risk = risk_data.get('portfolio_risk', {})
            st.markdown("**🛡️ Portfolio Risk**")
            if portfolio_risk:
                risk_level = portfolio_risk.get('risk_level', 'Unknown')
                st.info(f"Risk Level: {risk_level}")

    # Phase 4: Adaptive Weighting
    if 'adaptive_weighting' in phase_results:
        weight_data = phase_results['adaptive_weighting']
        weights = weight_data.get('weights', {})
        selected_strategies = weight_data.get('selected_strategies', [])

        col1, col2 = st.columns(2)
        with col1:
            st.markdown("**⚖️ Strategy Weights**")
            for strategy, weight in weights.items():
                st.info(f"{strategy}: {weight:.3f}")

        with col2:
            st.markdown("**🎯 Selected Strategies**")
            if selected_strategies:
                for strategy in selected_strategies:
                    st.success(f"✅ {strategy}")
            else:
                st.warning("No strategies selected")

def display_performance_charts(historical_cycles):
    """Display performance charts from historical data."""
    if not historical_cycles:
        st.warning("No historical data available for charts")
        return

    st.subheader("📊 Performance Charts")

    # Prepare data for charts
    chart_data = []
    for cycle in historical_cycles:
        cycle_num = cycle.get('cycle_number', 0)
        timestamp = cycle.get('timestamp', '')
        phase_results = cycle.get('phase_results', {})

        # Extract key metrics
        regime_data = phase_results.get('regime_detection', {})
        risk_data = phase_results.get('risk_management', {})
        weight_data = phase_results.get('adaptive_weighting', {})

        var_metrics = risk_data.get('var_metrics', {})

        chart_data.append({
            'cycle': cycle_num,
            'timestamp': timestamp,
            'regime': regime_data.get('regime', 'unknown'),
            'confidence': regime_data.get('confidence', 0.0),
            'var_95': var_metrics.get('var_95', 0.0),
            'cvar_95': var_metrics.get('cvar_95', 0.0),
            'selected_strategies': len(weight_data.get('selected_strategies', []))
        })

    if not chart_data:
        st.warning("No chart data available")
        return

    df = pd.DataFrame(chart_data)

    # Create charts
    col1, col2 = st.columns(2)

    with col1:
        # Regime confidence over time
        fig_confidence = px.line(
            df, x='cycle', y='confidence',
            title='Market Regime Confidence Over Time',
            labels={'cycle': 'Cycle Number', 'confidence': 'Confidence'}
        )
        fig_confidence.update_layout(height=300)
        st.plotly_chart(fig_confidence, use_container_width=True)

    with col2:
        # VaR over time
        fig_var = px.line(
            df, x='cycle', y='var_95',
            title='Portfolio VaR (95%) Over Time',
            labels={'cycle': 'Cycle Number', 'var_95': 'VaR (95%)'}
        )
        fig_var.update_layout(height=300)
        st.plotly_chart(fig_var, use_container_width=True)

    # Strategy selection frequency
    col1, col2 = st.columns(2)

    with col1:
        # Regime distribution
        regime_counts = df['regime'].value_counts()
        fig_regime = px.pie(
            values=regime_counts.values,
            names=regime_counts.index,
            title='Market Regime Distribution'
        )
        fig_regime.update_layout(height=300)
        st.plotly_chart(fig_regime, use_container_width=True)

    with col2:
        # Strategy selection over time
        fig_strategies = px.bar(
            df, x='cycle', y='selected_strategies',
            title='Selected Strategies Per Cycle',
            labels={'cycle': 'Cycle Number', 'selected_strategies': 'Number of Strategies'}
        )
        fig_strategies.update_layout(height=300)
        st.plotly_chart(fig_strategies, use_container_width=True)

def main():
    """Main dashboard function."""
    st.title("📈 Synergy7 Enhanced Trading Dashboard")
    st.markdown("Real-time monitoring of enhanced 4-phase trading system (Live & Paper Trading)")

    # Sidebar controls
    st.sidebar.title("⚙️ Dashboard Controls")
    auto_refresh = st.sidebar.checkbox("Auto-refresh", value=True)
    refresh_interval = st.sidebar.slider("Refresh interval (seconds)", 5, 60, 10)

    if st.sidebar.button("🔄 Refresh Now"):
        st.experimental_rerun()

    # Load data
    cycle_data = load_latest_cycle_data()
    metrics_data = load_trading_metrics()
    historical_cycles = load_historical_cycles()

    # Display session overview
    st.header("📊 Session Overview")
    display_session_overview(metrics_data)

    st.markdown("---")

    # Display current cycle
    display_current_cycle(cycle_data)

    st.markdown("---")

    # Display performance charts
    display_performance_charts(historical_cycles)

    # System status
    st.markdown("---")
    st.header("🔧 System Status")

    col1, col2, col3 = st.columns(3)

    with col1:
        if cycle_data:
            st.success("✅ Trading System Active")
        else:
            st.error("❌ No Trading Data")

    with col2:
        if metrics_data:
            last_update = metrics_data.get('timestamp', '')
            if last_update:
                update_time = datetime.fromisoformat(last_update.replace('Z', '+00:00'))
                st.info(f"Last Update: {update_time.strftime('%H:%M:%S')}")
        else:
            st.warning("⚠️ No Metrics Data")

    with col3:
        cycles_count = len(historical_cycles)
        st.info(f"📊 Historical Cycles: {cycles_count}")

    # Footer
    st.markdown("---")
    st.markdown(f"**Dashboard Updated**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # Auto-refresh
    if auto_refresh:
        time.sleep(refresh_interval)
        st.experimental_rerun()

if __name__ == "__main__":
    main()
