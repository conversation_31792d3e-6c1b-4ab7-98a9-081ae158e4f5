#!/usr/bin/env python3
"""
Simplified test script for Phase 2: Advanced Risk Management.

This script tests the core risk management functionality without external ML dependencies.
"""

import os
import sys
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import yaml
import re

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_test_config():
    """Load test configuration with environment variable substitution."""
    try:
        # Set test environment variables
        os.environ.setdefault('VAR_ENABLED', 'true')
        os.environ.setdefault('CVAR_ENABLED', 'true')
        os.environ.setdefault('POSITION_SIZING_METHOD', 'traditional')  # Use traditional instead of var_based
        os.environ.setdefault('REGIME_BASED_SIZING', 'true')
        
        # Load and process config
        with open('config.yaml', 'r') as f:
            config_text = f.read()
        
        # Simple environment variable substitution
        def replace_env_var(match):
            var_expr = match.group(1)
            if ':-' in var_expr:
                var_name, default_value = var_expr.split(':-', 1)
                return os.environ.get(var_name, default_value)
            else:
                return os.environ.get(var_expr, '')
        
        config_text = re.sub(r'\$\{([^}]+)\}', replace_env_var, config_text)
        config = yaml.safe_load(config_text)
        
        # Convert string values to appropriate types
        def convert_types(obj):
            if isinstance(obj, dict):
                return {k: convert_types(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_types(item) for item in obj]
            elif isinstance(obj, str):
                if obj.lower() == 'true':
                    return True
                elif obj.lower() == 'false':
                    return False
                else:
                    try:
                        if '.' in obj:
                            return float(obj)
                        else:
                            return int(obj)
                    except ValueError:
                        return obj
            return obj
        
        config = convert_types(config)
        return config
    except Exception as e:
        logger.error(f"Error loading config: {str(e)}")
        return {}

def generate_simple_test_data(days=60):
    """Generate simple test data."""
    logger.info(f"Generating {days} days of test data...")
    
    # Generate dates
    dates = pd.date_range(start=datetime.now() - timedelta(days=days), periods=days*24, freq='H')
    
    # Generate synthetic price data
    np.random.seed(42)
    base_price = 25.0
    prices = [base_price]
    volumes = []
    
    for i in range(len(dates) - 1):
        # Generate price change
        change = np.random.normal(0.0001, 0.02)
        new_price = prices[-1] * (1 + change)
        prices.append(max(0.1, new_price))
        
        # Generate volume
        base_volume = 1000000
        volume_change = np.random.normal(0, 0.3)
        volume = base_volume * (1 + volume_change)
        volumes.append(max(100000, volume))
    
    volumes.append(volumes[-1])
    
    # Create OHLCV data
    df = pd.DataFrame({
        'timestamp': dates,
        'open': prices,
        'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
        'close': prices,
        'volume': volumes
    })
    
    # Ensure OHLC consistency
    df['high'] = df[['open', 'close', 'high']].max(axis=1)
    df['low'] = df[['open', 'close', 'low']].min(axis=1)
    
    logger.info(f"Generated test data: {len(df)} rows")
    return df

def test_var_calculator_basic():
    """Test basic VaR calculator functionality."""
    logger.info("Testing VaR Calculator (Basic)...")
    
    try:
        # Import here to catch any issues
        from core.risk.var_calculator import VaRCalculator
        
        # Create simple config
        config = {
            'risk_management': {
                'var_enabled': True,
                'var_confidence_levels': [0.95, 0.99],
                'var_lookback_days': 252,
                'cvar_enabled': True
            }
        }
        
        # Initialize calculator
        calculator = VaRCalculator(config)
        logger.info("VaR Calculator initialized successfully")
        
        # Generate test data
        test_data = generate_simple_test_data(60)
        
        # Calculate returns
        returns = calculator.calculate_returns(test_data)
        logger.info(f"Calculated {len(returns)} returns")
        
        # Test historical VaR
        if len(returns) > 30:
            hist_var = calculator.historical_var(returns, 0.95)
            logger.info(f"Historical VaR (95%): {hist_var:.4f}")
            
            # Test parametric VaR
            param_var = calculator.parametric_var(returns, 0.95)
            logger.info(f"Parametric VaR (95%): {param_var:.4f}")
            
            # Test CVaR
            cvar = calculator.conditional_var(returns, 0.95)
            logger.info(f"Conditional VaR (95%): {cvar:.4f}")
        
        # Test summary
        summary = calculator.get_var_summary()
        logger.info(f"VaR summary: {summary}")
        
        return True
    
    except Exception as e:
        logger.error(f"Error testing VaR calculator: {str(e)}")
        return False

def test_enhanced_position_sizer_basic():
    """Test basic enhanced position sizer functionality."""
    logger.info("Testing Enhanced Position Sizer (Basic)...")
    
    try:
        # Import here to catch any issues
        from core.risk.position_sizer import EnhancedPositionSizer
        
        # Create simple config
        config = {
            'risk_management': {
                'position_sizing_method': 'traditional',  # Avoid VaR-based for now
                'regime_based_sizing': True,
                'max_position_size_pct': 0.1,
                'min_position_size': 0.01,
                'volatility_scaling': True,
                'volatility_lookback': 20
            }
        }
        
        # Initialize sizer
        sizer = EnhancedPositionSizer(config)
        logger.info("Enhanced Position Sizer initialized successfully")
        
        # Generate test data
        test_data = generate_simple_test_data(60)
        account_balance = 100000.0
        
        # Test basic position sizing
        result = sizer.calculate_enhanced_position_size(
            price_data=test_data,
            account_balance=account_balance,
            market="TEST_ASSET",
            signal_strength=0.8,
            market_regime="trending_up"
        )
        
        logger.info(f"Position sizing result:")
        logger.info(f"  Position size: {result['position_size']:.4f}")
        logger.info(f"  Position value: ${result['position_value']:,.2f}")
        logger.info(f"  Market regime: {result['market_regime']}")
        logger.info(f"  Risk adjusted: {result['risk_adjusted']}")
        
        # Test different regimes
        regimes = ["trending_up", "trending_down", "ranging", "volatile", "choppy"]
        for regime in regimes:
            regime_result = sizer.calculate_enhanced_position_size(
                price_data=test_data,
                account_balance=account_balance,
                market="TEST_ASSET",
                signal_strength=1.0,
                market_regime=regime
            )
            logger.info(f"Regime {regime}: position size {regime_result['position_size']:.4f}")
        
        # Test backward compatibility
        compat_result = sizer.calculate_position_size(test_data, account_balance, "TEST_ASSET")
        logger.info(f"Backward compatibility: {compat_result['position_size']:.4f}")
        
        return True
    
    except Exception as e:
        logger.error(f"Error testing enhanced position sizer: {str(e)}")
        return False

def test_portfolio_risk_manager_basic():
    """Test basic portfolio risk manager functionality."""
    logger.info("Testing Portfolio Risk Manager (Basic)...")
    
    try:
        # Import here to catch any issues
        from core.risk.portfolio_risk_manager import PortfolioRiskManager
        
        # Create simple config
        config = {
            'risk_management': {
                'var_enabled': True,
                'correlation_threshold': 0.7,
                'max_correlated_exposure_pct': 0.3,
                'portfolio_var_limit_pct': 0.02,
                'max_position_size_pct': 0.1
            }
        }
        
        # Initialize manager
        manager = PortfolioRiskManager(config)
        logger.info("Portfolio Risk Manager initialized successfully")
        
        # Create test positions
        positions = {
            'ASSET_1': {'size': 1000, 'value': 25000, 'entry_price': 24.5},
            'ASSET_2': {'size': 800, 'value': 24000, 'entry_price': 29.5},
            'ASSET_3': {'size': 1200, 'value': 36000, 'entry_price': 29.8}
        }
        
        # Update positions
        manager.update_positions(positions)
        logger.info(f"Updated positions: {len(positions)} assets")
        
        # Test risk summary
        summary = manager.get_risk_summary()
        logger.info(f"Risk summary: portfolio value ${summary.get('portfolio_value', 0):,.2f}")
        logger.info(f"Position count: {summary.get('position_count', 0)}")
        
        return True
    
    except Exception as e:
        logger.error(f"Error testing portfolio risk manager: {str(e)}")
        return False

def main():
    """Main test function."""
    logger.info("Starting Phase 2 Risk Management Tests (Simplified)...")
    
    # Load configuration
    config = load_test_config()
    if not config:
        logger.error("Failed to load configuration")
        return False
    
    logger.info("Configuration loaded successfully")
    
    # Test results
    results = {}
    
    # Test VaR calculator (basic)
    results['var_calculator_basic'] = test_var_calculator_basic()
    
    # Test enhanced position sizer (basic)
    results['enhanced_position_sizer_basic'] = test_enhanced_position_sizer_basic()
    
    # Test portfolio risk manager (basic)
    results['portfolio_risk_manager_basic'] = test_portfolio_risk_manager_basic()
    
    # Print results summary
    logger.info("\n" + "="*50)
    logger.info("PHASE 2 RISK MANAGEMENT TEST RESULTS (SIMPLIFIED)")
    logger.info("="*50)
    
    all_passed = True
    for test_name, passed in results.items():
        status = "PASS" if passed else "FAIL"
        logger.info(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    logger.info("="*50)
    overall_status = "ALL TESTS PASSED" if all_passed else "SOME TESTS FAILED"
    logger.info(f"Overall Status: {overall_status}")
    logger.info("="*50)
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
