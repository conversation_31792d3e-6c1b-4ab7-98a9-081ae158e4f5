#!/usr/bin/env python3
"""
Production Setup Validation Script for Synergy7 Enhanced Trading System.

This script validates that the production environment is correctly configured
and all components are ready for deployment.
"""

import os
import sys
import asyncio
import logging
import json
import yaml
import re
from pathlib import Path
from typing import Dict, List, Any, Tuple
from datetime import datetime
import httpx

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ProductionValidator:
    """Validates production environment setup."""

    def __init__(self):
        self.project_root = Path.cwd()
        self.validation_results = {}
        self.critical_failures = []
        self.warnings = []

    def load_environment_variables(self) -> bool:
        """Load and validate environment variables."""
        logger.info("Loading environment variables...")

        try:
            env_path = self.project_root / '.env'
            if not env_path.exists():
                logger.error("❌ .env file not found!")
                return False

            # Load environment variables
            with open(env_path, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ[key] = value

            logger.info("✅ Environment variables loaded")
            return True

        except Exception as e:
            logger.error(f"❌ Error loading environment variables: {str(e)}")
            return False

    def validate_api_connectivity(self) -> bool:
        """Validate API connectivity and authentication."""
        logger.info("Validating API connectivity...")

        async def test_apis():
            results = {}

            # Test Helius API
            helius_key = os.environ.get('HELIUS_API_KEY')
            if helius_key:
                try:
                    async with httpx.AsyncClient() as client:
                        response = await client.post(
                            f"https://rpc.helius.xyz/?api-key={helius_key}",
                            json={"jsonrpc": "2.0", "id": 1, "method": "getHealth"},
                            timeout=10
                        )
                        if response.status_code == 200:
                            results['helius'] = True
                            logger.info("✅ Helius API connection successful")
                        else:
                            results['helius'] = False
                            logger.error(f"❌ Helius API failed: {response.status_code}")
                except Exception as e:
                    results['helius'] = False
                    logger.error(f"❌ Helius API error: {str(e)}")
            else:
                results['helius'] = False
                logger.warning("⚠️  Helius API key not configured")

            # Test Birdeye API
            birdeye_key = os.environ.get('BIRDEYE_API_KEY')
            if birdeye_key:
                try:
                    async with httpx.AsyncClient() as client:
                        response = await client.get(
                            "https://public-api.birdeye.so/public/tokenlist",
                            headers={"X-API-KEY": birdeye_key},
                            timeout=10
                        )
                        if response.status_code == 200:
                            results['birdeye'] = True
                            logger.info("✅ Birdeye API connection successful")
                        else:
                            results['birdeye'] = False
                            logger.error(f"❌ Birdeye API failed: {response.status_code}")
                except Exception as e:
                    results['birdeye'] = False
                    logger.error(f"❌ Birdeye API error: {str(e)}")
            else:
                results['birdeye'] = False
                logger.warning("⚠️  Birdeye API key not configured")

            return results

        try:
            api_results = asyncio.run(test_apis())

            # Check if at least one API is working
            if any(api_results.values()):
                logger.info("✅ At least one API connection is working")
                return True
            else:
                logger.error("❌ No API connections are working")
                return False

        except Exception as e:
            logger.error(f"❌ Error testing API connectivity: {str(e)}")
            return False

    def validate_configuration_integrity(self) -> bool:
        """Validate configuration file integrity and completeness."""
        logger.info("Validating configuration integrity...")

        try:
            config_path = self.project_root / 'config.yaml'
            if not config_path.exists():
                logger.error("❌ config.yaml not found!")
                return False

            # Load configuration with environment variable substitution
            with open(config_path, 'r') as f:
                config_text = f.read()

            # Simple environment variable substitution
            def replace_env_var(match):
                var_expr = match.group(1)
                if ':-' in var_expr:
                    var_name, default_value = var_expr.split(':-', 1)
                    return os.environ.get(var_name, default_value)
                else:
                    return os.environ.get(var_expr, '')

            config_text = re.sub(r'\$\{([^}]+)\}', replace_env_var, config_text)
            config = yaml.safe_load(config_text)

            # Validate required sections
            required_sections = {
                'market_regime': ['enabled', 'adaptive_thresholds'],
                'whale_watching': ['enabled', 'whale_confidence_weight'],
                'risk_management': ['var_enabled', 'portfolio_var_limit_pct'],
                'strategy_attribution': ['enabled', 'attribution_window_days'],
                'adaptive_weighting': ['enabled', 'learning_rate']
            }

            missing_configs = []
            for section, required_keys in required_sections.items():
                if section not in config:
                    missing_configs.append(f"Section: {section}")
                else:
                    for key in required_keys:
                        if key not in config[section]:
                            missing_configs.append(f"{section}.{key}")

            if missing_configs:
                logger.error(f"❌ Missing configuration items: {missing_configs}")
                return False

            # Validate configuration values
            validation_errors = []

            # Check risk management limits
            var_limit = config['risk_management'].get('portfolio_var_limit_pct', 0)
            if var_limit <= 0 or var_limit > 0.1:
                validation_errors.append("portfolio_var_limit_pct should be between 0 and 0.1")

            # Check adaptive weighting parameters
            learning_rate = config['adaptive_weighting'].get('learning_rate', 0)
            if learning_rate <= 0 or learning_rate > 0.1:
                validation_errors.append("learning_rate should be between 0 and 0.1")

            if validation_errors:
                logger.error(f"❌ Configuration validation errors: {validation_errors}")
                return False

            logger.info("✅ Configuration integrity validated")
            return True

        except Exception as e:
            logger.error(f"❌ Error validating configuration: {str(e)}")
            return False

    def validate_file_permissions(self) -> bool:
        """Validate file and directory permissions."""
        logger.info("Validating file permissions...")

        try:
            permission_checks = [
                ('data', 0o755, 'directory'),
                ('logs', 0o755, 'directory'),
                ('keys', 0o700, 'directory'),
                ('.env', 0o600, 'file'),
                ('config.yaml', 0o644, 'file')
            ]

            permission_errors = []

            for item, expected_perm, item_type in permission_checks:
                item_path = self.project_root / item
                if item_path.exists():
                    current_perm = oct(item_path.stat().st_mode)[-3:]
                    expected_perm_str = oct(expected_perm)[-3:]

                    if current_perm != expected_perm_str:
                        permission_errors.append(f"{item}: {current_perm} (expected {expected_perm_str})")
                        # Try to fix permissions
                        try:
                            os.chmod(item_path, expected_perm)
                            logger.info(f"🔧 Fixed permissions for {item}")
                        except:
                            pass
                else:
                    if item in ['.env', 'config.yaml']:
                        permission_errors.append(f"{item}: missing")

            if permission_errors:
                logger.warning(f"⚠️  Permission issues (some may be auto-fixed): {permission_errors}")

            logger.info("✅ File permissions validated")
            return True

        except Exception as e:
            logger.error(f"❌ Error validating permissions: {str(e)}")
            return False

    def validate_system_resources(self) -> bool:
        """Validate system resources and requirements."""
        logger.info("Validating system resources...")

        try:
            import psutil

            # Check available memory
            memory = psutil.virtual_memory()
            available_gb = memory.available / (1024**3)

            if available_gb < 1.0:
                logger.warning(f"⚠️  Low available memory: {available_gb:.1f}GB (recommended: 2GB+)")
            else:
                logger.info(f"✅ Available memory: {available_gb:.1f}GB")

            # Check disk space
            disk = psutil.disk_usage('.')
            available_gb = disk.free / (1024**3)

            if available_gb < 5.0:
                logger.warning(f"⚠️  Low disk space: {available_gb:.1f}GB (recommended: 10GB+)")
            else:
                logger.info(f"✅ Available disk space: {available_gb:.1f}GB")

            # Check CPU
            cpu_count = psutil.cpu_count()
            logger.info(f"✅ CPU cores: {cpu_count}")

            return True

        except ImportError:
            logger.warning("⚠️  psutil not installed, skipping system resource check")
            return True
        except Exception as e:
            logger.error(f"❌ Error checking system resources: {str(e)}")
            return False

    def validate_component_imports(self) -> bool:
        """Validate that all enhanced components can be imported."""
        logger.info("Validating component imports...")

        components_to_test = [
            ('core.strategies.market_regime_detector', 'EnhancedMarketRegimeDetector'),
            ('core.strategies.probabilistic_regime', 'ProbabilisticRegimeDetector'),
            ('core.data.whale_signal_generator', 'WhaleSignalGenerator'),
            ('core.signals.whale_signal_processor', 'WhaleSignalProcessor'),
            ('core.risk.var_calculator', 'VaRCalculator'),
            ('core.risk.portfolio_risk_manager', 'PortfolioRiskManager'),
            ('core.risk.position_sizer', 'EnhancedPositionSizer'),
            ('core.analytics.strategy_attribution', 'StrategyAttributionTracker'),
            ('core.analytics.performance_analyzer', 'PerformanceAnalyzer'),
            ('core.strategies.adaptive_weight_manager', 'AdaptiveWeightManager'),
            ('core.strategies.strategy_selector', 'StrategySelector')
        ]

        import_errors = []

        for module_name, class_name in components_to_test:
            try:
                module = __import__(module_name, fromlist=[class_name])
                getattr(module, class_name)
                logger.debug(f"✅ {module_name}.{class_name}")
            except ImportError as e:
                import_errors.append(f"{module_name}: {str(e)}")
                logger.error(f"❌ Failed to import {module_name}.{class_name}")
            except AttributeError as e:
                import_errors.append(f"{module_name}.{class_name}: {str(e)}")
                logger.error(f"❌ Class {class_name} not found in {module_name}")

        if import_errors:
            logger.error(f"❌ Component import errors: {import_errors}")
            return False

        logger.info("✅ All enhanced components can be imported")
        return True

    def run_integration_test(self) -> bool:
        """Run a quick integration test to ensure components work together."""
        logger.info("Running integration test...")

        try:
            # Import the complete integration test
            import test_complete_integration

            # Run a simplified version of the integration test
            config = test_complete_integration.load_integration_config()
            if not config:
                logger.error("❌ Failed to load integration config")
                return False

            # Test basic component initialization
            from core.strategies.market_regime_detector import EnhancedMarketRegimeDetector
            from core.risk.var_calculator import VaRCalculator
            from core.analytics.strategy_attribution import StrategyAttributionTracker
            from core.strategies.adaptive_weight_manager import AdaptiveWeightManager

            # Initialize components
            regime_detector = EnhancedMarketRegimeDetector(config)
            var_calculator = VaRCalculator(config)
            attribution_tracker = StrategyAttributionTracker(config)
            weight_manager = AdaptiveWeightManager(config)

            logger.info("✅ Integration test passed - all components initialized successfully")
            return True

        except Exception as e:
            logger.error(f"❌ Integration test failed: {str(e)}")
            return False

    def generate_validation_report(self) -> Dict[str, Any]:
        """Generate a comprehensive validation report."""
        logger.info("\n" + "="*60)
        logger.info("PRODUCTION VALIDATION REPORT")
        logger.info("="*60)

        validation_tests = [
            ("Environment Variables", self.load_environment_variables),
            ("API Connectivity", self.validate_api_connectivity),
            ("Configuration Integrity", self.validate_configuration_integrity),
            ("File Permissions", self.validate_file_permissions),
            ("System Resources", self.validate_system_resources),
            ("Component Imports", self.validate_component_imports),
            ("Integration Test", self.run_integration_test)
        ]

        results = {}
        passed_tests = 0
        total_tests = len(validation_tests)

        for test_name, test_function in validation_tests:
            logger.info(f"\n📋 Testing: {test_name}")
            try:
                result = test_function()
                results[test_name] = result
                if result:
                    passed_tests += 1
                    logger.info(f"✅ {test_name}: PASSED")
                else:
                    logger.error(f"❌ {test_name}: FAILED")
            except Exception as e:
                results[test_name] = False
                logger.error(f"❌ {test_name}: ERROR - {str(e)}")

        # Generate summary
        success_rate = (passed_tests / total_tests) * 100

        logger.info(f"\n📊 VALIDATION SUMMARY")
        logger.info(f"Tests Passed: {passed_tests}/{total_tests} ({success_rate:.1f}%)")

        if success_rate == 100:
            logger.info("🎉 ALL VALIDATION TESTS PASSED!")
            logger.info("✅ Production environment is ready for deployment")
            status = "READY"
        elif success_rate >= 80:
            logger.warning("⚠️  Most tests passed, but some issues need attention")
            logger.info("🔧 Review warnings and fix issues before deployment")
            status = "NEEDS_ATTENTION"
        else:
            logger.error("❌ VALIDATION FAILED")
            logger.error("💥 Critical issues must be fixed before deployment")
            status = "NOT_READY"

        return {
            "status": status,
            "success_rate": success_rate,
            "passed_tests": passed_tests,
            "total_tests": total_tests,
            "test_results": results,
            "timestamp": datetime.now().isoformat()
        }

def main():
    """Main validation function."""
    logger.info("Starting Synergy7 Production Environment Validation...")

    validator = ProductionValidator()
    report = validator.generate_validation_report()

    # Save validation report
    report_path = Path.cwd() / 'validation_report.json'
    with open(report_path, 'w') as f:
        json.dump(report, f, indent=2)

    logger.info(f"\n📄 Validation report saved to: {report_path}")

    return report["status"] == "READY"

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
