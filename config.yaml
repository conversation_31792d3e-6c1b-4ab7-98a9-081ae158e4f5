# Q5 System Configuration
# Central configuration file for runner and backtester

# System mode
mode:
  live_trading: true
  paper_trading: false
  backtesting: false
  simulation: false

# Solana configuration
solana:
  rpc_url: ${HELIUS_RPC_URL}
  private_rpc_url: ${HELIUS_RPC_URL}
  fallback_rpc_url: ${FALLBACK_RPC_URL}
  commitment: confirmed
  max_retries: 3
  retry_delay: 1.0
  tx_timeout: 30
  provider: "helius"  # Using He<PERSON> as the primary RPC provider

# Wallet configuration
wallet:
  address: ${WALLET_ADDRESS}
  state_sync_interval: 60  # seconds
  position_update_interval: 300  # seconds
  max_positions: 10
  data_dir: "phase_0_env_setup/data"

# Strategy configuration
strategies:
  - name: momentum_sol_usdc
    enabled: true
    params:
      window_size: 20
      threshold: 0.01
      smoothing_factor: 0.1
      max_value: 0.05
      use_filters: true
  - name: wallet_momentum
    enabled: true
    params:
      lookback_period: 24  # hours
      min_wallet_count: 5
      momentum_threshold: 0.1
  - name: alpha_signal_blend
    enabled: false
    params:
      alpha_weight: 0.7
      momentum_weight: 0.3

# Risk management
risk:
  max_position_size_usd: 50000  # Increased to allow larger positions
  max_position_size_pct: 0.5  # 50% of portfolio - use half of wallet balance
  stop_loss_pct: 0.10  # 10% loss
  take_profit_pct: 0.20  # 20% gain
  max_drawdown_pct: 0.15  # 15% max drawdown
  daily_loss_limit_usd: 5000  # Moderate limit for initial testing
  circuit_breaker_enabled: true

# Execution parameters
execution:
  slippage_tolerance: 0.02  # 2% - increased for larger trades
  max_spread_pct: 0.03  # 3% - increased for larger trades
  min_liquidity_usd: 50000  # Increased to ensure sufficient liquidity for larger trades
  order_type: "market"  # market, limit
  retry_failed_orders: true
  max_order_retries: 5  # Increased for better reliability
  use_jito: true
  fallback_enabled: true
  retry_delay: 1.0
  circuit_breaker_enabled: true

# Filter configuration
filters:
  enabled: true
  alpha_wallet:
    enabled: true
    min_wallet_count: 5
    lookback_period: 24  # hours
    momentum_threshold: 0.1
  liquidity_guard:
    enabled: true
    min_liquidity_usd: 50000
    order_book_depth: 10
  volatility_screener:
    enabled: true
    max_volatility: 0.05
    wick_threshold: 0.02

# RL integration
rl_agent:
  enabled: false  # Initially disabled, enable after testing
  data_collection: true
  collection_path: "phase_4_deployment/output/rl_data"

# Backtest configuration
backtest:
  start_date: "2023-01-01"
  end_date: "2023-12-31"
  initial_capital: 10000
  fee_pct: 0.001  # 0.1%
  data_source: "phase_0_env_setup/data/historical"
  output_dir: "phase_2_backtest_engine/output"

# Monitoring and alerts
monitoring:
  enabled: true
  update_interval: 300  # seconds
  telegram_alerts: true
  email_alerts: false
  performance_report_interval: 86400  # daily in seconds
  log_level: "INFO"

# API integrations
apis:
  helius:
    enabled: true
    api_key: ${HELIUS_API_KEY}
    endpoint: "https://api.helius.dev/v0"
    rpc_endpoint: "https://mainnet.helius-rpc.com/?api-key=${HELIUS_API_KEY}"
    ws_endpoint: "wss://mainnet.helius-rpc.com/?api-key=${HELIUS_API_KEY}"
    use_enhanced_apis: true
  coingecko:
    enabled: true
    api_key: ${COINGECKO_API_KEY}
    endpoint: "https://api.coingecko.com/api/v3"
  # Pump.fun integration removed

# Deployment
deployment:
  docker:
    image: "q5system:latest"
    container_name: "q5_trading_bot"
    restart_policy: "unless-stopped"
  quantconnect:
    project_id: "your_project_id"
    backtest_id: "your_backtest_id"
