# Synergy7 System Entry Points

This document provides information about the various entry points for the Synergy7 Trading System and their recommended usage.

## Primary Entry Point (Recommended)

### phase_4_deployment/unified_runner.py

This is the **recommended entry point** for all production deployments. It provides a unified interface for running the Synergy7 Trading System in different operational modes.

**Features:**
- Supports multiple operational modes: live, paper, backtest, and simulation
- Properly initializes monitoring, health checks, and the Streamlit dashboard
- Handles configuration loading and environment variable setup
- Provides graceful shutdown handling

**Usage:**
```bash
# Run in paper trading mode (default)
python phase_4_deployment/unified_runner.py

# Run in live trading mode
python phase_4_deployment/unified_runner.py --mode live

# Run in backtest mode
python phase_4_deployment/unified_runner.py --mode backtest

# Run in simulation mode
python phase_4_deployment/unified_runner.py --mode simulation

# Specify a custom configuration file
python phase_4_deployment/unified_runner.py --config custom_config.yaml
```

## Secondary Entry Points

### run_dashboard.py

This is a standalone entry point for running only the Streamlit dashboard without starting the trading system.

**Usage:**
```bash
python run_dashboard.py
```

## Deprecated Entry Points

The following entry points are deprecated and should not be used for production deployments:

### unified_runner.py (root level)

This is an older version of the unified runner that has been superseded by `phase_4_deployment/unified_runner.py`.

### run_q5_system.py

This script runs the Synergy7 Trading System using the fallback implementation of solana_tx_utils.

### start_live_trading_local.py

This is a local version of the live trading script designed for development and testing on devnet to avoid API rate limits.

## Environment Variables

The following environment variables can be used to configure the system behavior:

- `TRADING_ENABLED`: Set to "true" to enable trading
- `PAPER_TRADING`: Set to "true" to use paper trading (no real funds)
- `BACKTESTING_ENABLED`: Set to "true" to enable backtesting
- `DRY_RUN`: Set to "true" to prevent actual transaction execution

## Configuration Files

The system uses the following configuration files:

- `config.yaml`: Main configuration file
- `.env`: Environment variables for API keys and other secrets

## Recommended Workflow

1. Start with simulation mode to test the system without any real transactions
2. Move to paper trading mode to test with real market data but no real funds
3. Finally, use live trading mode for production with real funds

Always ensure you have thoroughly tested the system in simulation and paper trading modes before using live trading mode.
