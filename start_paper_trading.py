#!/usr/bin/env python3
"""
Synergy7 Enhanced Trading System - Paper Trading Mode

This script starts the enhanced trading system in paper trading mode for testing
all four phases of the integration before going live.
"""

import os
import sys
import asyncio
import logging
import json
import yaml
import re
from pathlib import Path
from datetime import datetime
from typing import Dict, Any

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class PaperTradingRunner:
    """Runs the enhanced trading system in paper trading mode."""

    def __init__(self):
        self.project_root = Path.cwd()
        self.config = None
        self.components = {}
        self.running = False

    def load_config(self) -> bool:
        """Load configuration for paper trading."""
        try:
            # Load environment variables
            env_path = self.project_root / '.env'
            if env_path.exists():
                with open(env_path, 'r') as f:
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith('#') and '=' in line:
                            key, value = line.split('=', 1)
                            os.environ[key] = value

            # Load configuration
            config_path = self.project_root / 'config.yaml'
            with open(config_path, 'r') as f:
                config_text = f.read()

            # Environment variable substitution
            def replace_env_var(match):
                var_expr = match.group(1)
                if ':-' in var_expr:
                    var_name, default_value = var_expr.split(':-', 1)
                    return os.environ.get(var_name, default_value)
                else:
                    return os.environ.get(var_expr, '')

            config_text = re.sub(r'\$\{([^}]+)\}', replace_env_var, config_text)
            self.config = yaml.safe_load(config_text)

            # Convert types
            def convert_types(obj):
                if isinstance(obj, dict):
                    return {k: convert_types(v) for k, v in obj.items()}
                elif isinstance(obj, list):
                    return [convert_types(item) for item in obj]
                elif isinstance(obj, str):
                    if obj.lower() == 'true':
                        return True
                    elif obj.lower() == 'false':
                        return False
                    else:
                        try:
                            if '.' in obj:
                                return float(obj)
                            else:
                                return int(obj)
                        except ValueError:
                            return obj
                return obj

            self.config = convert_types(self.config)

            # Verify paper trading mode
            trading_mode = os.environ.get('TRADING_MODE', 'production')
            if trading_mode != 'paper_trading':
                logger.warning(f"Trading mode is {trading_mode}, switching to paper_trading")
                os.environ['TRADING_MODE'] = 'paper_trading'
                os.environ['PAPER_TRADING'] = 'true'

            logger.info("Configuration loaded successfully for paper trading")
            return True

        except Exception as e:
            logger.error(f"Error loading configuration: {str(e)}")
            return False

    def initialize_components(self) -> bool:
        """Initialize all enhanced trading system components."""
        try:
            logger.info("Initializing enhanced trading system components...")

            # Phase 1: Enhanced Market Regime Detection & Whale Watching
            from core.strategies.market_regime_detector import EnhancedMarketRegimeDetector
            from core.strategies.probabilistic_regime import ProbabilisticRegimeDetector
            from core.data.whale_signal_generator import WhaleSignalGenerator
            from core.signals.whale_signal_processor import WhaleSignalProcessor

            self.components['regime_detector'] = EnhancedMarketRegimeDetector(self.config)
            self.components['probabilistic_regime'] = ProbabilisticRegimeDetector(self.config)
            self.components['whale_generator'] = WhaleSignalGenerator(self.config)
            self.components['whale_processor'] = WhaleSignalProcessor(self.config)

            logger.info("✅ Phase 1 components initialized")

            # Phase 2: Advanced Risk Management
            from core.risk.var_calculator import VaRCalculator
            from core.risk.portfolio_risk_manager import PortfolioRiskManager
            from core.risk.position_sizer import EnhancedPositionSizer

            self.components['var_calculator'] = VaRCalculator(self.config)
            self.components['portfolio_risk_manager'] = PortfolioRiskManager(self.config)
            self.components['position_sizer'] = EnhancedPositionSizer(self.config)

            logger.info("✅ Phase 2 components initialized")

            # Phase 3: Strategy Performance Attribution
            from core.analytics.strategy_attribution import StrategyAttributionTracker
            from core.analytics.performance_analyzer import PerformanceAnalyzer

            self.components['attribution_tracker'] = StrategyAttributionTracker(self.config)
            self.components['performance_analyzer'] = PerformanceAnalyzer(self.config)

            logger.info("✅ Phase 3 components initialized")

            # Phase 4: Adaptive Strategy Weighting
            from core.strategies.adaptive_weight_manager import AdaptiveWeightManager
            from core.strategies.strategy_selector import StrategySelector

            self.components['weight_manager'] = AdaptiveWeightManager(self.config)
            self.components['strategy_selector'] = StrategySelector(self.config)

            logger.info("✅ Phase 4 components initialized")

            logger.info("🎉 All enhanced components initialized successfully!")
            return True

        except Exception as e:
            logger.error(f"Error initializing components: {str(e)}")
            return False

    async def send_telegram_alert(self, message: str) -> bool:
        """Send Telegram alert."""
        try:
            import httpx

            bot_token = os.environ.get('TELEGRAM_BOT_TOKEN')
            chat_id = os.environ.get('TELEGRAM_CHAT_ID')

            if not bot_token or not chat_id:
                logger.warning("Telegram credentials not configured")
                return False

            url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
            payload = {
                "chat_id": chat_id,
                "text": message,
                "parse_mode": "Markdown"
            }

            async with httpx.AsyncClient() as client:
                response = await client.post(url, json=payload)
                response.raise_for_status()
                logger.info("Telegram alert sent successfully")
                return True

        except Exception as e:
            logger.error(f"Error sending Telegram alert: {str(e)}")
            return False

    async def run_paper_trading_cycle(self) -> Dict[str, Any]:
        """Run one complete paper trading cycle."""
        try:
            cycle_results = {
                'timestamp': datetime.now().isoformat(),
                'regime_detection': {},
                'whale_signals': {},
                'risk_metrics': {},
                'strategy_performance': {},
                'adaptive_weights': {},
                'selected_strategies': []
            }

            # Generate sample market data for testing
            import pandas as pd
            import numpy as np

            # Create sample OHLCV data
            dates = pd.date_range(start=datetime.now(), periods=100, freq='1min')
            np.random.seed(42)

            base_price = 25.0
            prices = [base_price]
            for i in range(99):
                change = np.random.normal(0, 0.02)
                new_price = prices[-1] * (1 + change)
                prices.append(max(0.1, new_price))

            market_data = pd.DataFrame({
                'timestamp': dates,
                'open': prices,
                'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
                'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
                'close': prices,
                'volume': [1000000 * (1 + np.random.normal(0, 0.3)) for _ in prices]
            })

            # Ensure OHLC consistency
            market_data['high'] = market_data[['open', 'close', 'high']].max(axis=1)
            market_data['low'] = market_data[['open', 'close', 'low']].min(axis=1)

            # Step 1: Market Regime Detection
            regime_detector = self.components['regime_detector']
            regime, metrics, probabilities = regime_detector.detect_regime(market_data)

            cycle_results['regime_detection'] = {
                'regime': regime.value,
                'confidence': probabilities.get(regime.value, 0.0),
                'metrics': metrics
            }

            logger.info(f"📊 Detected regime: {regime.value} (confidence: {probabilities.get(regime.value, 0.0):.3f})")

            # Step 2: Whale Signal Generation (simulated)
            whale_generator = self.components['whale_generator']
            whale_signals = await whale_generator.update_whale_signals()

            cycle_results['whale_signals'] = {
                'signal_count': len(whale_signals),
                'signals': list(whale_signals.keys())
            }

            logger.info(f"🐋 Generated {len(whale_signals)} whale signals")

            # Step 3: Risk Management
            var_calculator = self.components['var_calculator']
            returns = var_calculator.calculate_returns(market_data)
            var_metrics = var_calculator.calculate_historical_var(returns)

            cycle_results['risk_metrics'] = {
                'var_95': var_metrics.get('var', 0.0),
                'cvar_95': var_metrics.get('cvar', 0.0),
                'volatility': np.std(returns) if len(returns) > 1 else 0.0
            }

            logger.info(f"📈 Risk metrics - VaR: {var_metrics.get('var', 0.0):.4f}, CVaR: {var_metrics.get('cvar', 0.0):.4f}")

            # Step 4: Strategy Performance (simulated)
            attribution_tracker = self.components['attribution_tracker']

            # Simulate some strategy performance
            mock_strategies = ['momentum_strategy', 'mean_reversion', 'breakout_strategy']
            strategy_performance = {}

            for strategy in mock_strategies:
                # Simulate performance metrics
                performance = {
                    'total_trades': np.random.randint(10, 30),
                    'net_pnl': np.random.normal(0.02, 0.01),
                    'win_rate': np.random.uniform(0.4, 0.7),
                    'sharpe_ratio': np.random.normal(0.8, 0.3),
                    'max_drawdown': -abs(np.random.normal(0.05, 0.02)),
                    'volatility': np.random.uniform(0.01, 0.03),
                    'recent_pnl_7d': np.random.normal(0.005, 0.003)
                }
                strategy_performance[strategy] = performance

            cycle_results['strategy_performance'] = strategy_performance

            # Step 5: Adaptive Weight Management
            weight_manager = self.components['weight_manager']
            adaptive_weights = weight_manager.update_weights(
                strategy_performance, regime.value, force_update=True
            )

            cycle_results['adaptive_weights'] = adaptive_weights

            logger.info(f"⚖️  Adaptive weights: {adaptive_weights}")

            # Step 6: Strategy Selection
            strategy_selector = self.components['strategy_selector']

            # Register strategies
            for strategy in mock_strategies:
                strategy_config = {
                    'enabled': True,
                    'min_confidence': 0.5,
                    'preferred_regimes': ['trending_up', 'ranging'],
                    'risk_level': 'medium'
                }
                strategy_selector.register_strategy(strategy, strategy_config)

            selected_strategies = strategy_selector.select_strategies(
                regime.value, probabilities.get(regime.value, 0.8),
                adaptive_weights, strategy_performance
            )

            cycle_results['selected_strategies'] = [
                {
                    'strategy': s['strategy_name'],
                    'allocation': s['effective_allocation'],
                    'suitability': s['suitability_score']
                }
                for s in selected_strategies
            ]

            logger.info(f"🎯 Selected {len(selected_strategies)} strategies for trading")

            return cycle_results

        except Exception as e:
            logger.error(f"Error in paper trading cycle: {str(e)}")
            return {}

    async def run_paper_trading(self, duration_minutes: int = 60) -> bool:
        """Run paper trading for specified duration."""
        try:
            logger.info(f"🚀 Starting paper trading for {duration_minutes} minutes...")

            # Send startup notification
            startup_message = f"""
🚀 *Synergy7 Enhanced Trading System - Paper Trading Started*

*Mode*: Paper Trading
*Duration*: {duration_minutes} minutes
*Start Time*: {datetime.now().isoformat()}

*Enhanced Features Active*:
✅ Market Regime Detection
✅ Whale Signal Monitoring
✅ Advanced Risk Management (VaR/CVaR)
✅ Strategy Performance Attribution
✅ Adaptive Strategy Weighting

The system will run trading cycles and report performance.
"""

            await self.send_telegram_alert(startup_message)

            self.running = True
            cycle_count = 0
            total_cycles = max(1, duration_minutes // 5)  # Run cycle every 5 minutes

            all_results = []

            while self.running and cycle_count < total_cycles:
                cycle_count += 1
                logger.info(f"📊 Running paper trading cycle {cycle_count}/{total_cycles}")

                # Run trading cycle
                cycle_results = await self.run_paper_trading_cycle()

                if cycle_results:
                    all_results.append(cycle_results)

                    # Log cycle summary
                    regime = cycle_results.get('regime_detection', {}).get('regime', 'unknown')
                    selected_count = len(cycle_results.get('selected_strategies', []))
                    var_95 = cycle_results.get('risk_metrics', {}).get('var_95', 0.0)

                    logger.info(f"✅ Cycle {cycle_count} complete - Regime: {regime}, Strategies: {selected_count}, VaR: {var_95:.4f}")

                    # Send periodic update
                    if cycle_count % 3 == 0:  # Every 3 cycles
                        update_message = f"""
📊 *Paper Trading Update - Cycle {cycle_count}/{total_cycles}*

*Current Status*:
• Market Regime: {regime}
• Active Strategies: {selected_count}
• Portfolio VaR (95%): {var_95:.4f}

*System Health*: All components operational ✅
"""
                        await self.send_telegram_alert(update_message)

                # Wait before next cycle (5 minutes)
                if cycle_count < total_cycles:
                    logger.info("⏳ Waiting 5 minutes before next cycle...")
                    await asyncio.sleep(300)  # 5 minutes

            # Generate final summary
            if all_results:
                avg_var = np.mean([r.get('risk_metrics', {}).get('var_95', 0) for r in all_results])
                total_strategies = sum([len(r.get('selected_strategies', [])) for r in all_results])

                summary_message = f"""
🎉 *Paper Trading Session Complete*

*Summary*:
• Total Cycles: {len(all_results)}
• Average VaR: {avg_var:.4f}
• Total Strategy Selections: {total_strategies}
• Duration: {duration_minutes} minutes

*All Enhanced Features Tested Successfully* ✅

Ready for live trading deployment!
"""
                await self.send_telegram_alert(summary_message)

            logger.info("🎉 Paper trading session completed successfully!")
            return True

        except Exception as e:
            logger.error(f"Error in paper trading: {str(e)}")

            error_message = f"""
❌ *Paper Trading Error*

An error occurred during paper trading:
{str(e)}

Please check logs and restart the system.
"""
            await self.send_telegram_alert(error_message)
            return False

    async def start(self, duration_minutes: int = 60) -> bool:
        """Start the paper trading system."""
        logger.info("="*60)
        logger.info("SYNERGY7 ENHANCED TRADING SYSTEM - PAPER TRADING MODE")
        logger.info("="*60)

        # Load configuration
        if not self.load_config():
            logger.error("Failed to load configuration")
            return False

        # Initialize components
        if not self.initialize_components():
            logger.error("Failed to initialize components")
            return False

        # Run paper trading
        success = await self.run_paper_trading(duration_minutes)

        if success:
            logger.info("✅ Paper trading completed successfully")
        else:
            logger.error("❌ Paper trading failed")

        return success

async def main():
    """Main function."""
    import argparse

    parser = argparse.ArgumentParser(description='Synergy7 Enhanced Trading System - Paper Trading')
    parser.add_argument('--duration', type=int, default=60, help='Duration in minutes (default: 60)')
    args = parser.parse_args()

    runner = PaperTradingRunner()
    success = await runner.start(args.duration)

    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
