#!/usr/bin/env python3
"""
Centralized Configuration Loader for Synergy7 Trading System

This module provides a robust, centralized configuration loader that handles
environment variables, type conversion, and configuration validation.
It is designed to be the single source of truth for all configuration in the system.
"""

import os
import yaml
import json
import logging
from typing import Dict, List, Any, Optional, Union, Callable, TypeVar, cast
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("synergy7.config_loader")

# Type variable for generic return type
T = TypeVar('T')

class ConfigError(Exception):
    """Exception raised for configuration errors."""
    pass

class ConfigLoader:
    """
    Centralized configuration loader for Synergy7 Trading System.
    
    This class provides methods for loading, validating, and accessing configuration
    from various sources, including YAML files and environment variables.
    """
    
    def __init__(self, config_dir: str = "config"):
        """
        Initialize the ConfigLoader.
        
        Args:
            config_dir: Directory containing configuration files
        """
        self.config_dir = config_dir
        self.config: Dict[str, Any] = {}
        self.env_vars: Dict[str, str] = {}
        self.default_config_path = os.path.join(config_dir, "default.yaml")
        self.environments_dir = os.path.join(config_dir, "environments")
        self.components_dir = os.path.join(config_dir, "components")
        self.phases_dir = os.path.join(config_dir, "phases")
        
        # Load environment variables
        self._load_env_vars()
        
        logger.info(f"Initialized ConfigLoader with config_dir={config_dir}")
    
    def _load_env_vars(self) -> None:
        """Load environment variables into a dictionary."""
        self.env_vars = dict(os.environ)
        
        # Convert boolean environment variables
        for key, value in self.env_vars.items():
            if value.lower() in ["true", "yes", "1", "on"]:
                self.env_vars[key] = "true"
            elif value.lower() in ["false", "no", "0", "off"]:
                self.env_vars[key] = "false"
        
        logger.debug(f"Loaded {len(self.env_vars)} environment variables")
    
    def load_config(self, environment: Optional[str] = None, 
                   components: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Load configuration from files and environment variables.
        
        Args:
            environment: Environment name (e.g., 'production', 'development')
            components: List of component names to load configuration for
            
        Returns:
            Merged configuration dictionary
        """
        # Load default configuration
        self.config = self._load_yaml_file(self.default_config_path) or {}
        
        # Load environment-specific configuration
        if environment:
            env_path = os.path.join(self.environments_dir, f"{environment}.yaml")
            env_config = self._load_yaml_file(env_path)
            if env_config:
                self.config = self._merge_configs(self.config, env_config)
        
        # Load component-specific configurations
        if components:
            for component in components:
                component_path = os.path.join(self.components_dir, f"{component}.yaml")
                component_config = self._load_yaml_file(component_path)
                if component_config:
                    self.config = self._merge_configs(self.config, component_config)
        
        # Set default values for critical configuration
        self._set_defaults()
        
        logger.info(f"Loaded configuration for environment={environment}, components={components}")
        return self.config
    
    def _set_defaults(self) -> None:
        """Set default values for critical configuration."""
        # Set default values for critical boolean flags
        self.config.setdefault("mode", {})
        self.config["mode"].setdefault("dry_run", True)
        self.config["mode"].setdefault("paper_trading", True)
        self.config["mode"].setdefault("live_trading", False)
        
        # Set default values for API configuration
        self.config.setdefault("apis", {})
        for api in ["helius", "birdeye", "jito"]:
            self.config["apis"].setdefault(api, {})
            self.config["apis"][api].setdefault("enabled", False)
        
        # Set default values for risk management
        self.config.setdefault("risk_management", {})
        self.config["risk_management"].setdefault("enabled", True)
        self.config["risk_management"].setdefault("max_position_size", 0.01)
        self.config["risk_management"].setdefault("max_exposure", 0.1)
        
        # Set default values for monitoring
        self.config.setdefault("monitoring", {})
        self.config["monitoring"].setdefault("enabled", True)
        self.config["monitoring"].setdefault("log_level", "INFO")
        
        # Set default values for circuit breaker
        self.config.setdefault("deployment", {})
        self.config["deployment"].setdefault("circuit_breaker", {})
        self.config["deployment"]["circuit_breaker"].setdefault("enabled", True)
        self.config["deployment"]["circuit_breaker"].setdefault("failure_threshold", 5)
        self.config["deployment"]["circuit_breaker"].setdefault("reset_timeout_seconds", 300)
        
        # Set default values for retry policy
        self.config["deployment"].setdefault("retry_policy", {})
        self.config["deployment"]["retry_policy"].setdefault("max_retries", 3)
        self.config["deployment"]["retry_policy"].setdefault("backoff_factor", 2)
        self.config["deployment"]["retry_policy"].setdefault("max_backoff_seconds", 30)
    
    def _load_yaml_file(self, file_path: str) -> Optional[Dict[str, Any]]:
        """
        Load a YAML file with environment variable substitution.
        
        Args:
            file_path: Path to the YAML file
            
        Returns:
            Dictionary containing the YAML file contents or None if the file doesn't exist
        """
        try:
            with open(file_path, 'r') as f:
                content = f.read()
                
                # Replace environment variables
                for key, value in self.env_vars.items():
                    placeholder = f"${{{key}}}"
                    if placeholder in content:
                        content = content.replace(placeholder, value)
                
                # Parse YAML
                config = yaml.safe_load(content)
                logger.debug(f"Loaded configuration from {file_path}")
                return config
        except FileNotFoundError:
            logger.warning(f"Configuration file not found: {file_path}")
            return None
        except yaml.YAMLError as e:
            logger.error(f"Error parsing YAML file {file_path}: {e}")
            return None
        except Exception as e:
            logger.error(f"Error loading configuration file {file_path}: {e}")
            return None
    
    def _merge_configs(self, base_config: Dict[str, Any], 
                      override_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Merge two configuration dictionaries.
        
        Args:
            base_config: Base configuration dictionary
            override_config: Override configuration dictionary
            
        Returns:
            Merged configuration dictionary
        """
        result = base_config.copy()
        
        for key, value in override_config.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                # Recursively merge dictionaries
                result[key] = self._merge_configs(result[key], value)
            else:
                # Override or add value
                result[key] = value
        
        return result
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        Get a value from the configuration.
        
        Args:
            key: Key to get (can be nested using dots, e.g., "section.subsection.key")
            default: Default value to return if the key is not found
            
        Returns:
            Value from the configuration or default if not found
        """
        return self.get_nested(self.config, key, default)
    
    def get_bool(self, key: str, default: bool = False) -> bool:
        """
        Get a boolean value from the configuration.
        
        Args:
            key: Key to get (can be nested using dots, e.g., "section.subsection.key")
            default: Default value to return if the key is not found
            
        Returns:
            Boolean value from the configuration or default if not found
        """
        value = self.get(key, default)
        if isinstance(value, bool):
            return value
        
        if isinstance(value, str):
            return value.lower() in ["true", "yes", "1", "on"]
        
        if isinstance(value, int):
            return value != 0
        
        return bool(value)
    
    def get_int(self, key: str, default: int = 0) -> int:
        """
        Get an integer value from the configuration.
        
        Args:
            key: Key to get (can be nested using dots, e.g., "section.subsection.key")
            default: Default value to return if the key is not found
            
        Returns:
            Integer value from the configuration or default if not found
        """
        value = self.get(key, default)
        if isinstance(value, int):
            return value
        
        try:
            return int(value)
        except (ValueError, TypeError):
            return default
    
    def get_float(self, key: str, default: float = 0.0) -> float:
        """
        Get a float value from the configuration.
        
        Args:
            key: Key to get (can be nested using dots, e.g., "section.subsection.key")
            default: Default value to return if the key is not found
            
        Returns:
            Float value from the configuration or default if not found
        """
        value = self.get(key, default)
        if isinstance(value, float):
            return value
        
        try:
            return float(value)
        except (ValueError, TypeError):
            return default
    
    def get_str(self, key: str, default: str = "") -> str:
        """
        Get a string value from the configuration.
        
        Args:
            key: Key to get (can be nested using dots, e.g., "section.subsection.key")
            default: Default value to return if the key is not found
            
        Returns:
            String value from the configuration or default if not found
        """
        value = self.get(key, default)
        if value is None:
            return default
        
        return str(value)
    
    def get_list(self, key: str, default: Optional[List[Any]] = None) -> List[Any]:
        """
        Get a list value from the configuration.
        
        Args:
            key: Key to get (can be nested using dots, e.g., "section.subsection.key")
            default: Default value to return if the key is not found
            
        Returns:
            List value from the configuration or default if not found
        """
        if default is None:
            default = []
        
        value = self.get(key, default)
        if isinstance(value, list):
            return value
        
        # Try to convert to list if it's a string
        if isinstance(value, str):
            try:
                # Try to parse as JSON
                parsed = json.loads(value)
                if isinstance(parsed, list):
                    return parsed
            except json.JSONDecodeError:
                # If it's a comma-separated string, split it
                return [item.strip() for item in value.split(",")]
        
        # If all else fails, return a list with the value as its only item
        return [value]
    
    def get_dict(self, key: str, default: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Get a dictionary value from the configuration.
        
        Args:
            key: Key to get (can be nested using dots, e.g., "section.subsection.key")
            default: Default value to return if the key is not found
            
        Returns:
            Dictionary value from the configuration or default if not found
        """
        if default is None:
            default = {}
        
        value = self.get(key, default)
        if isinstance(value, dict):
            return value
        
        # Try to convert to dict if it's a string
        if isinstance(value, str):
            try:
                # Try to parse as JSON
                parsed = json.loads(value)
                if isinstance(parsed, dict):
                    return parsed
            except json.JSONDecodeError:
                pass
        
        return default
    
    @staticmethod
    def get_nested(data: Dict[str, Any], key: str, default: Any = None) -> Any:
        """
        Get a nested value from a dictionary using a dot-separated key.
        
        Args:
            data: Dictionary to get the value from
            key: Dot-separated key (e.g., "section.subsection.key")
            default: Default value to return if the key is not found
            
        Returns:
            Value from the dictionary or default if not found
        """
        if not data:
            return default
        
        if "." not in key:
            return data.get(key, default)
        
        parts = key.split(".")
        current = data
        
        for part in parts:
            if not isinstance(current, dict) or part not in current:
                return default
            current = current[part]
        
        return current
    
    def get_env(self, key: str, default: str = "") -> str:
        """
        Get an environment variable.
        
        Args:
            key: Environment variable name
            default: Default value to return if the environment variable is not set
            
        Returns:
            Environment variable value or default if not set
        """
        return self.env_vars.get(key, default)
    
    def get_env_bool(self, key: str, default: bool = False) -> bool:
        """
        Get a boolean environment variable.
        
        Args:
            key: Environment variable name
            default: Default value to return if the environment variable is not set
            
        Returns:
            Boolean value of the environment variable or default if not set
        """
        value = self.get_env(key, "")
        if not value:
            return default
        
        return value.lower() in ["true", "yes", "1", "on"]
    
    def get_env_int(self, key: str, default: int = 0) -> int:
        """
        Get an integer environment variable.
        
        Args:
            key: Environment variable name
            default: Default value to return if the environment variable is not set
            
        Returns:
            Integer value of the environment variable or default if not set
        """
        value = self.get_env(key, "")
        if not value:
            return default
        
        try:
            return int(value)
        except ValueError:
            return default
    
    def get_env_float(self, key: str, default: float = 0.0) -> float:
        """
        Get a float environment variable.
        
        Args:
            key: Environment variable name
            default: Default value to return if the environment variable is not set
            
        Returns:
            Float value of the environment variable or default if not set
        """
        value = self.get_env(key, "")
        if not value:
            return default
        
        try:
            return float(value)
        except ValueError:
            return default

# Global instance
_config_loader: Optional[ConfigLoader] = None

def get_config_loader(config_dir: str = "config") -> ConfigLoader:
    """
    Get the global ConfigLoader instance.
    
    Args:
        config_dir: Directory containing configuration files
        
    Returns:
        ConfigLoader instance
    """
    global _config_loader
    if _config_loader is None:
        _config_loader = ConfigLoader(config_dir)
    
    return _config_loader

def load_config(environment: Optional[str] = None, 
               components: Optional[List[str]] = None) -> Dict[str, Any]:
    """
    Load configuration using the global ConfigLoader instance.
    
    Args:
        environment: Environment name (e.g., 'production', 'development')
        components: List of component names to load configuration for
        
    Returns:
        Merged configuration dictionary
    """
    return get_config_loader().load_config(environment, components)
