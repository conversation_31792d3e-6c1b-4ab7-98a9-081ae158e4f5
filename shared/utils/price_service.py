"""
Price Service for Synergy7 System.

This module provides a centralized service for fetching and caching price data
from multiple sources with automatic fallback mechanisms.
"""

import os
import time
import json
import logging
import asyncio
from typing import Dict, Any, List, Optional, Tuple, Callable
from datetime import datetime, timedelta

import httpx

# Configure logging
logger = logging.getLogger(__name__)

class PriceSource:
    """Base class for price data sources."""
    
    def __init__(self, name: str, priority: int = 1):
        """
        Initialize a price source.
        
        Args:
            name: Name of the price source
            priority: Priority of the source (lower is higher priority)
        """
        self.name = name
        self.priority = priority
        self.last_success = 0
        self.last_failure = 0
        self.success_count = 0
        self.failure_count = 0
        self.http_client = httpx.AsyncClient(timeout=10.0)
        
    async def get_price(self, token_address: str) -> Optional[float]:
        """
        Get price for a token.
        
        Args:
            token_address: Token address
            
        Returns:
            Price in USD or None if not available
        """
        raise NotImplementedError("Subclasses must implement get_price")
    
    async def get_token_info(self, token_address: str) -> Optional[Dict[str, Any]]:
        """
        Get token information.
        
        Args:
            token_address: Token address
            
        Returns:
            Token information or None if not available
        """
        raise NotImplementedError("Subclasses must implement get_token_info")
    
    def record_success(self) -> None:
        """Record a successful API call."""
        self.last_success = time.time()
        self.success_count += 1
        
    def record_failure(self) -> None:
        """Record a failed API call."""
        self.last_failure = time.time()
        self.failure_count += 1
        
    async def close(self) -> None:
        """Close the HTTP client."""
        await self.http_client.aclose()


class BirdeyePriceSource(PriceSource):
    """Birdeye price source."""
    
    def __init__(self, api_key: str, priority: int = 1):
        """
        Initialize Birdeye price source.
        
        Args:
            api_key: Birdeye API key
            priority: Priority of the source
        """
        super().__init__("birdeye", priority)
        self.api_key = api_key
        self.base_url = "https://public-api.birdeye.so"
        self.headers = {
            "X-API-KEY": api_key,
            "Content-Type": "application/json"
        }
        self.cache = {}
        self.cache_expiry = {}
        self.cache_ttl = 300  # 5 minutes
        
    async def get_price(self, token_address: str) -> Optional[float]:
        """
        Get price for a token from Birdeye.
        
        Args:
            token_address: Token address
            
        Returns:
            Price in USD or None if not available
        """
        try:
            # Check cache
            if token_address in self.cache and time.time() - self.cache_expiry.get(token_address, 0) < self.cache_ttl:
                return self.cache[token_address]
            
            # Get price from API
            url = f"{self.base_url}/public/price"
            params = {"address": token_address}
            
            response = await self.http_client.get(url, headers=self.headers, params=params)
            response.raise_for_status()
            
            data = response.json()
            price = data.get("data", {}).get("value")
            
            if price is not None:
                # Cache the price
                self.cache[token_address] = price
                self.cache_expiry[token_address] = time.time()
                self.record_success()
                return price
            
            self.record_failure()
            return None
        except Exception as e:
            logger.error(f"Error getting price from Birdeye: {str(e)}")
            self.record_failure()
            return None
    
    async def get_token_info(self, token_address: str) -> Optional[Dict[str, Any]]:
        """
        Get token information from Birdeye.
        
        Args:
            token_address: Token address
            
        Returns:
            Token information or None if not available
        """
        try:
            url = f"{self.base_url}/public/token_overview"
            params = {"address": token_address}
            
            response = await self.http_client.get(url, headers=self.headers, params=params)
            response.raise_for_status()
            
            data = response.json()
            self.record_success()
            return data.get("data", {})
        except Exception as e:
            logger.error(f"Error getting token info from Birdeye: {str(e)}")
            self.record_failure()
            return None


class PythPriceSource(PriceSource):
    """Pyth Network price source."""
    
    def __init__(self, priority: int = 2):
        """
        Initialize Pyth Network price source.
        
        Args:
            priority: Priority of the source
        """
        super().__init__("pyth", priority)
        self.base_url = "https://xc-mainnet.pyth.network"
        self.cache = {}
        self.cache_expiry = {}
        self.cache_ttl = 300  # 5 minutes
        self.token_mapping = self._load_token_mapping()
        
    def _load_token_mapping(self) -> Dict[str, str]:
        """
        Load token mapping from Solana addresses to Pyth price feed IDs.
        
        Returns:
            Dictionary mapping token addresses to Pyth price feed IDs
        """
        # This would be loaded from a file or database in production
        return {
            "So11111111111111111111111111111111111111112": "0xef0d8b6fda2ceba41da15d4095d1da392a0d2f8ed0c6c7bc0f4cfac8c280b56d",  # SOL
            "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v": "0xeaa020c61cc479712813461ce153894a96a6c00b21ed0cfc2798d1f9a9e9c94a",  # USDC
            # Add more mappings as needed
        }
        
    async def get_price(self, token_address: str) -> Optional[float]:
        """
        Get price for a token from Pyth Network.
        
        Args:
            token_address: Token address
            
        Returns:
            Price in USD or None if not available
        """
        try:
            # Check cache
            if token_address in self.cache and time.time() - self.cache_expiry.get(token_address, 0) < self.cache_ttl:
                return self.cache[token_address]
            
            # Get Pyth price feed ID
            price_feed_id = self.token_mapping.get(token_address)
            if not price_feed_id:
                logger.warning(f"No Pyth price feed ID found for token {token_address}")
                return None
            
            # Get price from API
            url = f"{self.base_url}/api/latest_price_feeds?ids[]={price_feed_id}"
            
            response = await self.http_client.get(url)
            response.raise_for_status()
            
            data = response.json()
            if data and len(data) > 0:
                price_info = data[0]
                price = price_info.get("price", {}).get("price")
                
                if price is not None:
                    # Convert from Pyth's format (may need adjustment)
                    price = float(price)
                    
                    # Cache the price
                    self.cache[token_address] = price
                    self.cache_expiry[token_address] = time.time()
                    self.record_success()
                    return price
            
            self.record_failure()
            return None
        except Exception as e:
            logger.error(f"Error getting price from Pyth: {str(e)}")
            self.record_failure()
            return None
    
    async def get_token_info(self, token_address: str) -> Optional[Dict[str, Any]]:
        """
        Get token information from Pyth Network.
        
        Args:
            token_address: Token address
            
        Returns:
            Token information or None if not available
        """
        # Pyth doesn't provide detailed token info, so we'll return a minimal set
        price = await self.get_price(token_address)
        if price is not None:
            return {
                "price": price,
                "source": "pyth"
            }
        return None


class PriceService:
    """Centralized price service with fallback mechanisms."""
    
    _instance = None
    
    @classmethod
    def get_instance(cls) -> 'PriceService':
        """
        Get singleton instance of PriceService.
        
        Returns:
            PriceService instance
        """
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    def __init__(self):
        """Initialize the price service."""
        self.sources = []
        self.token_addresses = {
            "SOL": "So11111111111111111111111111111111111111112",
            "USDC": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
            # Add more tokens as needed
        }
        self.cache = {}
        self.cache_expiry = {}
        self.cache_ttl = 300  # 5 minutes
        self.initialized = False
        
    async def initialize(self) -> None:
        """Initialize price sources."""
        if self.initialized:
            return
        
        # Add Birdeye price source
        birdeye_api_key = os.environ.get("BIRDEYE_API_KEY", "a2679724762a47b58dde41b20fb55ce9")
        self.add_source(BirdeyePriceSource(birdeye_api_key, priority=1))
        
        # Add Pyth price source
        self.add_source(PythPriceSource(priority=2))
        
        # Sort sources by priority
        self.sources.sort(key=lambda s: s.priority)
        
        self.initialized = True
        
    def add_source(self, source: PriceSource) -> None:
        """
        Add a price source.
        
        Args:
            source: Price source to add
        """
        self.sources.append(source)
        
    async def get_price(self, token: str) -> Optional[float]:
        """
        Get price for a token with fallback.
        
        Args:
            token: Token symbol or address
            
        Returns:
            Price in USD or None if not available
        """
        if not self.initialized:
            await self.initialize()
        
        # Get token address if symbol provided
        token_address = self.token_addresses.get(token, token)
        
        # Check cache
        cache_key = f"price_{token_address}"
        if cache_key in self.cache and time.time() - self.cache_expiry.get(cache_key, 0) < self.cache_ttl:
            return self.cache[cache_key]
        
        # Try each source in order of priority
        for source in self.sources:
            price = await source.get_price(token_address)
            if price is not None:
                # Cache the price
                self.cache[cache_key] = price
                self.cache_expiry[cache_key] = time.time()
                return price
        
        # If we get here, all sources failed
        logger.warning(f"Failed to get price for token {token} from any source")
        return None
    
    async def get_token_info(self, token: str) -> Optional[Dict[str, Any]]:
        """
        Get token information with fallback.
        
        Args:
            token: Token symbol or address
            
        Returns:
            Token information or None if not available
        """
        if not self.initialized:
            await self.initialize()
        
        # Get token address if symbol provided
        token_address = self.token_addresses.get(token, token)
        
        # Check cache
        cache_key = f"info_{token_address}"
        if cache_key in self.cache and time.time() - self.cache_expiry.get(cache_key, 0) < self.cache_ttl:
            return self.cache[cache_key]
        
        # Try each source in order of priority
        for source in self.sources:
            info = await source.get_token_info(token_address)
            if info is not None:
                # Cache the info
                self.cache[cache_key] = info
                self.cache_expiry[cache_key] = time.time()
                return info
        
        # If we get here, all sources failed
        logger.warning(f"Failed to get token info for {token} from any source")
        return None
    
    async def get_sol_price_usd(self) -> float:
        """
        Get SOL price in USD with fallback to default.
        
        Returns:
            SOL price in USD (defaults to 25.0 if unavailable)
        """
        price = await self.get_price("SOL")
        return price if price is not None else 25.0
    
    async def get_usdc_price_usd(self) -> float:
        """
        Get USDC price in USD with fallback to default.
        
        Returns:
            USDC price in USD (defaults to 1.0 if unavailable)
        """
        price = await self.get_price("USDC")
        return price if price is not None else 1.0
    
    async def convert_sol_to_usd(self, sol_amount: float) -> float:
        """
        Convert SOL amount to USD.
        
        Args:
            sol_amount: Amount in SOL
            
        Returns:
            Amount in USD
        """
        sol_price = await self.get_sol_price_usd()
        return sol_amount * sol_price
    
    async def convert_usdc_to_usd(self, usdc_amount: float) -> float:
        """
        Convert USDC amount to USD.
        
        Args:
            usdc_amount: Amount in USDC
            
        Returns:
            Amount in USD
        """
        usdc_price = await self.get_usdc_price_usd()
        return usdc_amount * usdc_price
    
    async def close(self) -> None:
        """Close all price sources."""
        for source in self.sources:
            await source.close()


# Convenience function to get the price service instance
def get_price_service() -> PriceService:
    """
    Get the price service instance.
    
    Returns:
        PriceService instance
    """
    return PriceService.get_instance()
