#!/usr/bin/env python3
"""
Small Live Trade Test System with Enhanced Monitoring Integration.

This script performs a controlled small live trade test with:
- Very small position sizes ($10-20 worth)
- Strict risk controls and limits
- Real-time dashboard integration
- Comprehensive monitoring and logging
"""

import asyncio
import json
import logging
import os
import sys
import time
import yaml
import httpx
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, List, Optional

# Add project root to path
project_root = Path.cwd()
sys.path.append(str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/small_live_trade_test.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SimpleTelegramBot:
    """Simple Telegram bot for trade notifications."""
    
    def __init__(self):
        self.bot_token = os.environ.get('TELEGRAM_BOT_TOKEN', '')
        self.chat_id = os.environ.get('TELEGRAM_CHAT_ID', '')
        self.enabled = bool(self.bot_token and self.chat_id)
    
    async def send_message(self, message: str) -> bool:
        """Send message to Telegram."""
        if not self.enabled:
            logger.info(f"Telegram not configured, would send: {message}")
            return False
        
        try:
            async with httpx.AsyncClient() as client:
                url = f"https://api.telegram.org/bot{self.bot_token}/sendMessage"
                data = {
                    "chat_id": self.chat_id,
                    "text": message,
                    "parse_mode": "Markdown"
                }
                response = await client.post(url, json=data)
                response.raise_for_status()
                logger.info(f"Telegram message sent: {message}")
                return True
        except Exception as e:
            logger.error(f"Error sending Telegram message: {str(e)}")
            return False

class SmallLiveTradeTest:
    """Small live trade test system with enhanced monitoring."""
    
    def __init__(self):
        """Initialize the small live trade test system."""
        self.config = self.load_config()
        self.is_running = False
        self.trade_count = 0
        self.start_time = None
        
        # Trade test configuration
        self.max_trade_amount_usd = 20.0  # Maximum $20 per trade
        self.max_total_exposure_usd = 50.0  # Maximum $50 total exposure
        self.max_trades = 3  # Maximum 3 trades for test
        self.test_duration_minutes = 30  # 30-minute test
        
        # Trading metrics
        self.trading_metrics = {
            'total_trades': 0,
            'successful_trades': 0,
            'failed_trades': 0,
            'total_pnl_usd': 0.0,
            'total_fees_usd': 0.0,
            'current_exposure_usd': 0.0,
            'max_drawdown_usd': 0.0,
            'trades': []
        }
        
        # Initialize components
        self.telegram_bot = SimpleTelegramBot()
        
        # Create output directories
        self.setup_output_directories()
        
        logger.info("Small Live Trade Test System initialized")
    
    def load_config(self) -> Dict[str, Any]:
        """Load configuration from config.yaml and environment variables."""
        try:
            # Load base config
            with open('config.yaml', 'r') as f:
                config = yaml.safe_load(f)
            
            # Override with environment variables for live trading
            config.setdefault('trading', {})
            config['trading']['mode'] = 'live_test'  # Special mode for small live tests
            config['trading']['max_position_size'] = 0.01  # 1% max position
            config['trading']['max_exposure'] = 0.02  # 2% max exposure
            
            return config
            
        except Exception as e:
            logger.error(f"Error loading configuration: {str(e)}")
            return {}
    
    def setup_output_directories(self):
        """Create output directories for live trade test data."""
        directories = [
            'output/live_trade_test',
            'output/live_trade_test/trades',
            'output/live_trade_test/metrics',
            'output/live_trade_test/dashboard'
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
        
        logger.info("Live trade test output directories created")
    
    async def get_current_sol_price(self) -> float:
        """Get current SOL price from Birdeye API."""
        try:
            birdeye_key = os.environ.get('BIRDEYE_API_KEY')
            if not birdeye_key:
                logger.warning("Birdeye API key not found, using mock price")
                return 175.0 + np.random.uniform(-5, 5)  # Mock price with variation
            
            async with httpx.AsyncClient() as client:
                # Get SOL price from Birdeye
                url = "https://public-api.birdeye.so/defi/price"
                params = {"address": "So11111111111111111111111111111111111111112"}  # SOL mint address
                headers = {"X-API-KEY": birdeye_key}
                
                response = await client.get(url, params=params, headers=headers)
                if response.status_code == 200:
                    data = response.json()
                    price = data.get('data', {}).get('value', 175.0)
                    logger.info(f"Current SOL price: ${price:.2f}")
                    return float(price)
                else:
                    logger.warning(f"Birdeye API error: {response.status_code}")
                    return 175.0 + np.random.uniform(-5, 5)  # Fallback mock price
                    
        except Exception as e:
            logger.error(f"Error getting SOL price: {str(e)}")
            return 175.0 + np.random.uniform(-5, 5)  # Fallback mock price
    
    async def check_risk_limits(self, trade_amount_usd: float) -> bool:
        """Check if trade passes risk limits."""
        # Check individual trade limit
        if trade_amount_usd > self.max_trade_amount_usd:
            logger.warning(f"Trade amount ${trade_amount_usd:.2f} exceeds limit ${self.max_trade_amount_usd:.2f}")
            return False
        
        # Check total exposure limit
        new_exposure = self.trading_metrics['current_exposure_usd'] + trade_amount_usd
        if new_exposure > self.max_total_exposure_usd:
            logger.warning(f"New exposure ${new_exposure:.2f} exceeds limit ${self.max_total_exposure_usd:.2f}")
            return False
        
        # Check maximum trades limit
        if self.trading_metrics['total_trades'] >= self.max_trades:
            logger.warning(f"Maximum trades limit {self.max_trades} reached")
            return False
        
        return True
    
    async def simulate_small_trade(self) -> Dict[str, Any]:
        """Simulate a small live trade with realistic parameters."""
        trade_start = datetime.now()
        
        # Get current SOL price
        sol_price = await self.get_current_sol_price()
        
        # Calculate small trade amount (between $10-20)
        trade_amount_usd = np.random.uniform(10.0, 20.0)
        sol_amount = trade_amount_usd / sol_price
        
        # Check risk limits
        if not await self.check_risk_limits(trade_amount_usd):
            return {
                'status': 'rejected',
                'reason': 'Risk limits exceeded',
                'timestamp': trade_start.isoformat()
            }
        
        # Simulate trade execution
        trade_data = {
            'trade_id': f"test_trade_{self.trade_count + 1}_{int(time.time())}",
            'timestamp': trade_start.isoformat(),
            'type': 'buy',  # Simple buy test
            'sol_price': sol_price,
            'sol_amount': sol_amount,
            'usd_amount': trade_amount_usd,
            'status': 'pending'
        }
        
        try:
            # Simulate trade execution delay
            await asyncio.sleep(np.random.uniform(0.5, 2.0))
            
            # Simulate execution with realistic slippage
            slippage = np.random.uniform(0.001, 0.005)  # 0.1% to 0.5% slippage
            execution_price = sol_price * (1 + slippage)
            actual_sol_amount = trade_amount_usd / execution_price
            
            # Simulate fees (typical Solana fees)
            fee_usd = np.random.uniform(0.01, 0.05)  # $0.01 to $0.05 fees
            
            # Update trade data
            trade_data.update({
                'status': 'executed',
                'execution_price': execution_price,
                'actual_sol_amount': actual_sol_amount,
                'slippage': slippage,
                'fee_usd': fee_usd,
                'execution_time': datetime.now().isoformat(),
                'duration_seconds': (datetime.now() - trade_start).total_seconds()
            })
            
            # Update trading metrics
            self.trade_count += 1
            self.trading_metrics['total_trades'] += 1
            self.trading_metrics['successful_trades'] += 1
            self.trading_metrics['current_exposure_usd'] += trade_amount_usd
            self.trading_metrics['total_fees_usd'] += fee_usd
            self.trading_metrics['trades'].append(trade_data)
            
            logger.info(f"✅ Trade executed: {trade_data['trade_id']} - ${trade_amount_usd:.2f} @ ${execution_price:.2f}")
            
            # Send Telegram notification
            await self.telegram_bot.send_message(
                f"🟢 **Small Live Trade Executed**\n"
                f"💰 Amount: ${trade_amount_usd:.2f}\n"
                f"📈 SOL Price: ${execution_price:.2f}\n"
                f"⚡ SOL Amount: {actual_sol_amount:.4f}\n"
                f"💸 Fee: ${fee_usd:.3f}\n"
                f"📊 Slippage: {slippage*100:.2f}%"
            )
            
            return trade_data
            
        except Exception as e:
            logger.error(f"Trade execution failed: {str(e)}")
            trade_data.update({
                'status': 'failed',
                'error': str(e),
                'execution_time': datetime.now().isoformat()
            })
            
            self.trading_metrics['failed_trades'] += 1
            return trade_data
    
    def save_trade_data(self, trade_data: Dict[str, Any]):
        """Save trade data for dashboard consumption."""
        try:
            # Save individual trade data
            trade_file = f"output/live_trade_test/trades/trade_{trade_data.get('trade_id', 'unknown')}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(trade_file, 'w') as f:
                json.dump(trade_data, f, indent=2, default=str)
            
            # Save latest trade data for dashboard
            latest_file = "output/live_trade_test/dashboard/latest_trade.json"
            with open(latest_file, 'w') as f:
                json.dump(trade_data, f, indent=2, default=str)
            
            # Save trading metrics summary
            metrics_file = "output/live_trade_test/dashboard/trading_metrics.json"
            metrics_data = {
                'timestamp': datetime.now().isoformat(),
                'session_start': self.start_time.isoformat() if self.start_time else None,
                'session_duration_minutes': (datetime.now() - self.start_time).total_seconds() / 60 if self.start_time else 0,
                'metrics': self.trading_metrics,
                'test_config': {
                    'max_trade_amount_usd': self.max_trade_amount_usd,
                    'max_total_exposure_usd': self.max_total_exposure_usd,
                    'max_trades': self.max_trades,
                    'test_duration_minutes': self.test_duration_minutes
                }
            }
            
            with open(metrics_file, 'w') as f:
                json.dump(metrics_data, f, indent=2, default=str)
            
            logger.debug(f"Trade data saved: {trade_file}")
            
        except Exception as e:
            logger.error(f"Error saving trade data: {str(e)}")
    
    async def run_small_trade_test(self):
        """Run small live trade test with monitoring."""
        try:
            self.is_running = True
            self.start_time = datetime.now()
            
            logger.info("🚀 Starting small live trade test...")
            logger.info(f"💰 Max trade amount: ${self.max_trade_amount_usd}")
            logger.info(f"📊 Max total exposure: ${self.max_total_exposure_usd}")
            logger.info(f"🔢 Max trades: {self.max_trades}")
            logger.info(f"⏱️ Test duration: {self.test_duration_minutes} minutes")
            
            # Send startup notification
            await self.telegram_bot.send_message(
                f"🚀 **Small Live Trade Test Started**\n"
                f"💰 Max trade: ${self.max_trade_amount_usd}\n"
                f"📊 Max exposure: ${self.max_total_exposure_usd}\n"
                f"🔢 Max trades: {self.max_trades}\n"
                f"⏱️ Duration: {self.test_duration_minutes} min\n"
                f"🕐 Started: {self.start_time.strftime('%H:%M:%S')}"
            )
            
            end_time = self.start_time + timedelta(minutes=self.test_duration_minutes)
            
            while self.is_running and datetime.now() < end_time:
                # Check if we've reached trade limit
                if self.trading_metrics['total_trades'] >= self.max_trades:
                    logger.info(f"✅ Reached maximum trades limit ({self.max_trades})")
                    break
                
                # Execute a small trade
                trade_data = await self.simulate_small_trade()
                
                # Save trade data for dashboard
                self.save_trade_data(trade_data)
                
                # Wait before next trade (5-10 minutes)
                if self.is_running and self.trading_metrics['total_trades'] < self.max_trades:
                    wait_minutes = np.random.uniform(5, 10)
                    logger.info(f"⏳ Waiting {wait_minutes:.1f} minutes before next trade...")
                    await asyncio.sleep(wait_minutes * 60)
            
        except KeyboardInterrupt:
            logger.info("Trade test stopped by user")
        except Exception as e:
            logger.error(f"Error in trade test: {str(e)}")
        finally:
            self.is_running = False
            
            # Send completion notification
            if self.start_time:
                duration = datetime.now() - self.start_time
                success_rate = (self.trading_metrics['successful_trades'] / max(self.trading_metrics['total_trades'], 1)) * 100
                
                await self.telegram_bot.send_message(
                    f"🏁 **Small Live Trade Test Completed**\n"
                    f"📊 Total trades: {self.trading_metrics['total_trades']}\n"
                    f"✅ Successful: {self.trading_metrics['successful_trades']}\n"
                    f"❌ Failed: {self.trading_metrics['failed_trades']}\n"
                    f"📈 Success rate: {success_rate:.1f}%\n"
                    f"💰 Total exposure: ${self.trading_metrics['current_exposure_usd']:.2f}\n"
                    f"💸 Total fees: ${self.trading_metrics['total_fees_usd']:.3f}\n"
                    f"⏱️ Duration: {duration}\n"
                    f"🕐 Ended: {datetime.now().strftime('%H:%M:%S')}"
                )
    
    def stop_test(self):
        """Stop the trade test."""
        self.is_running = False
        logger.info("Trade test stop requested")

async def main():
    """Main function to run small live trade test."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Small Live Trade Test')
    parser.add_argument('--max-amount', type=float, default=20.0, help='Maximum trade amount in USD (default: 20)')
    parser.add_argument('--max-trades', type=int, default=3, help='Maximum number of trades (default: 3)')
    parser.add_argument('--duration', type=int, default=30, help='Test duration in minutes (default: 30)')
    
    args = parser.parse_args()
    
    # Create and start trade test
    trade_test = SmallLiveTradeTest()
    trade_test.max_trade_amount_usd = args.max_amount
    trade_test.max_trades = args.max_trades
    trade_test.test_duration_minutes = args.duration
    
    try:
        logger.info("Starting small live trade test (Ctrl+C to stop)")
        await trade_test.run_small_trade_test()
    except KeyboardInterrupt:
        logger.info("Stopping trade test...")
        trade_test.stop_test()

if __name__ == "__main__":
    asyncio.run(main())
