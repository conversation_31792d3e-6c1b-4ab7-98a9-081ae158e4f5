#!/usr/bin/env python3
"""
Test module for the Position Sizer.
"""

import unittest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
import sys

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the module to test
from core.risk.position_sizer import PositionSizer

class TestPositionSizer(unittest.TestCase):
    """Test cases for the PositionSizer class."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create test configuration
        self.config = {
            "max_position_size": 0.1,
            "max_portfolio_risk": 0.02,
            "volatility_lookback": 20,
            "volatility_scaling": True,
            "min_position_size": 0.01,
            "position_size_increment": 0.01,
            "risk_per_trade": 0.01,
            "atr_multiplier": 2.0
        }
        
        # Create test price data
        dates = pd.date_range(start="2023-01-01", periods=100, freq="D")
        prices = np.linspace(100, 200, 100) + np.random.normal(0, 5, 100)
        
        self.price_data = pd.DataFrame({
            "close": prices,
            "high": prices + np.random.normal(0, 1, 100),
            "low": prices - np.random.normal(0, 1, 100),
            "volume": np.random.normal(1000, 100, 100)
        }, index=dates)
        
        # Initialize position sizer
        self.position_sizer = PositionSizer(self.config)
    
    def test_calculate_position_size(self):
        """Test position size calculation."""
        # Calculate position size
        account_balance = 10000
        market = "SOL-USDC"
        signal_strength = 0.8
        
        result = self.position_sizer.calculate_position_size(
            self.price_data,
            account_balance,
            market,
            signal_strength
        )
        
        # Check that result contains expected keys
        expected_keys = ["position_size", "position_value", "volatility", "risk_adjusted"]
        for key in expected_keys:
            self.assertIn(key, result)
        
        # Check that position size is within bounds
        self.assertLessEqual(result["position_size"], self.config["max_position_size"])
        self.assertGreaterEqual(result["position_size"], self.config["min_position_size"])
        
        # Check that position value is calculated correctly
        self.assertAlmostEqual(result["position_value"], account_balance * result["position_size"])
        
        # Check that position size is adjusted for signal strength
        self.assertLessEqual(result["position_size"], self.config["max_position_size"] * signal_strength)
    
    def test_calculate_position_size_insufficient_data(self):
        """Test position size calculation with insufficient data."""
        # Create price data with fewer samples than lookback
        short_data = self.price_data.iloc[:10].copy()
        
        # Calculate position size
        account_balance = 10000
        market = "SOL-USDC"
        
        result = self.position_sizer.calculate_position_size(
            short_data,
            account_balance,
            market
        )
        
        # Check that minimum position size is used
        self.assertEqual(result["position_size"], self.config["min_position_size"])
        self.assertEqual(result["position_value"], account_balance * self.config["min_position_size"])
        self.assertIsNone(result["volatility"])
        self.assertFalse(result["risk_adjusted"])
    
    def test_calculate_stop_loss(self):
        """Test stop loss calculation."""
        # Calculate stop loss
        entry_price = 150.0
        position_size = 0.05
        account_balance = 10000
        is_long = True
        
        result = self.position_sizer.calculate_stop_loss(
            entry_price,
            position_size,
            account_balance,
            self.price_data,
            is_long
        )
        
        # Check that result contains expected keys
        expected_keys = ["stop_loss_price", "risk_amount", "risk_percentage"]
        for key in expected_keys:
            self.assertIn(key, result)
        
        # Check that stop loss price is lower than entry price for long positions
        self.assertLess(result["stop_loss_price"], entry_price)
        
        # Check that risk amount is calculated correctly
        self.assertAlmostEqual(result["risk_amount"], account_balance * self.config["risk_per_trade"])
    
    def test_calculate_stop_loss_short(self):
        """Test stop loss calculation for short positions."""
        # Calculate stop loss
        entry_price = 150.0
        position_size = 0.05
        account_balance = 10000
        is_long = False
        
        result = self.position_sizer.calculate_stop_loss(
            entry_price,
            position_size,
            account_balance,
            self.price_data,
            is_long
        )
        
        # Check that stop loss price is higher than entry price for short positions
        self.assertGreater(result["stop_loss_price"], entry_price)
    
    def test_calculate_take_profit(self):
        """Test take profit calculation."""
        # Calculate take profit
        entry_price = 150.0
        stop_loss_price = 145.0
        is_long = True
        risk_reward_ratio = 2.0
        
        take_profit_price = self.position_sizer.calculate_take_profit(
            entry_price,
            stop_loss_price,
            is_long,
            risk_reward_ratio
        )
        
        # Check that take profit price is calculated correctly
        risk = entry_price - stop_loss_price
        expected_take_profit = entry_price + (risk * risk_reward_ratio)
        self.assertAlmostEqual(take_profit_price, expected_take_profit)
    
    def test_calculate_take_profit_short(self):
        """Test take profit calculation for short positions."""
        # Calculate take profit
        entry_price = 150.0
        stop_loss_price = 155.0
        is_long = False
        risk_reward_ratio = 2.0
        
        take_profit_price = self.position_sizer.calculate_take_profit(
            entry_price,
            stop_loss_price,
            is_long,
            risk_reward_ratio
        )
        
        # Check that take profit price is calculated correctly
        risk = stop_loss_price - entry_price
        expected_take_profit = entry_price - (risk * risk_reward_ratio)
        self.assertAlmostEqual(take_profit_price, expected_take_profit)

if __name__ == "__main__":
    unittest.main()
