#!/usr/bin/env python3
"""
Test module for the Momentum Optimizer.
"""

import unittest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
import sys
import json

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the module to test
from core.strategies.momentum_optimizer import MomentumOptimizer

class TestMomentumOptimizer(unittest.TestCase):
    """Test cases for the MomentumOptimizer class."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create test configuration
        self.config = {
            "window_size_range": [5, 10, 15],
            "threshold_range": [0.01, 0.02],
            "smoothing_factor_range": [0.1, 0.2],
            "max_value_range": [0.1, 0.2],
            "train_test_split": 0.7,
            "walk_forward_window": 30,
            "walk_forward_step": 7,
            "min_trades": 5,
            "output_dir": "tests/output/momentum_optimizer"
        }
        
        # Create test price data
        dates = pd.date_range(start="2023-01-01", periods=100, freq="D")
        prices = np.linspace(100, 200, 100) + np.random.normal(0, 5, 100)
        
        # Add some momentum patterns
        for i in range(30, 40):
            prices[i] += i - 30
        for i in range(60, 70):
            prices[i] -= i - 60
        
        self.price_data = pd.DataFrame({
            "close": prices,
            "high": prices + np.random.normal(0, 1, 100),
            "low": prices - np.random.normal(0, 1, 100),
            "volume": np.random.normal(1000, 100, 100)
        }, index=dates)
        
        # Create output directory
        os.makedirs(self.config["output_dir"], exist_ok=True)
        
        # Initialize optimizer
        self.optimizer = MomentumOptimizer(self.config)
    
    def test_calculate_momentum(self):
        """Test momentum calculation."""
        # Calculate momentum
        signals = self.optimizer.calculate_momentum(
            self.price_data,
            window_size=10,
            threshold=0.01,
            smoothing_factor=0.2,
            max_value=0.1
        )
        
        # Check that signals DataFrame has the expected columns
        expected_columns = ["close", "high", "low", "volume", "returns", "momentum", 
                           "momentum_smooth", "momentum_capped", "signal", "position", "entry_exit"]
        for col in expected_columns:
            self.assertIn(col, signals.columns)
        
        # Check that signals are generated
        self.assertTrue((signals["signal"].abs() > 0).any())
    
    def test_calculate_performance(self):
        """Test performance calculation."""
        # Calculate momentum
        signals = self.optimizer.calculate_momentum(
            self.price_data,
            window_size=10,
            threshold=0.01,
            smoothing_factor=0.2,
            max_value=0.1
        )
        
        # Calculate performance
        performance = self.optimizer.calculate_performance(signals)
        
        # Check that performance metrics are calculated
        expected_metrics = ["sharpe_ratio", "profit_factor", "win_rate", "max_drawdown", 
                           "total_return", "num_trades", "avg_trade_return"]
        for metric in expected_metrics:
            self.assertIn(metric, performance)
    
    def test_optimize_parameters(self):
        """Test parameter optimization."""
        # Optimize parameters
        results = self.optimizer.optimize_parameters(self.price_data)
        
        # Check that results contain expected keys
        expected_keys = ["best_params", "train_performance", "test_performance", "all_results"]
        for key in expected_keys:
            self.assertIn(key, results)
        
        # Check that best parameters contain expected keys
        expected_param_keys = ["window_size", "threshold", "smoothing_factor", "max_value"]
        for key in expected_param_keys:
            self.assertIn(key, results["best_params"])
    
    def test_walk_forward_optimization(self):
        """Test walk-forward optimization."""
        # Create longer price data for walk-forward optimization
        dates = pd.date_range(start="2023-01-01", periods=200, freq="D")
        prices = np.linspace(100, 300, 200) + np.random.normal(0, 10, 200)
        
        # Add some momentum patterns
        for i in range(30, 40):
            prices[i] += i - 30
        for i in range(60, 70):
            prices[i] -= i - 60
        for i in range(120, 130):
            prices[i] += i - 120
        for i in range(150, 160):
            prices[i] -= i - 150
        
        price_data = pd.DataFrame({
            "close": prices,
            "high": prices + np.random.normal(0, 2, 200),
            "low": prices - np.random.normal(0, 2, 200),
            "volume": np.random.normal(1000, 100, 200)
        }, index=dates)
        
        # Perform walk-forward optimization
        results = self.optimizer.walk_forward_optimization(price_data)
        
        # Check that results contain expected keys
        self.assertIn("wfo_results", results)
        
        # Check that at least one period was optimized
        self.assertTrue(len(results["wfo_results"]) > 0)
    
    def test_save_optimization_results(self):
        """Test saving optimization results."""
        # Create test results
        results = [
            {
                "params": {
                    "window_size": 10,
                    "threshold": 0.01,
                    "smoothing_factor": 0.2,
                    "max_value": 0.1
                },
                "train_performance": {
                    "sharpe_ratio": 1.5,
                    "profit_factor": 1.2,
                    "win_rate": 0.6,
                    "max_drawdown": 0.1,
                    "total_return": 0.2,
                    "num_trades": 10,
                    "avg_trade_return": 0.02
                },
                "test_performance": {
                    "sharpe_ratio": 1.2,
                    "profit_factor": 1.1,
                    "win_rate": 0.55,
                    "max_drawdown": 0.15,
                    "total_return": 0.15,
                    "num_trades": 8,
                    "avg_trade_return": 0.018
                }
            }
        ]
        
        # Save results
        self.optimizer.save_optimization_results(results)
        
        # Check that file was created
        files = os.listdir(self.config["output_dir"])
        self.assertTrue(any(f.startswith("optimization_results_") for f in files))
    
    def tearDown(self):
        """Clean up after tests."""
        # Remove test output directory
        import shutil
        shutil.rmtree(self.config["output_dir"], ignore_errors=True)

if __name__ == "__main__":
    unittest.main()
