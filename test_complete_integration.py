#!/usr/bin/env python3
"""
Complete Integration Test for Synergy7 Enhanced Trading System.

This script tests all phases working together:
- Phase 1: Enhanced Market Regime Detection & Whale Watching
- Phase 2: Advanced Risk Management (VaR/CVaR)
- Phase 3: Strategy Performance Attribution
- Phase 4: Adaptive Strategy Weighting

This validates the complete end-to-end integration.
"""

import os
import sys
import asyncio
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import yaml
import re

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import all modules from all phases
from core.strategies.market_regime_detector import EnhancedMarketRegimeDetector, MarketRegime
from core.strategies.probabilistic_regime import ProbabilisticRegimeDetector
from core.data.whale_signal_generator import WhaleSignalGenerator
from core.signals.whale_signal_processor import WhaleSignalProcessor
from core.risk.var_calculator import VaRCalculator
from core.risk.portfolio_risk_manager import PortfolioRiskManager
from core.risk.position_sizer import EnhancedPositionSizer
from core.analytics.strategy_attribution import StrategyAttributionTracker
from core.analytics.performance_analyzer import PerformanceAnalyzer
from core.strategies.adaptive_weight_manager import AdaptiveWeightManager
from core.strategies.strategy_selector import StrategySelector

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_integration_config():
    """Load complete integration configuration."""
    try:
        # Set all environment variables for complete testing
        env_vars = {
            'MARKET_REGIME_ENABLED': 'true',
            'WHALE_WATCHING_ENABLED': 'true',
            'HMM_ENABLED': 'true',
            'VAR_ENABLED': 'true',
            'CVAR_ENABLED': 'true',
            'STRATEGY_ATTRIBUTION_ENABLED': 'true',
            'ADAPTIVE_WEIGHTING_ENABLED': 'true',
            'POSITION_SIZING_METHOD': 'var_based',
            'REGIME_BASED_SIZING': 'true',
            'LEARNING_RATE': '0.05',
            'WEIGHT_UPDATE_INTERVAL': '60'
        }
        
        for key, value in env_vars.items():
            os.environ.setdefault(key, value)
        
        # Load and process config
        with open('config.yaml', 'r') as f:
            config_text = f.read()
        
        # Environment variable substitution
        def replace_env_var(match):
            var_expr = match.group(1)
            if ':-' in var_expr:
                var_name, default_value = var_expr.split(':-', 1)
                return os.environ.get(var_name, default_value)
            else:
                return os.environ.get(var_expr, '')
        
        config_text = re.sub(r'\$\{([^}]+)\}', replace_env_var, config_text)
        config = yaml.safe_load(config_text)
        
        # Convert types
        def convert_types(obj):
            if isinstance(obj, dict):
                return {k: convert_types(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_types(item) for item in obj]
            elif isinstance(obj, str):
                if obj.lower() == 'true':
                    return True
                elif obj.lower() == 'false':
                    return False
                else:
                    try:
                        if '.' in obj:
                            return float(obj)
                        else:
                            return int(obj)
                    except ValueError:
                        return obj
            return obj
        
        config = convert_types(config)
        return config
    except Exception as e:
        logger.error(f"Error loading integration config: {str(e)}")
        return {}

def generate_comprehensive_test_data(days=60):
    """Generate comprehensive test data for all components."""
    logger.info(f"Generating comprehensive test data for {days} days...")
    
    # Generate market data with different regime periods
    dates = pd.date_range(start=datetime.now() - timedelta(days=days), periods=days*24, freq='H')
    
    np.random.seed(42)
    base_price = 25.0
    prices = [base_price]
    volumes = []
    
    # Create distinct market regimes
    regime_periods = {
        'trending_up': (0, 0.25),
        'ranging': (0.25, 0.5),
        'volatile': (0.5, 0.75),
        'trending_down': (0.75, 0.9),
        'choppy': (0.9, 1.0)
    }
    
    for i in range(len(dates) - 1):
        # Determine current regime based on position in data
        position = i / len(dates)
        current_regime = 'ranging'  # default
        
        for regime, (start, end) in regime_periods.items():
            if start <= position < end:
                current_regime = regime
                break
        
        # Generate price changes based on regime
        if current_regime == 'trending_up':
            trend = 0.002
            volatility = 0.015
        elif current_regime == 'trending_down':
            trend = -0.002
            volatility = 0.015
        elif current_regime == 'ranging':
            trend = 0.0
            volatility = 0.01
        elif current_regime == 'volatile':
            trend = 0.0005
            volatility = 0.04
        else:  # choppy
            trend = np.random.choice([-0.003, 0.003])
            volatility = 0.025
        
        change = np.random.normal(trend, volatility)
        new_price = prices[-1] * (1 + change)
        prices.append(max(0.1, new_price))
        
        # Generate volume
        base_volume = 1000000
        volume_change = np.random.normal(0, 0.3)
        volume = base_volume * (1 + volume_change)
        volumes.append(max(100000, volume))
    
    volumes.append(volumes[-1])
    
    # Create OHLCV data
    df = pd.DataFrame({
        'timestamp': dates,
        'open': prices,
        'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
        'close': prices,
        'volume': volumes
    })
    
    # Ensure OHLC consistency
    df['high'] = df[['open', 'close', 'high']].max(axis=1)
    df['low'] = df[['open', 'close', 'low']].min(axis=1)
    
    logger.info(f"Generated comprehensive test data: {len(df)} rows")
    return df

def generate_mock_trades_for_strategies():
    """Generate mock trades for multiple strategies."""
    strategies = {
        'momentum_strategy': {'win_rate': 0.65, 'avg_win': 0.025, 'avg_loss': -0.015},
        'mean_reversion': {'win_rate': 0.60, 'avg_win': 0.018, 'avg_loss': -0.012},
        'breakout_strategy': {'win_rate': 0.50, 'avg_win': 0.035, 'avg_loss': -0.020},
        'scalping_strategy': {'win_rate': 0.75, 'avg_win': 0.008, 'avg_loss': -0.006},
        'whale_momentum': {'win_rate': 0.55, 'avg_win': 0.022, 'avg_loss': -0.018}
    }
    
    all_trades = {}
    base_time = datetime.now() - timedelta(days=30)
    
    for strategy_name, config in strategies.items():
        trades = []
        np.random.seed(hash(strategy_name) % 2**32)
        
        for i in range(20):  # 20 trades per strategy
            is_winner = np.random.random() < config['win_rate']
            base_pnl = config['avg_win'] if is_winner else config['avg_loss']
            pnl = base_pnl + np.random.normal(0, abs(base_pnl) * 0.3)
            
            trade = {
                'timestamp': (base_time + timedelta(hours=i * 6)).isoformat(),
                'symbol': 'SOL/USDC',
                'side': 'buy' if pnl > 0 else 'sell',
                'quantity': np.random.uniform(100, 1000),
                'price': 25.0 + np.random.normal(0, 2),
                'pnl': pnl,
                'commission': abs(pnl) * 0.001,
                'slippage': abs(pnl) * 0.0005,
                'signal_strength': np.random.uniform(0.6, 1.0),
                'market_regime': np.random.choice(['trending_up', 'ranging', 'volatile']),
                'trade_id': f"{strategy_name}_trade_{i}"
            }
            trades.append(trade)
        
        all_trades[strategy_name] = trades
    
    return all_trades

async def test_complete_integration(config):
    """Test complete integration of all phases."""
    logger.info("="*60)
    logger.info("COMPLETE INTEGRATION TEST - ALL PHASES")
    logger.info("="*60)
    
    try:
        # Generate comprehensive test data
        market_data = generate_comprehensive_test_data(60)
        mock_trades = generate_mock_trades_for_strategies()
        
        # Initialize all components
        logger.info("Initializing all components...")
        
        # Phase 1: Market Regime Detection & Whale Watching
        regime_detector = EnhancedMarketRegimeDetector(config)
        whale_generator = WhaleSignalGenerator(config)
        whale_processor = WhaleSignalProcessor(config)
        
        # Phase 2: Risk Management
        var_calculator = VaRCalculator(config)
        portfolio_risk_manager = PortfolioRiskManager(config)
        position_sizer = EnhancedPositionSizer(config)
        
        # Phase 3: Strategy Attribution
        attribution_tracker = StrategyAttributionTracker(config)
        performance_analyzer = PerformanceAnalyzer(config)
        
        # Phase 4: Adaptive Weighting
        weight_manager = AdaptiveWeightManager(config)
        strategy_selector = StrategySelector(config)
        
        logger.info("All components initialized successfully!")
        
        # STEP 1: Market Regime Detection
        logger.info("\n" + "="*50)
        logger.info("STEP 1: Market Regime Detection")
        logger.info("="*50)
        
        regime, metrics, probabilities = regime_detector.detect_regime(market_data)
        regime_analysis = regime_detector.analyze_market(market_data)
        
        logger.info(f"Detected Market Regime: {regime.value}")
        logger.info(f"Regime Confidence: {probabilities.get(regime.value, 0):.3f}")
        logger.info(f"Regime Change Detected: {regime_analysis.get('regime_change', False)}")
        
        # STEP 2: Whale Signal Generation
        logger.info("\n" + "="*50)
        logger.info("STEP 2: Whale Signal Generation")
        logger.info("="*50)
        
        whale_signals = await whale_generator.update_whale_signals()
        logger.info(f"Generated Whale Signals: {len(whale_signals)}")
        
        # Process whale signals
        processed_whale_signals = []
        for token, signal in whale_signals.items():
            processed_signal = whale_processor.process_whale_signal(signal, market_data)
            if processed_signal:
                processed_whale_signals.append(processed_signal)
        
        logger.info(f"Processed Whale Signals: {len(processed_whale_signals)}")
        
        # STEP 3: Strategy Performance Attribution
        logger.info("\n" + "="*50)
        logger.info("STEP 3: Strategy Performance Attribution")
        logger.info("="*50)
        
        # Record trades for all strategies
        for strategy_name, trades in mock_trades.items():
            for trade in trades:
                attribution_tracker.record_trade(strategy_name, trade)
        
        # Calculate performance for all strategies
        strategy_performance = {}
        for strategy_name in mock_trades.keys():
            perf = attribution_tracker.calculate_strategy_performance(strategy_name)
            if perf:
                strategy_performance[strategy_name] = perf
        
        logger.info(f"Strategy Performance Calculated: {len(strategy_performance)} strategies")
        
        # Portfolio attribution
        portfolio_attribution = attribution_tracker.calculate_portfolio_attribution()
        logger.info(f"Portfolio Attribution: {portfolio_attribution.get('portfolio_metrics', {}).get('total_portfolio_pnl', 0):.4f} total PnL")
        
        # STEP 4: Risk Management Integration
        logger.info("\n" + "="*50)
        logger.info("STEP 4: Risk Management Integration")
        logger.info("="*50)
        
        # Calculate VaR for portfolio
        portfolio_returns = {}
        for strategy_name in strategy_performance.keys():
            returns = var_calculator.calculate_returns(market_data)
            portfolio_returns[strategy_name] = returns
        
        weights = {name: 1.0/len(strategy_performance) for name in strategy_performance.keys()}
        portfolio_var = var_calculator.calculate_portfolio_var(portfolio_returns, weights)
        
        logger.info(f"Portfolio VaR (95%): {portfolio_var.get('portfolio_var', 0):.4f}")
        logger.info(f"Portfolio CVaR (95%): {portfolio_var.get('portfolio_cvar', 0):.4f}")
        
        # Generate test positions for risk management
        test_positions = {}
        account_balance = 100000.0
        for i, strategy_name in enumerate(strategy_performance.keys()):
            position_value = account_balance * 0.15  # 15% per strategy
            test_positions[strategy_name] = {
                'size': position_value / 25.0,  # Assuming $25 price
                'value': position_value,
                'entry_price': 24.5,
                'current_price': 25.0
            }
        
        portfolio_risk_manager.update_positions(test_positions)
        risk_metrics = portfolio_risk_manager.calculate_portfolio_risk_metrics({
            'SOL': market_data  # Simplified for testing
        })
        
        logger.info(f"Portfolio Risk Metrics: ${risk_metrics.get('portfolio_value', 0):,.2f} total value")
        
        # STEP 5: Adaptive Weight Management
        logger.info("\n" + "="*50)
        logger.info("STEP 5: Adaptive Weight Management")
        logger.info("="*50)
        
        # Calculate adaptive weights
        adaptive_weights = weight_manager.update_weights(
            strategy_performance, regime.value, force_update=True
        )
        
        logger.info("Adaptive Strategy Weights:")
        for strategy, weight in adaptive_weights.items():
            logger.info(f"  {strategy}: {weight:.3f}")
        
        # Get weight recommendations
        recommendations = weight_manager.get_weight_recommendations(strategy_performance)
        logger.info(f"Weight Recommendations: {len(recommendations)}")
        
        # STEP 6: Strategy Selection
        logger.info("\n" + "="*50)
        logger.info("STEP 6: Strategy Selection")
        logger.info("="*50)
        
        # Register strategies
        for strategy_name in strategy_performance.keys():
            strategy_config = {
                'enabled': True,
                'min_confidence': 0.5,
                'preferred_regimes': ['trending_up', 'ranging'] if 'momentum' in strategy_name else ['ranging'],
                'risk_level': 'medium'
            }
            strategy_selector.register_strategy(strategy_name, strategy_config)
        
        # Select strategies
        selected_strategies = strategy_selector.select_strategies(
            regime.value, probabilities.get(regime.value, 0.8), 
            adaptive_weights, strategy_performance
        )
        
        logger.info(f"Selected Strategies: {len(selected_strategies)}")
        for strategy in selected_strategies:
            logger.info(f"  {strategy['strategy_name']}: {strategy['effective_allocation']:.3f} allocation")
        
        # STEP 7: Enhanced Position Sizing
        logger.info("\n" + "="*50)
        logger.info("STEP 7: Enhanced Position Sizing")
        logger.info("="*50)
        
        # Test enhanced position sizing for selected strategies
        for strategy in selected_strategies[:2]:  # Test top 2 strategies
            position_result = position_sizer.calculate_enhanced_position_size(
                price_data=market_data,
                account_balance=account_balance,
                market=strategy['strategy_name'],
                signal_strength=0.8,
                market_regime=regime.value,
                current_positions=test_positions,
                correlation_data={'SOL': market_data}
            )
            
            logger.info(f"{strategy['strategy_name']} Position Sizing:")
            logger.info(f"  Position Size: {position_result['position_size']:.4f}")
            logger.info(f"  Position Value: ${position_result['position_value']:,.2f}")
            logger.info(f"  VaR-Based: {position_result.get('var_based', False)}")
        
        # STEP 8: Performance Analysis
        logger.info("\n" + "="*50)
        logger.info("STEP 8: Performance Analysis")
        logger.info("="*50)
        
        # Analyze portfolio performance
        portfolio_analysis = performance_analyzer.analyze_portfolio_performance(portfolio_attribution)
        
        logger.info(f"Portfolio Health: {portfolio_analysis.get('health_status', 'unknown')}")
        logger.info(f"Health Score: {portfolio_analysis.get('health_score', 0)}/100")
        logger.info(f"Effective Strategies: {portfolio_analysis.get('effective_strategies', 0):.1f}")
        
        # Identify underperforming strategies
        underperforming = performance_analyzer.identify_underperforming_strategies(strategy_performance)
        logger.info(f"Underperforming Strategies: {len(underperforming)}")
        
        # Generate optimization recommendations
        optimization_recs = performance_analyzer.generate_optimization_recommendations(
            strategy_performance, adaptive_weights
        )
        logger.info(f"Optimization Recommendations: {len(optimization_recs)}")
        
        # FINAL INTEGRATION SUMMARY
        logger.info("\n" + "="*60)
        logger.info("INTEGRATION SUMMARY")
        logger.info("="*60)
        
        logger.info(f"✅ Market Regime: {regime.value} (confidence: {probabilities.get(regime.value, 0):.3f})")
        logger.info(f"✅ Whale Signals: {len(whale_signals)} generated, {len(processed_whale_signals)} processed")
        logger.info(f"✅ Strategy Performance: {len(strategy_performance)} strategies analyzed")
        logger.info(f"✅ Portfolio VaR: {portfolio_var.get('portfolio_var', 0):.4f} (95% confidence)")
        logger.info(f"✅ Adaptive Weights: {len(adaptive_weights)} strategies weighted")
        logger.info(f"✅ Strategy Selection: {len(selected_strategies)} strategies selected")
        logger.info(f"✅ Portfolio Health: {portfolio_analysis.get('health_status', 'unknown')} ({portfolio_analysis.get('health_score', 0)}/100)")
        logger.info(f"✅ Risk Management: ${risk_metrics.get('portfolio_value', 0):,.2f} portfolio value monitored")
        
        await whale_generator.close()
        return True
        
    except Exception as e:
        logger.error(f"Error in complete integration test: {str(e)}")
        return False

async def main():
    """Main integration test function."""
    logger.info("Starting Complete Synergy7 Integration Test...")
    
    # Load configuration
    config = load_integration_config()
    if not config:
        logger.error("Failed to load integration configuration")
        return False
    
    # Run complete integration test
    success = await test_complete_integration(config)
    
    # Final results
    logger.info("\n" + "="*60)
    logger.info("COMPLETE INTEGRATION TEST RESULTS")
    logger.info("="*60)
    
    if success:
        logger.info("🎉 ALL PHASES INTEGRATED SUCCESSFULLY! 🎉")
        logger.info("")
        logger.info("✅ Phase 1: Enhanced Market Regime Detection & Whale Watching")
        logger.info("✅ Phase 2: Advanced Risk Management (VaR/CVaR)")
        logger.info("✅ Phase 3: Strategy Performance Attribution")
        logger.info("✅ Phase 4: Adaptive Strategy Weighting")
        logger.info("")
        logger.info("🚀 Synergy7 Enhanced Trading System is ready for production!")
    else:
        logger.error("❌ INTEGRATION TEST FAILED")
    
    logger.info("="*60)
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
