"""
Synergy7 Trading System Configuration Management

This package provides a unified interface for managing configuration in the Synergy7 system.
It includes modules for loading, validating, and accessing configuration from multiple sources.

Usage:
    from utils.config import ConfigManager, EnvManager, ModeManager, TradingMode, ConfigValidationError

    # Initialize configuration manager
    config_manager = ConfigManager()

    # Load configuration
    config = config_manager.load()

    # Access configuration values
    rpc_url = config_manager.get("solana.rpc_url")

    # Check current mode
    if config_manager.is_live_trading():
        # Do live trading specific setup
        pass
"""

# Import legacy configuration utilities for backward compatibility
from utils.config.config_loader import (
    ConfigLoader,
    ConfigValidationError as LegacyConfigValidationError,
    load_config as legacy_load_config
)

from utils.config.loader import (
    load_environment_config,
    load_component_config,
    get_config_value,
    set_config_value,
    merge_configs
)

# Import new configuration management utilities
from utils.config.config_manager import ConfigManager
from utils.config.env_manager import EnvManager
from utils.config.mode_manager import ModeManager, TradingMode
from utils.config.config_validator import validate_config, ConfigValidationError

__all__ = [
    # New configuration management utilities
    'ConfigManager',
    'EnvManager',
    'ModeManager',
    'TradingMode',
    'validate_config',
    'ConfigValidationError',

    # Legacy configuration utilities
    'ConfigLoader',
    'LegacyConfigValidationError',
    'legacy_load_config',
    'load_environment_config',
    'load_component_config',
    'get_config_value',
    'set_config_value',
    'merge_configs'
]
