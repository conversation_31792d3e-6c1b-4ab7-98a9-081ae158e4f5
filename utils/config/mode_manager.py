#!/usr/bin/env python3
"""
Mode Manager for Synergy7 System

This module provides a unified interface for managing the different operating modes
of the Synergy7 trading system. It handles mode transitions, validation, and provides
helper methods for checking the current mode.

Usage:
    from utils.config.mode_manager import ModeManager, TradingMode
    
    # Initialize with default mode
    mode_manager = ModeManager()
    
    # Or specify initial mode
    mode_manager = ModeManager(initial_mode=TradingMode.PAPER)
    
    # Check current mode
    if mode_manager.is_live_trading():
        # Do live trading specific setup
        pass
    
    # Transition to a different mode
    mode_manager.transition_to(TradingMode.BACKTEST)
    
    # Get mode-specific configuration
    backtest_config = mode_manager.get_mode_config()
"""

import os
import logging
import enum
from typing import Any, Dict, Optional, Set, List, Tuple
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('mode_manager')

class TradingMode(enum.Enum):
    """Enum representing the different operating modes of the system."""
    LIVE = "live_trading"
    PAPER = "paper_trading"
    BACKTEST = "backtesting"
    SIMULATION = "simulation"

class ModeTransitionError(Exception):
    """Exception raised for invalid mode transitions."""
    pass

class ModeManager:
    """
    Mode Manager for Synergy7 System.
    
    This class provides a unified interface for managing the different operating modes
    of the Synergy7 trading system.
    """
    
    def __init__(self, initial_mode: Optional[TradingMode] = None, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the ModeManager.
        
        Args:
            initial_mode: Initial operating mode
            config: Configuration dictionary
        """
        self.config = config or {}
        self.current_mode = initial_mode or TradingMode.SIMULATION
        self.previous_mode: Optional[TradingMode] = None
        
        # Define valid mode transitions
        # Each mode can transition to specific other modes
        self.valid_transitions: Dict[TradingMode, Set[TradingMode]] = {
            TradingMode.SIMULATION: {TradingMode.PAPER, TradingMode.BACKTEST},
            TradingMode.PAPER: {TradingMode.SIMULATION, TradingMode.BACKTEST, TradingMode.LIVE},
            TradingMode.BACKTEST: {TradingMode.SIMULATION, TradingMode.PAPER},
            TradingMode.LIVE: {TradingMode.PAPER}
        }
        
        # Define risk levels for each mode
        self.risk_levels: Dict[TradingMode, int] = {
            TradingMode.SIMULATION: 0,  # No risk
            TradingMode.BACKTEST: 1,    # Low risk
            TradingMode.PAPER: 2,       # Medium risk
            TradingMode.LIVE: 3         # High risk
        }
        
        # Initialize mode from environment variables if available
        self._init_from_env()
        
        logger.info(f"Initialized mode manager with mode: {self.current_mode.name}")
    
    def _init_from_env(self) -> None:
        """Initialize mode from environment variables."""
        # Check for TRADING_MODE environment variable
        if 'TRADING_MODE' in os.environ:
            mode_value = os.environ['TRADING_MODE'].strip().lower()
            if mode_value == 'live':
                self.current_mode = TradingMode.LIVE
            elif mode_value == 'paper':
                self.current_mode = TradingMode.PAPER
            elif mode_value == 'backtest':
                self.current_mode = TradingMode.BACKTEST
            elif mode_value == 'simulation':
                self.current_mode = TradingMode.SIMULATION
        
        # Check for individual mode flags
        elif 'PAPER_TRADING' in os.environ and os.environ['PAPER_TRADING'].strip().lower() == 'true':
            self.current_mode = TradingMode.PAPER
        elif 'BACKTESTING_ENABLED' in os.environ and os.environ['BACKTESTING_ENABLED'].strip().lower() == 'true':
            self.current_mode = TradingMode.BACKTEST
    
    def transition_to(self, new_mode: TradingMode, force: bool = False) -> bool:
        """
        Transition to a new operating mode.
        
        Args:
            new_mode: New operating mode
            force: Whether to force the transition even if it's not valid
            
        Returns:
            True if the transition was successful, False otherwise
            
        Raises:
            ModeTransitionError: If the transition is not valid and force is False
        """
        # Check if the transition is valid
        if not force and new_mode not in self.valid_transitions[self.current_mode]:
            error_msg = f"Invalid mode transition: {self.current_mode.name} -> {new_mode.name}"
            logger.error(error_msg)
            raise ModeTransitionError(error_msg)
        
        # Check if the transition is to a higher risk level
        if self.risk_levels[new_mode] > self.risk_levels[self.current_mode]:
            logger.warning(f"Transitioning to a higher risk mode: {self.current_mode.name} -> {new_mode.name}")
        
        # Store previous mode
        self.previous_mode = self.current_mode
        
        # Update current mode
        self.current_mode = new_mode
        
        # Update environment variables
        self._update_env_vars()
        
        logger.info(f"Transitioned from {self.previous_mode.name} to {self.current_mode.name}")
        return True
    
    def _update_env_vars(self) -> None:
        """Update environment variables based on current mode."""
        # Set TRADING_MODE environment variable
        os.environ['TRADING_MODE'] = self.current_mode.value.split('_')[0]  # 'live', 'paper', etc.
        
        # Set individual mode flags
        os.environ['PAPER_TRADING'] = str(self.current_mode == TradingMode.PAPER).lower()
        os.environ['BACKTESTING_ENABLED'] = str(self.current_mode == TradingMode.BACKTEST).lower()
        
        # Set DRY_RUN based on mode
        # In simulation and paper trading, we don't execute real transactions
        os.environ['DRY_RUN'] = str(self.current_mode in (TradingMode.SIMULATION, TradingMode.PAPER)).lower()
        
        # Set TRADING_ENABLED based on mode
        # In live and paper trading, trading is enabled
        os.environ['TRADING_ENABLED'] = str(self.current_mode in (TradingMode.LIVE, TradingMode.PAPER)).lower()
    
    def get_mode_config(self) -> Dict[str, Any]:
        """
        Get mode-specific configuration.
        
        Returns:
            Dictionary containing mode-specific configuration
        """
        if not self.config:
            logger.warning("No configuration provided to mode manager")
            return {}
        
        # Get base configuration
        mode_config = {}
        
        # Add mode-specific configuration
        if self.current_mode.value in self.config:
            mode_config.update(self.config[self.current_mode.value])
        
        return mode_config
    
    def is_mode(self, mode: TradingMode) -> bool:
        """
        Check if the system is in the specified mode.
        
        Args:
            mode: Mode to check
            
        Returns:
            True if the system is in the specified mode, False otherwise
        """
        return self.current_mode == mode
    
    def is_live_trading(self) -> bool:
        """
        Check if the system is in live trading mode.
        
        Returns:
            True if the system is in live trading mode, False otherwise
        """
        return self.current_mode == TradingMode.LIVE
    
    def is_paper_trading(self) -> bool:
        """
        Check if the system is in paper trading mode.
        
        Returns:
            True if the system is in paper trading mode, False otherwise
        """
        return self.current_mode == TradingMode.PAPER
    
    def is_backtesting(self) -> bool:
        """
        Check if the system is in backtesting mode.
        
        Returns:
            True if the system is in backtesting mode, False otherwise
        """
        return self.current_mode == TradingMode.BACKTEST
    
    def is_simulation(self) -> bool:
        """
        Check if the system is in simulation mode.
        
        Returns:
            True if the system is in simulation mode, False otherwise
        """
        return self.current_mode == TradingMode.SIMULATION
    
    def is_dry_run(self) -> bool:
        """
        Check if the system is in dry run mode.
        
        Returns:
            True if the system is in dry run mode, False otherwise
        """
        return self.current_mode in (TradingMode.SIMULATION, TradingMode.PAPER)
    
    def requires_confirmation(self, target_mode: TradingMode) -> bool:
        """
        Check if transitioning to the target mode requires confirmation.
        
        Args:
            target_mode: Target mode to transition to
            
        Returns:
            True if confirmation is required, False otherwise
        """
        # Transitions to higher risk modes require confirmation
        return self.risk_levels[target_mode] > self.risk_levels[self.current_mode]
    
    def get_valid_transitions(self) -> List[TradingMode]:
        """
        Get valid transitions from the current mode.
        
        Returns:
            List of valid transition modes
        """
        return list(self.valid_transitions[self.current_mode])
    
    def get_mode_info(self) -> Dict[str, Any]:
        """
        Get information about the current mode.
        
        Returns:
            Dictionary containing information about the current mode
        """
        return {
            "current_mode": self.current_mode.name,
            "current_mode_value": self.current_mode.value,
            "previous_mode": self.previous_mode.name if self.previous_mode else None,
            "risk_level": self.risk_levels[self.current_mode],
            "is_dry_run": self.is_dry_run(),
            "valid_transitions": [mode.name for mode in self.get_valid_transitions()]
        }
