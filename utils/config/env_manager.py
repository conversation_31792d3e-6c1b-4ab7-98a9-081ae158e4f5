#!/usr/bin/env python3
"""
Environment Variable Manager for Synergy7 System

This module provides a unified interface for managing environment variables in the Synergy7 system.
It handles loading, validation, type conversion, and access to environment variables with proper
error handling and default values.

Usage:
    from utils.config.env_manager import EnvManager
    
    # Initialize with default .env file
    env_manager = EnvManager()
    
    # Or specify custom .env file
    env_manager = EnvManager(env_file=".custom_env")
    
    # Load environment variables
    env_manager.load()
    
    # Access environment variables with type conversion
    api_key = env_manager.get("HELIUS_API_KEY")
    max_retries = env_manager.get_int("MAX_RETRIES", 3)  # With default value
    is_enabled = env_manager.get_bool("FEATURE_ENABLED", False)  # With default value
    
    # Check if environment variables are set
    if env_manager.is_set("HELIUS_API_KEY"):
        # Use Helius API
        pass
"""

import os
import json
import logging
from typing import Any, Dict, List, Optional, Union, Set, Type, TypeVar, cast
from pathlib import Path
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('env_manager')

# Type variable for generic type conversion
T = TypeVar('T')

class EnvValidationError(Exception):
    """Exception raised for environment variable validation errors."""
    pass

class EnvManager:
    """
    Environment Variable Manager for Synergy7 System.
    
    This class provides a unified interface for managing environment variables with
    proper error handling, type conversion, and default values.
    """
    
    def __init__(self, env_file: str = ".env", schema_path: Optional[str] = None):
        """
        Initialize the EnvManager.
        
        Args:
            env_file: Path to the environment file
            schema_path: Path to the JSON Schema file for validation
        """
        self.env_file = Path(env_file)
        self.schema_path = Path(schema_path) if schema_path else None
        self.env_vars: Dict[str, str] = {}
        self.loaded = False
        
        # Define required environment variables
        self.required_vars: Set[str] = {
            "HELIUS_API_KEY",
            "WALLET_ADDRESS"
        }
        
        # Define environment variable types
        self.var_types: Dict[str, Type] = {
            # API Keys
            "HELIUS_API_KEY": str,
            "BIRDEYE_API_KEY": str,
            "COINGECKO_API_KEY": str,
            "LILJITO_QUICKNODE_API_KEY": str,
            
            # Wallet Configuration
            "WALLET_ADDRESS": str,
            
            # Mode Flags
            "TRADING_ENABLED": bool,
            "BACKTESTING_ENABLED": bool,
            "PAPER_TRADING": bool,
            "DRY_RUN": bool,
            
            # Risk Management
            "MAX_POSITION_SIZE": float,
            "MAX_EXPOSURE": float,
            "MAX_DRAWDOWN": float,
            "VAR_THRESHOLD": float,
            "STOP_LOSS_PCT": float,
            "TAKE_PROFIT_PCT": float,
            
            # Transaction Configuration
            "MAX_TRANSACTION_FEE": int,
            "MAX_PRIORITY_FEE": int,
            "SLIPPAGE_TOLERANCE": float,
            "MAX_SPREAD_PCT": float,
            "MAX_RETRIES": int,
            "RETRY_DELAY_MS": int,
            
            # System Settings
            "LOG_LEVEL": str,
            "RUST_BACKTRACE": int,
            "PYTHONUNBUFFERED": int,
            "RUST_LOG": str,
            
            # Streamlit Settings
            "STREAMLIT_SERVER_PORT": int,
            "STREAMLIT_SERVER_HEADLESS": bool,
            "STREAMLIT_BROWSER_GATHER_USAGE_STATS": bool
        }
    
    def load(self, validate: bool = True) -> Dict[str, str]:
        """
        Load environment variables from the .env file.
        
        Args:
            validate: Whether to validate required environment variables
            
        Returns:
            Dictionary of loaded environment variables
            
        Raises:
            EnvValidationError: If validation is enabled and required variables are missing
        """
        try:
            if self.env_file.exists():
                # Load environment variables from .env file
                load_dotenv(self.env_file)
                logger.info(f"Loaded environment variables from {self.env_file}")
            else:
                logger.warning(f"Environment file not found: {self.env_file}")
            
            # Store environment variables
            self.env_vars = {key: value.strip() for key, value in os.environ.items()}
            self.loaded = True
            
            # Validate required environment variables
            if validate:
                self._validate_required_vars()
            
            return self.env_vars
        except Exception as e:
            logger.error(f"Error loading environment variables: {str(e)}")
            raise
    
    def _validate_required_vars(self) -> None:
        """
        Validate that all required environment variables are set.
        
        Raises:
            EnvValidationError: If required variables are missing
        """
        missing_vars = [var for var in self.required_vars if not self.is_set(var)]
        if missing_vars:
            error_msg = f"Missing required environment variables: {', '.join(missing_vars)}"
            logger.error(error_msg)
            raise EnvValidationError(error_msg)
    
    def is_set(self, name: str) -> bool:
        """
        Check if an environment variable is set.
        
        Args:
            name: Name of the environment variable
            
        Returns:
            True if the environment variable is set, False otherwise
        """
        if not self.loaded:
            logger.warning("Environment variables not loaded. Call load() first.")
            return False
        
        return name in self.env_vars and self.env_vars[name] != ""
    
    def get(self, name: str, default: Optional[str] = None) -> Optional[str]:
        """
        Get an environment variable value.
        
        Args:
            name: Name of the environment variable
            default: Default value to return if the variable is not set
            
        Returns:
            Value of the environment variable or default value
        """
        if not self.loaded:
            logger.warning("Environment variables not loaded. Call load() first.")
            return default
        
        return self.env_vars.get(name, default)
    
    def get_typed(self, name: str, expected_type: Type[T], default: Optional[T] = None) -> Optional[T]:
        """
        Get an environment variable value with type conversion.
        
        Args:
            name: Name of the environment variable
            expected_type: Expected type of the variable
            default: Default value to return if the variable is not set
            
        Returns:
            Value of the environment variable converted to the expected type, or default value
            
        Raises:
            ValueError: If the variable cannot be converted to the expected type
        """
        value = self.get(name)
        if value is None:
            return default
        
        try:
            if expected_type == bool:
                # Handle boolean conversion
                return cast(T, value.lower() in ('true', 'yes', '1', 'y', 'on'))
            else:
                # Handle other type conversions
                return cast(T, expected_type(value))
        except ValueError as e:
            logger.error(f"Error converting environment variable {name} to {expected_type.__name__}: {str(e)}")
            return default
    
    def get_int(self, name: str, default: Optional[int] = None) -> Optional[int]:
        """
        Get an environment variable as an integer.
        
        Args:
            name: Name of the environment variable
            default: Default value to return if the variable is not set
            
        Returns:
            Value of the environment variable as an integer, or default value
        """
        return self.get_typed(name, int, default)
    
    def get_float(self, name: str, default: Optional[float] = None) -> Optional[float]:
        """
        Get an environment variable as a float.
        
        Args:
            name: Name of the environment variable
            default: Default value to return if the variable is not set
            
        Returns:
            Value of the environment variable as a float, or default value
        """
        return self.get_typed(name, float, default)
    
    def get_bool(self, name: str, default: Optional[bool] = None) -> Optional[bool]:
        """
        Get an environment variable as a boolean.
        
        Args:
            name: Name of the environment variable
            default: Default value to return if the variable is not set
            
        Returns:
            Value of the environment variable as a boolean, or default value
        """
        return self.get_typed(name, bool, default)
    
    def get_list(self, name: str, delimiter: str = ",", default: Optional[List[str]] = None) -> Optional[List[str]]:
        """
        Get an environment variable as a list of strings.
        
        Args:
            name: Name of the environment variable
            delimiter: Delimiter to split the string
            default: Default value to return if the variable is not set
            
        Returns:
            Value of the environment variable as a list of strings, or default value
        """
        value = self.get(name)
        if value is None:
            return default
        
        return [item.strip() for item in value.split(delimiter) if item.strip()]
    
    def get_json(self, name: str, default: Any = None) -> Any:
        """
        Get an environment variable as a JSON object.
        
        Args:
            name: Name of the environment variable
            default: Default value to return if the variable is not set
            
        Returns:
            Value of the environment variable as a JSON object, or default value
            
        Raises:
            ValueError: If the variable cannot be parsed as JSON
        """
        value = self.get(name)
        if value is None:
            return default
        
        try:
            return json.loads(value)
        except json.JSONDecodeError as e:
            logger.error(f"Error parsing environment variable {name} as JSON: {str(e)}")
            return default
    
    def set(self, name: str, value: str) -> None:
        """
        Set an environment variable.
        
        Args:
            name: Name of the environment variable
            value: Value to set
        """
        os.environ[name] = value
        self.env_vars[name] = value
    
    def get_all(self) -> Dict[str, str]:
        """
        Get all environment variables.
        
        Returns:
            Dictionary of all environment variables
        """
        if not self.loaded:
            logger.warning("Environment variables not loaded. Call load() first.")
            return {}
        
        return self.env_vars.copy()
    
    def get_with_prefix(self, prefix: str) -> Dict[str, str]:
        """
        Get all environment variables with a specific prefix.
        
        Args:
            prefix: Prefix to filter environment variables
            
        Returns:
            Dictionary of environment variables with the specified prefix
        """
        if not self.loaded:
            logger.warning("Environment variables not loaded. Call load() first.")
            return {}
        
        return {key: value for key, value in self.env_vars.items() if key.startswith(prefix)}
    
    def save_to_file(self, file_path: Optional[str] = None) -> None:
        """
        Save current environment variables to a file.
        
        Args:
            file_path: Path to the file to save to (defaults to self.env_file)
        """
        if not self.loaded:
            logger.warning("Environment variables not loaded. Call load() first.")
            return
        
        file_path = file_path or str(self.env_file)
        
        try:
            with open(file_path, 'w') as f:
                for key, value in sorted(self.env_vars.items()):
                    # Skip environment variables that are not relevant to the application
                    if key.startswith(('_', 'PYTHON', 'PATH')) and key not in self.var_types:
                        continue
                    
                    f.write(f"{key}={value}\n")
            
            logger.info(f"Saved environment variables to {file_path}")
        except Exception as e:
            logger.error(f"Error saving environment variables to {file_path}: {str(e)}")
            raise
