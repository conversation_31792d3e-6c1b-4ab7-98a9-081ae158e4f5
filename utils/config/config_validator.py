#!/usr/bin/env python3
"""
Configuration Validator for Synergy7 System

This module provides functions for validating configuration files against JSON Schema
and performing additional semantic validation.

Usage:
    from utils.config.config_validator import validate_config, ConfigValidationError
    
    try:
        # Validate configuration
        validate_config(config)
        print("Configuration is valid")
    except ConfigValidationError as e:
        print(f"Configuration validation failed: {e}")
"""

import os
import json
import logging
from typing import Any, Dict, List, Optional, Set
from pathlib import Path
from jsonschema import validate, ValidationError

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('config_validator')

class ConfigValidationError(Exception):
    """Exception raised for configuration validation errors."""
    pass

def validate_config(config: Dict[str, Any], schema_path: Optional[str] = None) -> None:
    """
    Validate configuration against JSON Schema and perform semantic validation.
    
    Args:
        config: Configuration dictionary to validate
        schema_path: Path to JSON Schema file (default: config/schemas/config_schema.json)
        
    Raises:
        ConfigValidationError: If the configuration is invalid
    """
    # Validate against JSON Schema
    validate_schema(config, schema_path)
    
    # Perform semantic validation
    validate_mode_settings(config)
    validate_risk_settings(config)
    validate_execution_settings(config)
    validate_api_settings(config)
    
    logger.info("Configuration validation successful")

def validate_schema(config: Dict[str, Any], schema_path: Optional[str] = None) -> None:
    """
    Validate configuration against JSON Schema.
    
    Args:
        config: Configuration dictionary to validate
        schema_path: Path to JSON Schema file (default: config/schemas/config_schema.json)
        
    Raises:
        ConfigValidationError: If the configuration is invalid
    """
    if not schema_path:
        schema_path = "config/schemas/config_schema.json"
    
    schema_file = Path(schema_path)
    if not schema_file.exists():
        logger.warning(f"Schema file not found: {schema_file}")
        return
    
    try:
        with open(schema_file, 'r') as f:
            schema = json.load(f)
        
        validate(instance=config, schema=schema)
        logger.info("Schema validation successful")
    except ValidationError as e:
        error_msg = f"Schema validation failed: {e.message}"
        logger.error(error_msg)
        raise ConfigValidationError(error_msg)
    except Exception as e:
        error_msg = f"Error validating configuration against schema: {str(e)}"
        logger.error(error_msg)
        raise ConfigValidationError(error_msg)

def validate_mode_settings(config: Dict[str, Any]) -> None:
    """
    Validate mode settings to ensure only one mode is enabled.
    
    Args:
        config: Configuration dictionary to validate
        
    Raises:
        ConfigValidationError: If multiple modes are enabled or no mode is enabled
    """
    if 'mode' not in config:
        error_msg = "Missing mode section in configuration"
        logger.error(error_msg)
        raise ConfigValidationError(error_msg)
    
    # Check that all required mode flags are present
    required_modes = {"live_trading", "paper_trading", "backtesting", "simulation"}
    for mode in required_modes:
        if mode not in config['mode']:
            error_msg = f"Missing mode flag in configuration: {mode}"
            logger.error(error_msg)
            raise ConfigValidationError(error_msg)
    
    # Check that only one mode is enabled
    enabled_modes = [mode for mode, enabled in config['mode'].items() if enabled]
    if len(enabled_modes) == 0:
        error_msg = "No mode is enabled in configuration"
        logger.error(error_msg)
        raise ConfigValidationError(error_msg)
    elif len(enabled_modes) > 1:
        error_msg = f"Multiple modes are enabled in configuration: {enabled_modes}"
        logger.error(error_msg)
        raise ConfigValidationError(error_msg)
    
    logger.info(f"Mode validation successful: {enabled_modes[0]} is enabled")

def validate_risk_settings(config: Dict[str, Any]) -> None:
    """
    Validate risk management settings.
    
    Args:
        config: Configuration dictionary to validate
        
    Raises:
        ConfigValidationError: If risk settings are invalid
    """
    if 'risk' not in config:
        error_msg = "Missing risk section in configuration"
        logger.error(error_msg)
        raise ConfigValidationError(error_msg)
    
    risk = config['risk']
    
    # Check required fields
    required_fields = ["max_position_size_pct", "stop_loss_pct", "take_profit_pct"]
    for field in required_fields:
        if field not in risk:
            error_msg = f"Missing required field in risk section: {field}"
            logger.error(error_msg)
            raise ConfigValidationError(error_msg)
    
    # Validate percentage fields
    percentage_fields = ["max_position_size_pct", "stop_loss_pct", "take_profit_pct", "max_drawdown_pct"]
    for field in percentage_fields:
        if field in risk and not (0 <= risk[field] <= 1):
            error_msg = f"Percentage field in risk section must be between 0 and 1: {field}"
            logger.error(error_msg)
            raise ConfigValidationError(error_msg)
    
    # Validate numeric fields
    numeric_fields = ["max_position_size_usd", "daily_loss_limit_usd"]
    for field in numeric_fields:
        if field in risk and risk[field] < 0:
            error_msg = f"Numeric field in risk section must be non-negative: {field}"
            logger.error(error_msg)
            raise ConfigValidationError(error_msg)
    
    # Validate boolean fields
    if "circuit_breaker_enabled" in risk and not isinstance(risk["circuit_breaker_enabled"], bool):
        error_msg = "circuit_breaker_enabled field in risk section must be a boolean"
        logger.error(error_msg)
        raise ConfigValidationError(error_msg)
    
    logger.info("Risk settings validation successful")

def validate_execution_settings(config: Dict[str, Any]) -> None:
    """
    Validate execution settings.
    
    Args:
        config: Configuration dictionary to validate
        
    Raises:
        ConfigValidationError: If execution settings are invalid
    """
    if 'execution' not in config:
        error_msg = "Missing execution section in configuration"
        logger.error(error_msg)
        raise ConfigValidationError(error_msg)
    
    execution = config['execution']
    
    # Check required fields
    required_fields = ["slippage_tolerance", "order_type"]
    for field in required_fields:
        if field not in execution:
            error_msg = f"Missing required field in execution section: {field}"
            logger.error(error_msg)
            raise ConfigValidationError(error_msg)
    
    # Validate percentage fields
    percentage_fields = ["slippage_tolerance", "max_spread_pct"]
    for field in percentage_fields:
        if field in execution and not (0 <= execution[field] <= 1):
            error_msg = f"Percentage field in execution section must be between 0 and 1: {field}"
            logger.error(error_msg)
            raise ConfigValidationError(error_msg)
    
    # Validate numeric fields
    numeric_fields = ["min_liquidity_usd", "max_order_retries"]
    for field in numeric_fields:
        if field in execution and execution[field] < 0:
            error_msg = f"Numeric field in execution section must be non-negative: {field}"
            logger.error(error_msg)
            raise ConfigValidationError(error_msg)
    
    # Validate order type
    valid_order_types = ["market", "limit"]
    if execution["order_type"] not in valid_order_types:
        error_msg = f"Invalid order type: {execution['order_type']}. Must be one of {valid_order_types}"
        logger.error(error_msg)
        raise ConfigValidationError(error_msg)
    
    # Validate boolean fields
    boolean_fields = ["retry_failed_orders", "dry_run"]
    for field in boolean_fields:
        if field in execution and not isinstance(execution[field], bool):
            error_msg = f"Field in execution section must be a boolean: {field}"
            logger.error(error_msg)
            raise ConfigValidationError(error_msg)
    
    logger.info("Execution settings validation successful")

def validate_api_settings(config: Dict[str, Any]) -> None:
    """
    Validate API settings.
    
    Args:
        config: Configuration dictionary to validate
        
    Raises:
        ConfigValidationError: If API settings are invalid
    """
    if 'apis' not in config:
        logger.info("No APIs section in configuration, skipping validation")
        return
    
    apis = config['apis']
    
    # Validate Helius API settings
    if 'helius' in apis:
        helius = apis['helius']
        if helius.get('enabled', False):
            if 'api_key' not in helius or not helius['api_key']:
                error_msg = "Missing API key for enabled Helius API"
                logger.error(error_msg)
                raise ConfigValidationError(error_msg)
    
    # Validate Birdeye API settings
    if 'birdeye' in apis:
        birdeye = apis['birdeye']
        if birdeye.get('enabled', False):
            if 'api_key' not in birdeye or not birdeye['api_key']:
                error_msg = "Missing API key for enabled Birdeye API"
                logger.error(error_msg)
                raise ConfigValidationError(error_msg)
    
    # Validate CoinGecko API settings
    if 'coingecko' in apis:
        coingecko = apis['coingecko']
        if coingecko.get('enabled', False):
            if 'api_key' not in coingecko or not coingecko['api_key']:
                error_msg = "Missing API key for enabled CoinGecko API"
                logger.error(error_msg)
                raise ConfigValidationError(error_msg)
    
    logger.info("API settings validation successful")

def validate_file_paths(config: Dict[str, Any]) -> None:
    """
    Validate file paths in configuration.
    
    Args:
        config: Configuration dictionary to validate
        
    Raises:
        ConfigValidationError: If file paths are invalid
    """
    # Validate wallet data directory
    if 'wallet' in config and 'data_dir' in config['wallet']:
        data_dir = Path(config['wallet']['data_dir'])
        if not data_dir.exists():
            logger.warning(f"Wallet data directory does not exist: {data_dir}")
    
    # Validate backtest data source
    if 'backtest' in config and 'data_source' in config['backtest']:
        data_source = Path(config['backtest']['data_source'])
        if not data_source.exists():
            logger.warning(f"Backtest data source does not exist: {data_source}")
    
    # Validate Carbon Core binary path
    if 'carbon_core' in config and 'binary_path' in config['carbon_core']:
        binary_path = Path(config['carbon_core']['binary_path'])
        if not binary_path.exists():
            logger.warning(f"Carbon Core binary does not exist: {binary_path}")
    
    logger.info("File path validation successful")

def validate_environment_variables(config: Dict[str, Any]) -> None:
    """
    Validate environment variables referenced in configuration.
    
    Args:
        config: Configuration dictionary to validate
        
    Raises:
        ConfigValidationError: If required environment variables are missing
    """
    # Extract environment variable references from configuration
    env_vars = set()
    
    def extract_env_vars(value):
        if isinstance(value, str) and '${' in value and '}' in value:
            start = value.find('${')
            end = value.find('}', start)
            if start != -1 and end != -1:
                env_var = value[start+2:end]
                env_vars.add(env_var)
    
    def traverse_dict(d):
        for key, value in d.items():
            if isinstance(value, dict):
                traverse_dict(value)
            elif isinstance(value, list):
                for item in value:
                    if isinstance(item, dict):
                        traverse_dict(item)
                    else:
                        extract_env_vars(item)
            else:
                extract_env_vars(value)
    
    traverse_dict(config)
    
    # Check if environment variables are set
    missing_vars = [var for var in env_vars if var not in os.environ]
    if missing_vars:
        logger.warning(f"Missing environment variables referenced in configuration: {missing_vars}")
    
    logger.info("Environment variable validation successful")
