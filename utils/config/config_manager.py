#!/usr/bin/env python3
"""
Configuration Manager for Synergy7 System

This module provides a unified configuration management system for the Synergy7 trading system.
It handles loading, validation, and access to configuration from multiple sources with a clear
precedence order:

1. Command-line arguments (highest priority)
2. Environment variables
3. Environment-specific config files (e.g., production.yaml)
4. Main config.yaml (base configuration)
5. Default values (lowest priority)

Usage:
    from utils.config.config_manager import ConfigManager
    
    # Initialize with default paths
    config_manager = ConfigManager()
    
    # Or specify custom paths
    config_manager = ConfigManager(
        config_path="custom_config.yaml",
        env_file=".custom_env",
        environment="production"
    )
    
    # Load configuration
    config = config_manager.load()
    
    # Access configuration values
    rpc_url = config_manager.get("solana.rpc_url")
    max_retries = config_manager.get("solana.max_retries", 3)  # With default value
    
    # Check if in specific mode
    if config_manager.is_mode("live_trading"):
        # Do live trading specific setup
        pass
"""

import os
import sys
import json
import yaml
import logging
import argparse
from typing import Any, Dict, List, Optional, Union, Set
from pathlib import Path
from dotenv import load_dotenv
from jsonschema import validate, ValidationError

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('config_manager')

class ConfigValidationError(Exception):
    """Exception raised for configuration validation errors."""
    pass

class ConfigManager:
    """
    Unified configuration manager for Synergy7 System.
    
    This class handles loading, validation, and access to configuration from multiple sources
    with a clear precedence order.
    """
    
    def __init__(
        self,
        config_path: str = "config.yaml",
        env_file: str = ".env",
        environment: Optional[str] = None,
        schema_path: Optional[str] = "config/schemas/config_schema.json",
        cmd_args: Optional[List[str]] = None
    ):
        """
        Initialize the ConfigManager.
        
        Args:
            config_path: Path to the main configuration file
            env_file: Path to the environment file
            environment: Environment name (e.g., 'production', 'development')
            schema_path: Path to the JSON Schema file for validation
            cmd_args: Command-line arguments to parse
        """
        self.config_path = Path(config_path)
        self.env_file = Path(env_file)
        self.environment = environment
        self.schema_path = Path(schema_path) if schema_path else None
        self.cmd_args = cmd_args or sys.argv[1:]
        
        # Configuration storage
        self.config: Dict[str, Any] = {}
        self.env_vars: Dict[str, str] = {}
        self.cmd_line_args: Dict[str, Any] = {}
        
        # Mode tracking
        self.current_mode: Optional[str] = None
        self.valid_modes: Set[str] = {"live_trading", "paper_trading", "backtesting", "simulation"}
        
        # Load environment variables
        self._load_env_vars()
        
        # Parse command-line arguments
        self._parse_cmd_args()
    
    def _load_env_vars(self) -> None:
        """Load environment variables from the .env file."""
        try:
            if self.env_file.exists():
                # Load environment variables from .env file
                load_dotenv(self.env_file)
                logger.info(f"Loaded environment variables from {self.env_file}")
                
                # Store environment variables for later use
                self.env_vars = {key: value.strip() for key, value in os.environ.items()}
            else:
                logger.warning(f"Environment file not found: {self.env_file}")
        except Exception as e:
            logger.error(f"Error loading environment variables: {str(e)}")
    
    def _parse_cmd_args(self) -> None:
        """Parse command-line arguments."""
        try:
            parser = argparse.ArgumentParser(description='Synergy7 Trading System')
            
            # Add common arguments
            parser.add_argument('--config', type=str, help='Path to configuration file')
            parser.add_argument('--env-file', type=str, help='Path to environment file')
            parser.add_argument('--environment', type=str, help='Environment name (e.g., production, development)')
            parser.add_argument('--mode', type=str, choices=list(self.valid_modes), help='Operating mode')
            parser.add_argument('--log-level', type=str, choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'], help='Logging level')
            
            # Parse known arguments (ignore unknown ones)
            args, _ = parser.parse_known_args(self.cmd_args)
            
            # Update paths if specified in command-line arguments
            if args.config:
                self.config_path = Path(args.config)
            
            if args.env_file:
                self.env_file = Path(args.env_file)
                # Reload environment variables with new path
                self._load_env_vars()
            
            if args.environment:
                self.environment = args.environment
            
            # Store command-line arguments for later use
            self.cmd_line_args = {key: value for key, value in vars(args).items() if value is not None}
            
            logger.debug(f"Parsed command-line arguments: {self.cmd_line_args}")
        except Exception as e:
            logger.error(f"Error parsing command-line arguments: {str(e)}")
    
    def _load_yaml_file(self, file_path: Path) -> Dict[str, Any]:
        """
        Load a YAML configuration file with environment variable substitution.
        
        Args:
            file_path: Path to the YAML file
            
        Returns:
            Dictionary containing the YAML file contents
            
        Raises:
            ConfigValidationError: If the file cannot be loaded or parsed
        """
        try:
            if not file_path.exists():
                logger.warning(f"Configuration file not found: {file_path}")
                return {}
            
            with open(file_path, 'r') as f:
                # Load YAML with environment variable substitution
                content = f.read()
                
                # Replace environment variables
                for key, value in os.environ.items():
                    placeholder = f"${{{key}}}"
                    if placeholder in content:
                        content = content.replace(placeholder, value)
                
                # Parse YAML
                config = yaml.safe_load(content) or {}
                return config
        except yaml.YAMLError as e:
            error_msg = f"Error parsing YAML file {file_path}: {e}"
            logger.error(error_msg)
            raise ConfigValidationError(error_msg)
        except Exception as e:
            error_msg = f"Error loading configuration file {file_path}: {e}"
            logger.error(error_msg)
            raise ConfigValidationError(error_msg)
    
    def _validate_config(self, config: Dict[str, Any]) -> None:
        """
        Validate the configuration against the JSON Schema.
        
        Args:
            config: Configuration dictionary to validate
            
        Raises:
            ConfigValidationError: If the configuration is invalid
        """
        if not self.schema_path or not self.schema_path.exists():
            logger.warning(f"Schema file not found: {self.schema_path}")
            return
        
        try:
            with open(self.schema_path, 'r') as f:
                schema = json.load(f)
            
            validate(instance=config, schema=schema)
            logger.info("Configuration validated successfully against schema")
        except ValidationError as e:
            error_msg = f"Configuration validation failed: {e.message}"
            logger.error(error_msg)
            raise ConfigValidationError(error_msg)
        except Exception as e:
            error_msg = f"Error validating configuration: {str(e)}"
            logger.error(error_msg)
            raise ConfigValidationError(error_msg)
    
    def _validate_mode_settings(self, config: Dict[str, Any]) -> None:
        """
        Validate mode settings to ensure only one mode is enabled.
        
        Args:
            config: Configuration dictionary to validate
            
        Raises:
            ConfigValidationError: If multiple modes are enabled
        """
        if 'mode' not in config:
            logger.warning("No mode section found in configuration")
            return
        
        # Check that all required mode flags are present
        for mode in self.valid_modes:
            if mode not in config['mode']:
                error_msg = f"Missing mode flag in configuration: {mode}"
                logger.error(error_msg)
                raise ConfigValidationError(error_msg)
        
        # Check that only one mode is enabled
        enabled_modes = [mode for mode, enabled in config['mode'].items() if enabled]
        if len(enabled_modes) == 0:
            error_msg = "No mode is enabled in configuration"
            logger.error(error_msg)
            raise ConfigValidationError(error_msg)
        elif len(enabled_modes) > 1:
            error_msg = f"Multiple modes are enabled in configuration: {enabled_modes}"
            logger.error(error_msg)
            raise ConfigValidationError(error_msg)
        
        # Set current mode
        self.current_mode = enabled_modes[0]
        logger.info(f"Current mode: {self.current_mode}")
    
    def _apply_env_overrides(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Apply environment variable overrides to the configuration.
        
        Args:
            config: Configuration dictionary to update
            
        Returns:
            Updated configuration dictionary
        """
        # Mode overrides from environment variables
        if 'TRADING_MODE' in os.environ:
            mode_value = os.environ['TRADING_MODE'].strip().lower()
            if mode_value == 'live':
                self._set_mode(config, 'live_trading')
            elif mode_value == 'paper':
                self._set_mode(config, 'paper_trading')
            elif mode_value == 'backtest':
                self._set_mode(config, 'backtesting')
            elif mode_value == 'simulation':
                self._set_mode(config, 'simulation')
        
        # Individual mode flags
        if 'PAPER_TRADING' in os.environ:
            paper_trading = os.environ['PAPER_TRADING'].strip().lower() == 'true'
            if paper_trading:
                self._set_mode(config, 'paper_trading')
        
        if 'BACKTESTING_ENABLED' in os.environ:
            backtesting = os.environ['BACKTESTING_ENABLED'].strip().lower() == 'true'
            if backtesting:
                self._set_mode(config, 'backtesting')
        
        # DRY_RUN affects execution behavior but doesn't change the mode
        if 'DRY_RUN' in os.environ:
            dry_run = os.environ['DRY_RUN'].strip().lower() == 'true'
            if 'execution' not in config:
                config['execution'] = {}
            config['execution']['dry_run'] = dry_run
        
        # Apply other environment variable overrides
        # This is a simplified implementation - a more comprehensive one would
        # handle nested keys and type conversion
        
        return config
    
    def _set_mode(self, config: Dict[str, Any], mode: str) -> None:
        """
        Set the specified mode and disable all others.
        
        Args:
            config: Configuration dictionary to update
            mode: Mode to enable
        """
        if 'mode' not in config:
            config['mode'] = {}
        
        # Disable all modes
        for valid_mode in self.valid_modes:
            config['mode'][valid_mode] = False
        
        # Enable the specified mode
        config['mode'][mode] = True
        self.current_mode = mode
    
    def _apply_cmd_line_overrides(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Apply command-line argument overrides to the configuration.
        
        Args:
            config: Configuration dictionary to update
            
        Returns:
            Updated configuration dictionary
        """
        # Apply mode override from command-line arguments
        if 'mode' in self.cmd_line_args:
            self._set_mode(config, self.cmd_line_args['mode'])
        
        # Apply log level override
        if 'log_level' in self.cmd_line_args:
            if 'monitoring' not in config:
                config['monitoring'] = {}
            config['monitoring']['log_level'] = self.cmd_line_args['log_level']
        
        return config
    
    def load(self) -> Dict[str, Any]:
        """
        Load the configuration from all sources and apply overrides.
        
        Returns:
            Merged configuration dictionary
            
        Raises:
            ConfigValidationError: If the configuration is invalid
        """
        try:
            # Load base configuration
            self.config = self._load_yaml_file(self.config_path)
            logger.info(f"Loaded base configuration from {self.config_path}")
            
            # Load environment-specific configuration if specified
            if self.environment:
                env_config_path = Path(f"config/environments/{self.environment}.yaml")
                if env_config_path.exists():
                    env_config = self._load_yaml_file(env_config_path)
                    # Merge environment-specific configuration
                    self._merge_configs(self.config, env_config)
                    logger.info(f"Loaded environment-specific configuration from {env_config_path}")
            
            # Apply environment variable overrides
            self.config = self._apply_env_overrides(self.config)
            
            # Apply command-line argument overrides
            self.config = self._apply_cmd_line_overrides(self.config)
            
            # Validate the configuration
            self._validate_config(self.config)
            
            # Validate mode settings
            self._validate_mode_settings(self.config)
            
            return self.config
        except Exception as e:
            logger.error(f"Error loading configuration: {str(e)}")
            raise
    
    def _merge_configs(self, base_config: Dict[str, Any], override_config: Dict[str, Any]) -> None:
        """
        Merge override configuration into base configuration.
        
        Args:
            base_config: Base configuration dictionary to update
            override_config: Override configuration dictionary
        """
        for key, value in override_config.items():
            if isinstance(value, dict) and key in base_config and isinstance(base_config[key], dict):
                # Recursively merge nested dictionaries
                self._merge_configs(base_config[key], value)
            else:
                # Override or add the value
                base_config[key] = value
    
    def get(self, key_path: str, default: Any = None) -> Any:
        """
        Get a value from the configuration using a dot-separated key path.
        
        Args:
            key_path: The dot-separated key path (e.g., "solana.rpc_url")
            default: The default value to return if the key is not found
            
        Returns:
            The value at the specified key path, or the default value if not found
        """
        if not self.config:
            logger.warning("Configuration not loaded. Call load() first.")
            return default
        
        keys = key_path.split('.')
        value = self.config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def is_mode(self, mode: str) -> bool:
        """
        Check if the system is in the specified mode.
        
        Args:
            mode: Mode to check (e.g., 'live_trading', 'paper_trading')
            
        Returns:
            True if the system is in the specified mode, False otherwise
        """
        return self.current_mode == mode
    
    def is_dry_run(self) -> bool:
        """
        Check if the system is in dry run mode.
        
        Returns:
            True if the system is in dry run mode, False otherwise
        """
        return self.get('execution.dry_run', False)
    
    def is_paper_trading(self) -> bool:
        """
        Check if the system is in paper trading mode.
        
        Returns:
            True if the system is in paper trading mode, False otherwise
        """
        return self.is_mode('paper_trading')
    
    def is_live_trading(self) -> bool:
        """
        Check if the system is in live trading mode.
        
        Returns:
            True if the system is in live trading mode, False otherwise
        """
        return self.is_mode('live_trading')
    
    def is_backtesting(self) -> bool:
        """
        Check if the system is in backtesting mode.
        
        Returns:
            True if the system is in backtesting mode, False otherwise
        """
        return self.is_mode('backtesting')
    
    def is_simulation(self) -> bool:
        """
        Check if the system is in simulation mode.
        
        Returns:
            True if the system is in simulation mode, False otherwise
        """
        return self.is_mode('simulation')
