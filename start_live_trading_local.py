#!/usr/bin/env python3
"""
[DEPRECATED] Start Synergy7 System in live trading mode (Local Version).
This script enables live trading mode for the Synergy7 System in a local environment.

WARNING: This entry point is DEPRECATED. Please use phase_4_deployment/unified_runner.py
         for all production deployments.
"""

import warnings
warnings.warn(
    "This entry point (start_live_trading_local.py) is deprecated. "
    "Please use phase_4_deployment/unified_runner.py for all production deployments.",
    DeprecationWarning,
    stacklevel=2
)

import os
import sys
import time
import json
import logging
import asyncio
from datetime import datetime
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("live_trading")

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import phase_4_deployment modules
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), "phase_4_deployment"))

async def check_wallet_balance():
    """Check wallet balance before starting live trading."""
    try:
        # Import necessary modules
        from phase_4_deployment.rpc_execution.helius_client import HeliusClient

        wallet_address = os.environ.get('WALLET_ADDRESS')
        helius_api_key = os.environ.get('HELIUS_API_KEY')
        rpc_url = f'https://mainnet.helius-rpc.com/?api-key={helius_api_key}'

        client = HeliusClient(rpc_url)
        balance_data = await client.get_balance(wallet_address)

        print(f'Wallet: {wallet_address}')

        # Extract the balance in SOL from the response
        if isinstance(balance_data, dict) and 'balance_sol' in balance_data:
            balance_sol = balance_data['balance_sol']
            print(f'Balance: {balance_sol} SOL')

            # Check if balance is sufficient
            if balance_sol < 0.1:
                print('\033[0;31mWARNING: Balance is very low!\033[0m')
                return False
            elif balance_sol < 1.0:
                print('\033[0;33mWARNING: Balance is low!\033[0m')
                return True
            else:
                print('\033[0;32mBalance is sufficient for trading\033[0m')
                return True
        else:
            print(f'Balance data: {balance_data}')
            print('\033[0;31mWARNING: Could not determine balance!\033[0m')
            return False

    except Exception as e:
        logger.error(f"Error checking wallet balance: {str(e)}")
        return False
    finally:
        if 'client' in locals():
            await client.close()

async def run_live_trading():
    """Run the system in live trading mode."""
    try:
        # Import necessary modules
        from phase_4_deployment.data_router.birdeye_scanner import BirdeyeScanner
        from phase_4_deployment.data_router.whale_watcher import main as track_whales
        from phase_4_deployment.core.signal_enricher import main as enrich_signals
        from phase_4_deployment.rpc_execution.tx_builder import TxBuilder
        from phase_4_deployment.rpc_execution.helius_executor import HeliusExecutor

        # Initialize components
        wallet_address = os.environ.get('WALLET_ADDRESS')
        tx_builder = TxBuilder(wallet_address)
        executor = HeliusExecutor()

        logger.info('Starting live trading mode')

        # Create output directory for logs
        output_dir = Path("output/live_trading_logs")
        output_dir.mkdir(parents=True, exist_ok=True)
        log_file = output_dir / f'live_trading_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'

        # Add file handler to logger
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
        logger.addHandler(file_handler)

        logger.info(f'Live trading started at {datetime.now().isoformat()}')
        logger.info(f'Wallet address: {os.environ.get("WALLET_ADDRESS")}')
        logger.info(f'DRY_RUN: {os.environ.get("DRY_RUN")}')
        logger.info(f'PAPER_TRADING: {os.environ.get("PAPER_TRADING")}')
        logger.info(f'TRADING_ENABLED: {os.environ.get("TRADING_ENABLED")}')

        # Run trading loop
        while True:
            try:
                logger.info('Running trading cycle')

                # Scan for tokens
                logger.info('Scanning for tokens')
                api_key = os.environ.get('BIRDEYE_API_KEY', '')
                scanner = BirdeyeScanner(api_key)
                await scanner.scan_for_opportunities()
                await scanner.close()

                # Track whales
                logger.info('Tracking whale activity')
                await track_whales()

                # Enrich signals
                logger.info('Enriching signals')
                await enrich_signals()

                # Check for trading opportunities
                logger.info('Checking for trading opportunities')

                # Example of using tx_builder (in a real implementation, this would use actual signals)
                # Create a signal based on configuration
                # Get values from environment variables or use defaults
                market = os.getenv("TEST_MARKET", "SOL-USDC")
                action = os.getenv("TEST_ACTION", "BUY")
                price = float(os.getenv("TEST_PRICE", "25.10"))
                size = float(os.getenv("TEST_SIZE", "0.1"))  # Small size for testing

                example_signal = {
                    "action": action,
                    "market": market,
                    "price": price,
                    "size": size,
                    "confidence": float(os.getenv("TEST_CONFIDENCE", "0.92")),
                    "timestamp": datetime.now().isoformat()
                }

                logger.info(f"Created test signal: {action} {size} {market} at {price}")

                # In live mode, we build and execute transactions
                dry_run = os.environ.get('DRY_RUN', 'true').lower().strip()
                if dry_run == 'false':
                    logger.info('Live mode - building and executing transactions')
                    tx_message = tx_builder.build_swap_tx(example_signal)
                    if tx_message:
                        logger.info('Transaction built successfully, executing now')
                        try:
                            # Execute the transaction
                            result = await executor.execute_transaction(tx_message)
                            if result and 'signature' in result:
                                logger.info(f"Transaction executed successfully: {result['signature']}")
                            else:
                                logger.warning(f"Transaction execution returned unexpected result: {result}")
                        except Exception as e:
                            logger.error(f"Error executing transaction: {str(e)}")
                else:
                    logger.info('Dry run mode - not building or executing transactions')

                # Wait for next cycle
                cycle_interval = int(os.getenv("TRADING_CYCLE_INTERVAL_SECONDS", "60"))
                logger.info(f'Waiting for next cycle ({cycle_interval} seconds)')
                await asyncio.sleep(cycle_interval)

            except Exception as e:
                logger.error(f'Error in trading cycle: {str(e)}')
                retry_delay = int(os.getenv("ERROR_RETRY_DELAY_SECONDS", "10"))
                logger.info(f'Retrying in {retry_delay} seconds')
                await asyncio.sleep(retry_delay)

    except KeyboardInterrupt:
        logger.info('Live trading stopped by user')
    except Exception as e:
        logger.error(f'Error in live trading: {str(e)}')
    finally:
        if 'executor' in locals():
            await executor.close()
        logger.info('Live trading session ended')

async def main():
    """Main function to start live trading."""
    print("\n======================================")
    print("STARTING SYNERGY7 SYSTEM IN DEVNET TESTING MODE")
    print("======================================\n")

    print("NOTE: Using Solana devnet for testing to avoid API rate limits.")
    print("This will execute real transactions but with test tokens on devnet.")
    print("No real funds will be used in this testing mode.\n")

    # Set environment variables for live trading with 50% of wallet balance
    # Use testnet/devnet to avoid hitting API rate limits
    os.environ['DRY_RUN'] = 'false'  # Set to false for real trading
    os.environ['PAPER_TRADING'] = 'true'  # Set to true to use testnet/devnet
    os.environ['TRADING_ENABLED'] = 'true'  # Set to true for real trading
    os.environ['MAX_POSITION_SIZE'] = '0.5'  # Use 50% of wallet balance
    os.environ['MAX_EXPOSURE'] = '0.5'  # Allow 50% exposure

    # Use devnet RPC URL to avoid hitting mainnet API rate limits
    os.environ['HELIUS_RPC_URL'] = os.environ['HELIUS_RPC_URL'].replace('mainnet', 'devnet')

    print("Environment variables set for live trading:")
    print(f"DRY_RUN: {os.environ.get('DRY_RUN')}")
    print(f"PAPER_TRADING: {os.environ.get('PAPER_TRADING')}")
    print(f"TRADING_ENABLED: {os.environ.get('TRADING_ENABLED')}")
    print(f"WALLET_ADDRESS: {os.environ.get('WALLET_ADDRESS')}\n")

    # Check wallet balance
    print("Checking wallet balance...")
    balance_ok = await check_wallet_balance()

    if not balance_ok:
        print("\nWARNING: Wallet balance check failed. Proceeding anyway.\n")

    print("\nStarting live trading in 5 seconds...")
    print("Press Ctrl+C to stop at any time.\n")

    # Wait 5 seconds before starting
    for i in range(5, 0, -1):
        print(f"Starting in {i} seconds...", end="")
        sys.stdout.flush()
        await asyncio.sleep(1)

    print("\nLive trading started!\n")

    # Run live trading
    await run_live_trading()

if __name__ == "__main__":
    asyncio.run(main())
