# Synergy7 Trading System Environment Variables
# Copy this file to .env and fill in your values
# IMPORTANT: Do not include comments on the same line as values to avoid parsing issues

# ======================================
# API Keys
# ======================================

# Helius API Key - Required for Solana RPC access
# Get from: https://dashboard.helius.dev/dashboard
HELIUS_API_KEY=dda9f776-9a40-447d-9ca4-22a27c21169e

# Birdeye API Key - Required for token price data
# Get from: https://docs.birdeye.so/reference/get-defi-price
BIRDEYE_API_KEY=a2679724762a47b58dde41b20fb55ce9

# CoinGecko API Key - Optional, used for additional price data
# Get from: https://www.coingecko.com/en/api/pricing
COINGECKO_API_KEY=

# Lil' Jito QuickNode API Key - Required for Jito integration
# Get from: https://www.quicknode.com/
LILJITO_QUICKNODE_API_KEY=QN_6bc9e73d888f418682d564eb13db68a8

# ======================================
# Wallet Configuration
# ======================================

# Wallet Address - Required for trading
# Your Solana wallet public address
WALLET_ADDRESS=

# Wallet Private Key - NEVER commit this to version control
# For development only, use secure storage in production
# WALLET_PRIVATE_KEY=

# Path to keypair JSON file - Alternative to private key
# KEYPAIR_PATH=

# ======================================
# RPC URLs
# ======================================

# Helius RPC URL - Primary RPC endpoint
# Uses the HELIUS_API_KEY environment variable
HELIUS_RPC_URL=https://mainnet.helius-rpc.com/?api-key=${HELIUS_API_KEY}

# Fallback RPC URL - Used when primary RPC is unavailable
FALLBACK_RPC_URL=https://api.mainnet-beta.solana.com

# Lil' Jito QuickNode RPC URL - Used for Jito integration
# Uses the LILJITO_QUICKNODE_API_KEY environment variable
LILJITO_QUICKNODE_RPC_URL=https://lil-jito.quiknode.pro/${LILJITO_QUICKNODE_API_KEY}

# ======================================
# Trading Mode Configuration
# ======================================

# Trading Mode - Controls the overall system mode
# Options: live, paper, backtest, simulation
# Only set ONE of these to true
TRADING_MODE=paper

# Individual Mode Flags - These should align with TRADING_MODE
# Set to true or false (lowercase)
TRADING_ENABLED=true
PAPER_TRADING=true
BACKTESTING_ENABLED=false
DRY_RUN=true

# ======================================
# Risk Management Configuration
# ======================================

# Maximum position size as a percentage of wallet balance (0.0 to 1.0)
# Example: 0.5 means 50% of wallet balance
MAX_POSITION_SIZE=0.5

# Maximum exposure as a percentage of wallet balance (0.0 to 1.0)
# Example: 0.5 means 50% of wallet balance
MAX_EXPOSURE=0.5

# Maximum drawdown as a percentage (0.0 to 1.0)
# Example: 0.15 means 15% drawdown
MAX_DRAWDOWN=0.15

# Value at Risk threshold (0.0 to 1.0)
# Example: 0.05 means 5% VaR
VAR_THRESHOLD=0.05

# Stop loss percentage (0.0 to 1.0)
# Example: 0.10 means 10% loss
STOP_LOSS_PCT=0.10

# Take profit percentage (0.0 to 1.0)
# Example: 0.20 means 20% gain
TAKE_PROFIT_PCT=0.20

# ======================================
# Transaction Configuration
# ======================================

# Maximum transaction fee in lamports
# Example: 20000 lamports
MAX_TRANSACTION_FEE=20000

# Maximum priority fee in lamports
# Example: 2000 lamports
MAX_PRIORITY_FEE=2000

# Slippage tolerance as a percentage (0.0 to 1.0)
# Example: 0.02 means 2% slippage
SLIPPAGE_TOLERANCE=0.02

# Maximum spread percentage (0.0 to 1.0)
# Example: 0.03 means 3% spread
MAX_SPREAD_PCT=0.03

# Order type (market or limit)
ORDER_TYPE=market

# Whether to retry failed orders
# Set to true or false (lowercase)
RETRY_FAILED_ORDERS=true

# Maximum number of retries for failed orders
MAX_ORDER_RETRIES=5

# Retry delay in milliseconds
RETRY_DELAY_MS=1000

# Minimum lamports for test transactions
MIN_TEST_LAMPORTS=1000

# ======================================
# Notification Settings
# ======================================

# Telegram Bot Token - Optional, for notifications
# Get from: https://core.telegram.org/bots#creating-a-new-bot
TELEGRAM_BOT_TOKEN=

# Telegram Chat ID - Required if TELEGRAM_BOT_TOKEN is set
# Get from: https://t.me/userinfobot
TELEGRAM_CHAT_ID=5135869709

# ======================================
# Database Configuration
# ======================================

# Database connection string - Optional, for persistent storage
DB_CONNECTION_STRING=

# ======================================
# Monitoring Configuration
# ======================================

# Metrics port for Prometheus/Grafana
METRICS_PORT=9091

# Metrics refresh interval in seconds
REFRESH_INTERVAL=5

# ======================================
# Timing Configuration
# ======================================

# Trading cycle interval in seconds
TRADING_CYCLE_INTERVAL_SECONDS=60

# Error retry delay in seconds
ERROR_RETRY_DELAY_SECONDS=10

# ======================================
# Test Transaction Configuration
# ======================================

# Test market for example signals
TEST_MARKET=SOL-USDC

# Test action for example signals
TEST_ACTION=BUY

# Test price for example signals
TEST_PRICE=25.10

# Test size for example signals
TEST_SIZE=0.1

# Test confidence for example signals
TEST_CONFIDENCE=0.92

# ======================================
# Paper Trading Simulation Defaults
# ======================================

# Default market for paper trading simulation
DEFAULT_MARKET=SOL-USDC

# Default amount for paper trading simulation
DEFAULT_AMOUNT=0.1

# Default price for paper trading simulation
DEFAULT_PRICE=25.0

# ======================================
# System Settings
# ======================================

# Logging level
# Options: DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_LEVEL=INFO

# Rust backtrace level
# Set to 1 for full backtrace
RUST_BACKTRACE=1

# Python unbuffered output
# Set to 1 to disable output buffering
PYTHONUNBUFFERED=1

# Rust logging level
# Options: trace, debug, info, warn, error
RUST_LOG=info

# ======================================
# Stream Data Configuration
# ======================================

# Stream buffer size
STREAM_BUFFER_SIZE=1000

# Stream reconnect interval in seconds
STREAM_RECONNECT_INTERVAL=5.0

# Maximum reconnect attempts
STREAM_MAX_RECONNECT_ATTEMPTS=10

# ======================================
# PyO3 Extension Settings
# ======================================

# Whether to use the Python fallback for Solana transaction utils
# Set to true or false (lowercase)
SOLANA_TX_UTILS_FALLBACK=true

# Whether running in a Docker container
# Set to true or false (lowercase)
DOCKER_CONTAINER=false

# ======================================
# Streamlit Settings
# ======================================

# Streamlit server port
STREAMLIT_SERVER_PORT=8501

# Whether to run Streamlit in headless mode
# Set to true or false (lowercase)
STREAMLIT_SERVER_HEADLESS=false

# Whether to gather Streamlit usage stats
# Set to true or false (lowercase)
STREAMLIT_BROWSER_GATHER_USAGE_STATS=false

# Streamlit theme base
# Options: light, dark
STREAMLIT_THEME_BASE=dark

# Streamlit primary color
STREAMLIT_THEME_PRIMARY_COLOR=#FF4B4B
