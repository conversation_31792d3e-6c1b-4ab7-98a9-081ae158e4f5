#!/usr/bin/env python3
"""
Simple Telegram Alert Test

This script tests sending a message to Telegram using the bot token and chat ID.
"""

import os
import sys
import json
import logging
import asyncio
import httpx
from datetime import datetime
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("test_telegram")

async def send_telegram_message(bot_token, chat_id, message):
    """Send a message to Telegram."""
    url = f"https://api.telegram.org/bot{bot_token}/sendMessage"

    payload = {
        "chat_id": chat_id,
        "text": message,
        "parse_mode": "Markdown"
    }

    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(url, json=payload)
            response.raise_for_status()
            logger.info(f"Sent Telegram message: {response.json()}")
            return True
        except Exception as e:
            logger.error(f"Error sending Telegram message: {str(e)}")
            return False

async def test_telegram_alerts():
    """Test Telegram alerts."""
    # Use hardcoded credentials for testing
    # In production, these would be loaded from environment variables
    telegram_bot_token = "**********************************************"
    telegram_chat_id = "5135869709"

    if not telegram_bot_token or telegram_bot_token == "your_telegram_bot_token_here":
        logger.error("Telegram bot token not configured or is set to default value")
        logger.info("Please set TELEGRAM_BOT_TOKEN in .env.paper")
        return False

    if not telegram_chat_id or telegram_chat_id == "your_telegram_chat_id_here":
        logger.error("Telegram chat ID not configured or is set to default value")
        logger.info("Please set TELEGRAM_CHAT_ID in .env.paper")
        return False

    # Print the actual token for debugging
    logger.info(f"Using Telegram bot token: {telegram_bot_token}")
    logger.info(f"Using Telegram chat ID: {telegram_chat_id}")

    # Send test message
    message = f"""
🚨 *Synergy7 Trading System Alert* 🚨

This is a test alert from the Synergy7 Trading System.

*Type*: Test Alert
*Time*: {datetime.now().isoformat()}

The system is ready for the 24-hour test.
"""

    success = await send_telegram_message(telegram_bot_token, telegram_chat_id, message)

    if success:
        logger.info("Test message sent successfully. Please check your Telegram.")
    else:
        logger.error("Failed to send test message.")

    return success

async def main():
    """Main function."""
    logger.info("Testing Telegram alerts...")

    success = await test_telegram_alerts()

    if success:
        logger.info("Telegram alert test completed. Please check your Telegram for messages.")
    else:
        logger.error("Telegram alert test failed. Please check the configuration.")

if __name__ == "__main__":
    asyncio.run(main())
