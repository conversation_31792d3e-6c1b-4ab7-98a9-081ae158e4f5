#!/usr/bin/env python3
"""
Live Trade Test Dashboard for Real-Time Monitoring.

This dashboard displays real-time data from our small live trade test
including trade execution, P&L, risk metrics, and system performance.
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import json
import os
import sys
from datetime import datetime, timedelta
from pathlib import Path
import time

# Configure page
st.set_page_config(
    page_title="Synergy7 Live Trade Test Dashboard",
    page_icon="💰",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Add project root to path
project_root = Path.cwd()
sys.path.append(str(project_root))

def load_latest_trade_data():
    """Load the latest trade data."""
    try:
        latest_file = Path("output/live_trade_test/dashboard/latest_trade.json")
        if latest_file.exists():
            with open(latest_file, 'r') as f:
                return json.load(f)
        return None
    except Exception as e:
        st.error(f"Error loading trade data: {str(e)}")
        return None

def load_trading_metrics():
    """Load trading metrics summary."""
    try:
        metrics_file = Path("output/live_trade_test/dashboard/trading_metrics.json")
        if metrics_file.exists():
            with open(metrics_file, 'r') as f:
                return json.load(f)
        return None
    except Exception as e:
        st.error(f"Error loading trading metrics: {str(e)}")
        return None

def load_historical_trades():
    """Load historical trade data."""
    try:
        trades_dir = Path("output/live_trade_test/trades")
        if not trades_dir.exists():
            return []
        
        trade_files = sorted(trades_dir.glob("trade_*.json"))
        trades = []
        
        for trade_file in trade_files:
            try:
                with open(trade_file, 'r') as f:
                    trade_data = json.load(f)
                    trades.append(trade_data)
            except Exception as e:
                st.warning(f"Error loading {trade_file}: {str(e)}")
        
        return trades
    except Exception as e:
        st.error(f"Error loading historical trades: {str(e)}")
        return []

def display_test_overview(metrics_data):
    """Display test overview metrics."""
    if not metrics_data:
        st.warning("No trading metrics available")
        return
    
    metrics = metrics_data.get('metrics', {})
    test_config = metrics_data.get('test_config', {})
    
    # Key metrics row
    col1, col2, col3, col4, col5 = st.columns(5)
    
    with col1:
        st.metric(
            "Total Trades",
            metrics.get('total_trades', 0),
            delta=None
        )
    
    with col2:
        success_rate = (metrics.get('successful_trades', 0) / max(metrics.get('total_trades', 1), 1)) * 100
        st.metric(
            "Success Rate",
            f"{success_rate:.1f}%",
            delta=None
        )
    
    with col3:
        current_exposure = metrics.get('current_exposure_usd', 0.0)
        max_exposure = test_config.get('max_total_exposure_usd', 50.0)
        st.metric(
            "Current Exposure",
            f"${current_exposure:.2f}",
            delta=f"${max_exposure:.0f} max"
        )
    
    with col4:
        total_fees = metrics.get('total_fees_usd', 0.0)
        st.metric(
            "Total Fees",
            f"${total_fees:.3f}",
            delta=None
        )
    
    with col5:
        session_duration = metrics_data.get('session_duration_minutes', 0)
        st.metric(
            "Test Duration",
            f"{session_duration:.1f} min",
            delta=None
        )

def display_latest_trade(trade_data):
    """Display latest trade information."""
    if not trade_data:
        st.warning("No trade data available")
        return
    
    st.subheader("💰 Latest Trade")
    
    # Trade overview
    col1, col2, col3 = st.columns(3)
    
    with col1:
        status = trade_data.get('status', 'Unknown')
        status_emoji = {
            'executed': '✅',
            'pending': '⏳',
            'failed': '❌',
            'rejected': '🚫'
        }.get(status, '❓')
        
        st.info(f"**Status**: {status_emoji} {status.title()}")
        st.info(f"**Trade ID**: {trade_data.get('trade_id', 'N/A')}")
        
    with col2:
        timestamp = trade_data.get('timestamp', '')
        if timestamp:
            trade_time = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            st.info(f"**Time**: {trade_time.strftime('%H:%M:%S')}")
        
        trade_type = trade_data.get('type', 'Unknown')
        st.info(f"**Type**: {trade_type.upper()}")
    
    with col3:
        usd_amount = trade_data.get('usd_amount', 0.0)
        sol_amount = trade_data.get('actual_sol_amount', trade_data.get('sol_amount', 0.0))
        st.info(f"**Amount**: ${usd_amount:.2f}")
        st.info(f"**SOL**: {sol_amount:.4f}")
    
    # Trade details
    if trade_data.get('status') == 'executed':
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            sol_price = trade_data.get('sol_price', 0.0)
            execution_price = trade_data.get('execution_price', sol_price)
            st.success(f"**SOL Price**: ${sol_price:.2f}")
            st.success(f"**Execution**: ${execution_price:.2f}")
        
        with col2:
            slippage = trade_data.get('slippage', 0.0) * 100
            fee_usd = trade_data.get('fee_usd', 0.0)
            st.info(f"**Slippage**: {slippage:.2f}%")
            st.info(f"**Fee**: ${fee_usd:.3f}")
        
        with col3:
            duration = trade_data.get('duration_seconds', 0.0)
            st.info(f"**Duration**: {duration:.2f}s")
        
        with col4:
            execution_time = trade_data.get('execution_time', '')
            if execution_time:
                exec_time = datetime.fromisoformat(execution_time.replace('Z', '+00:00'))
                st.info(f"**Executed**: {exec_time.strftime('%H:%M:%S')}")
    
    elif trade_data.get('status') == 'failed':
        st.error(f"**Error**: {trade_data.get('error', 'Unknown error')}")
    
    elif trade_data.get('status') == 'rejected':
        st.warning(f"**Reason**: {trade_data.get('reason', 'Unknown reason')}")

def display_trade_history(historical_trades):
    """Display trade history and analytics."""
    if not historical_trades:
        st.warning("No historical trade data available")
        return
    
    st.subheader("📊 Trade History & Analytics")
    
    # Prepare data for charts
    df_trades = pd.DataFrame(historical_trades)
    
    if len(df_trades) == 0:
        st.info("No trades to display")
        return
    
    # Trade timeline
    col1, col2 = st.columns(2)
    
    with col1:
        # Trade amounts over time
        if 'timestamp' in df_trades.columns and 'usd_amount' in df_trades.columns:
            df_trades['timestamp'] = pd.to_datetime(df_trades['timestamp'])
            df_trades = df_trades.sort_values('timestamp')
            
            fig_amounts = px.line(
                df_trades, x='timestamp', y='usd_amount',
                title='Trade Amounts Over Time',
                labels={'timestamp': 'Time', 'usd_amount': 'Amount (USD)'}
            )
            fig_amounts.update_layout(height=300)
            st.plotly_chart(fig_amounts, use_container_width=True)
    
    with col2:
        # Trade status distribution
        if 'status' in df_trades.columns:
            status_counts = df_trades['status'].value_counts()
            fig_status = px.pie(
                values=status_counts.values,
                names=status_counts.index,
                title='Trade Status Distribution'
            )
            fig_status.update_layout(height=300)
            st.plotly_chart(fig_status, use_container_width=True)
    
    # Trade performance metrics
    executed_trades = df_trades[df_trades['status'] == 'executed']
    
    if len(executed_trades) > 0:
        col1, col2 = st.columns(2)
        
        with col1:
            # Slippage analysis
            if 'slippage' in executed_trades.columns:
                fig_slippage = px.histogram(
                    executed_trades, x='slippage',
                    title='Slippage Distribution',
                    labels={'slippage': 'Slippage', 'count': 'Frequency'}
                )
                fig_slippage.update_layout(height=300)
                st.plotly_chart(fig_slippage, use_container_width=True)
        
        with col2:
            # Fee analysis
            if 'fee_usd' in executed_trades.columns:
                fig_fees = px.bar(
                    executed_trades, x=executed_trades.index, y='fee_usd',
                    title='Trading Fees by Trade',
                    labels={'index': 'Trade Number', 'fee_usd': 'Fee (USD)'}
                )
                fig_fees.update_layout(height=300)
                st.plotly_chart(fig_fees, use_container_width=True)
    
    # Trade details table
    st.subheader("📋 Trade Details")
    
    if len(df_trades) > 0:
        # Select relevant columns for display
        display_columns = ['trade_id', 'timestamp', 'status', 'type', 'usd_amount', 'sol_amount']
        if 'execution_price' in df_trades.columns:
            display_columns.append('execution_price')
        if 'slippage' in df_trades.columns:
            display_columns.append('slippage')
        if 'fee_usd' in df_trades.columns:
            display_columns.append('fee_usd')
        
        # Filter columns that exist
        available_columns = [col for col in display_columns if col in df_trades.columns]
        
        if available_columns:
            display_df = df_trades[available_columns].copy()
            
            # Format columns for better display
            if 'timestamp' in display_df.columns:
                display_df['timestamp'] = display_df['timestamp'].dt.strftime('%H:%M:%S')
            if 'usd_amount' in display_df.columns:
                display_df['usd_amount'] = display_df['usd_amount'].round(2)
            if 'sol_amount' in display_df.columns:
                display_df['sol_amount'] = display_df['sol_amount'].round(4)
            if 'execution_price' in display_df.columns:
                display_df['execution_price'] = display_df['execution_price'].round(2)
            if 'slippage' in display_df.columns:
                display_df['slippage'] = (display_df['slippage'] * 100).round(3)
            if 'fee_usd' in display_df.columns:
                display_df['fee_usd'] = display_df['fee_usd'].round(4)
            
            st.dataframe(display_df, use_container_width=True)
        else:
            st.info("No displayable trade data available")

def main():
    """Main dashboard function."""
    st.title("💰 Synergy7 Live Trade Test Dashboard")
    st.markdown("Real-time monitoring of small live trade test")
    
    # Sidebar controls
    st.sidebar.title("⚙️ Dashboard Controls")
    auto_refresh = st.sidebar.checkbox("Auto-refresh", value=True)
    refresh_interval = st.sidebar.slider("Refresh interval (seconds)", 5, 60, 10)
    
    if st.sidebar.button("🔄 Refresh Now"):
        st.experimental_rerun()
    
    # Load data
    trade_data = load_latest_trade_data()
    metrics_data = load_trading_metrics()
    historical_trades = load_historical_trades()
    
    # Display test overview
    st.header("📊 Test Overview")
    display_test_overview(metrics_data)
    
    st.markdown("---")
    
    # Display latest trade
    display_latest_trade(trade_data)
    
    st.markdown("---")
    
    # Display trade history
    display_trade_history(historical_trades)
    
    # System status
    st.markdown("---")
    st.header("🔧 System Status")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if trade_data:
            st.success("✅ Trade System Active")
        else:
            st.error("❌ No Trade Data")
    
    with col2:
        if metrics_data:
            last_update = metrics_data.get('timestamp', '')
            if last_update:
                update_time = datetime.fromisoformat(last_update.replace('Z', '+00:00'))
                st.info(f"Last Update: {update_time.strftime('%H:%M:%S')}")
        else:
            st.warning("⚠️ No Metrics Data")
    
    with col3:
        trades_count = len(historical_trades)
        st.info(f"📊 Total Trades: {trades_count}")
    
    # Footer
    st.markdown("---")
    st.markdown(f"**Dashboard Updated**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Auto-refresh
    if auto_refresh:
        time.sleep(refresh_interval)
        st.experimental_rerun()

if __name__ == "__main__":
    main()
