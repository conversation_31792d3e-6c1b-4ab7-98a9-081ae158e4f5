#!/usr/bin/env python3
"""
Test script for Phase 1 integration: Enhanced Market Regime Detection and Whale Watching.

This script tests the new enhanced market regime detector and whale signal generator
to ensure they work correctly with the configuration-driven architecture.
"""

import os
import sys
import asyncio
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import yaml

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import our new modules
from core.strategies.market_regime_detector import EnhancedMarketRegimeDetector, MarketRegime
from core.strategies.probabilistic_regime import ProbabilisticRegimeDetector
from core.data.whale_signal_generator import WhaleSignalGenerator
from core.signals.whale_signal_processor import WhaleSignalProcessor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_test_config():
    """Load test configuration."""
    try:
        # Set test environment variables first
        os.environ.setdefault('MARKET_REGIME_ENABLED', 'true')
        os.environ.setdefault('WHALE_WATCHING_ENABLED', 'true')
        os.environ.setdefault('HMM_ENABLED', 'true')
        os.environ.setdefault('HELIUS_API_KEY', 'test_key')

        # Load and process config with environment variable substitution
        with open('config.yaml', 'r') as f:
            config_text = f.read()

        # Simple environment variable substitution
        import re
        def replace_env_var(match):
            var_expr = match.group(1)
            if ':-' in var_expr:
                var_name, default_value = var_expr.split(':-', 1)
                return os.environ.get(var_name, default_value)
            else:
                return os.environ.get(var_expr, '')

        config_text = re.sub(r'\$\{([^}]+)\}', replace_env_var, config_text)
        config = yaml.safe_load(config_text)

        # Convert string boolean values to actual booleans
        def convert_booleans(obj):
            if isinstance(obj, dict):
                return {k: convert_booleans(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_booleans(item) for item in obj]
            elif isinstance(obj, str):
                if obj.lower() == 'true':
                    return True
                elif obj.lower() == 'false':
                    return False
                else:
                    # Try to convert to int or float
                    try:
                        if '.' in obj:
                            return float(obj)
                        else:
                            return int(obj)
                    except ValueError:
                        return obj
            return obj

        config = convert_booleans(config)
        return config
    except Exception as e:
        logger.error(f"Error loading config: {str(e)}")
        return {}

def generate_test_market_data(days=100):
    """Generate synthetic market data for testing."""
    logger.info(f"Generating {days} days of test market data...")

    # Generate dates
    dates = pd.date_range(start=datetime.now() - timedelta(days=days), periods=days*24, freq='H')

    # Generate synthetic price data with different regimes
    np.random.seed(42)

    # Start with base price
    base_price = 25.0
    prices = [base_price]
    volumes = []

    for i in range(len(dates) - 1):
        # Create different market regimes
        if i < len(dates) * 0.3:  # Trending up
            trend = 0.001
            volatility = 0.02
        elif i < len(dates) * 0.6:  # Ranging
            trend = 0.0
            volatility = 0.015
        elif i < len(dates) * 0.8:  # Volatile
            trend = 0.0005
            volatility = 0.04
        else:  # Choppy
            trend = np.random.choice([-0.002, 0.002])
            volatility = 0.025

        # Generate price change
        change = np.random.normal(trend, volatility)
        new_price = prices[-1] * (1 + change)
        prices.append(max(0.1, new_price))  # Ensure positive prices

        # Generate volume
        base_volume = 1000000
        volume_change = np.random.normal(0, 0.3)
        volume = base_volume * (1 + volume_change)
        volumes.append(max(100000, volume))

    # Add final volume
    volumes.append(volumes[-1])

    # Create OHLCV data
    df = pd.DataFrame({
        'timestamp': dates,
        'open': prices,
        'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
        'close': prices,
        'volume': volumes
    })

    # Ensure high >= close >= low and high >= open >= low
    df['high'] = df[['open', 'close', 'high']].max(axis=1)
    df['low'] = df[['open', 'close', 'low']].min(axis=1)

    logger.info(f"Generated market data: {len(df)} rows, price range ${df['close'].min():.2f} - ${df['close'].max():.2f}")
    return df

def test_enhanced_regime_detector(config, market_data):
    """Test the enhanced market regime detector."""
    logger.info("Testing Enhanced Market Regime Detector...")

    try:
        # Initialize detector
        detector = EnhancedMarketRegimeDetector(config)

        # Test regime detection
        regime, metrics, probabilities = detector.detect_regime(market_data)

        logger.info(f"Detected regime: {regime.value}")
        logger.info(f"Regime metrics: {metrics}")
        logger.info(f"Regime probabilities: {probabilities}")

        # Test strategy recommendations
        recommendations = detector.get_strategy_recommendation(regime, probabilities)
        logger.info(f"Strategy recommendations: {recommendations}")

        # Test market analysis
        analysis = detector.analyze_market(market_data)
        logger.info(f"Market analysis completed. Regime change: {analysis['regime_change']}")

        # Test regime history
        history = detector.get_regime_history(5)
        logger.info(f"Regime history length: {len(history)}")

        return True

    except Exception as e:
        logger.error(f"Error testing enhanced regime detector: {str(e)}")
        return False

def test_probabilistic_regime_detector(config, market_data):
    """Test the probabilistic regime detector."""
    logger.info("Testing Probabilistic Regime Detector...")

    try:
        # Initialize detector
        detector = ProbabilisticRegimeDetector(config)

        # Test model training
        training_success = detector.train_model(market_data)
        logger.info(f"Model training success: {training_success}")

        if training_success:
            # Test regime prediction
            probabilities = detector.predict_regime_probabilities(market_data)
            logger.info(f"Predicted regime probabilities: {probabilities}")

            # Test regime stability
            stability = detector.get_regime_stability()
            logger.info(f"Regime stability: {stability:.3f}")

            # Test model info
            model_info = detector.get_model_info()
            logger.info(f"Model info: {model_info}")

        return True

    except Exception as e:
        logger.error(f"Error testing probabilistic regime detector: {str(e)}")
        return False

async def test_whale_signal_generator(config):
    """Test the whale signal generator."""
    logger.info("Testing Whale Signal Generator...")

    try:
        # Initialize generator
        generator = WhaleSignalGenerator(config)

        # Test whale discovery (will use mock data)
        new_whales = await generator.discover_new_whales()
        logger.info(f"Discovered whales: {len(new_whales)}")

        # Test whale transactions (will use mock data)
        transactions = await generator.get_whale_transactions()
        logger.info(f"Retrieved transactions: {len(transactions)}")

        # Test signal generation
        signals = generator.generate_whale_signals(transactions)
        logger.info(f"Generated signals: {len(signals)}")

        for token_addr, signal in signals.items():
            logger.info(f"Signal for {token_addr}: direction={signal['signal_direction']}, strength={signal['signal_strength']:.3f}")

        # Test signal updates
        updated_signals = await generator.update_whale_signals()
        logger.info(f"Updated signals: {len(updated_signals)}")

        # Test signal summary
        summary = generator.get_signal_summary()
        logger.info(f"Signal summary: {summary}")

        # Close generator
        await generator.close()

        return True

    except Exception as e:
        logger.error(f"Error testing whale signal generator: {str(e)}")
        return False

def test_whale_signal_processor(config, market_data):
    """Test the whale signal processor."""
    logger.info("Testing Whale Signal Processor...")

    try:
        # Initialize processor
        processor = WhaleSignalProcessor(config)

        # Create mock whale signal
        mock_signal = {
            'token_address': 'So11111111111111111111111111111111111111112',
            'signal_direction': 1,
            'signal_strength': 0.75,
            'confidence': 0.8,
            'whale_count': 5,
            'total_volume_usd': 750000,
            'net_buying_ratio': 0.6,
            'timestamp': datetime.now().isoformat(),
            'decay_time': (datetime.now() + timedelta(hours=6)).isoformat()
        }

        # Test signal validation
        is_valid = processor.validate_whale_signal(mock_signal)
        logger.info(f"Signal validation: {is_valid}")

        # Test signal processing
        processed_signal = processor.process_whale_signal(mock_signal, market_data)
        if processed_signal:
            logger.info(f"Processed signal: {processed_signal['signal_type']}, strength={processed_signal['strength']:.3f}")

        # Test signal recommendation
        if processed_signal:
            recommendation = processor.get_signal_recommendation(processed_signal)
            logger.info(f"Trading recommendation: {recommendation['action']}, position_size={recommendation['position_size_pct']:.3f}")

        # Test processing summary
        summary = processor.get_processing_summary()
        logger.info(f"Processing summary: {summary}")

        return True

    except Exception as e:
        logger.error(f"Error testing whale signal processor: {str(e)}")
        return False

async def main():
    """Main test function."""
    logger.info("Starting Phase 1 Integration Tests...")

    # Load configuration
    config = load_test_config()
    if not config:
        logger.error("Failed to load configuration")
        return False

    # Generate test market data
    market_data = generate_test_market_data(days=60)

    # Test results
    results = {}

    # Test enhanced regime detector
    results['enhanced_regime_detector'] = test_enhanced_regime_detector(config, market_data)

    # Test probabilistic regime detector
    results['probabilistic_regime_detector'] = test_probabilistic_regime_detector(config, market_data)

    # Test whale signal generator
    results['whale_signal_generator'] = await test_whale_signal_generator(config)

    # Test whale signal processor
    results['whale_signal_processor'] = test_whale_signal_processor(config, market_data)

    # Print results summary
    logger.info("\n" + "="*50)
    logger.info("PHASE 1 INTEGRATION TEST RESULTS")
    logger.info("="*50)

    all_passed = True
    for test_name, passed in results.items():
        status = "PASS" if passed else "FAIL"
        logger.info(f"{test_name}: {status}")
        if not passed:
            all_passed = False

    logger.info("="*50)
    overall_status = "ALL TESTS PASSED" if all_passed else "SOME TESTS FAILED"
    logger.info(f"Overall Status: {overall_status}")
    logger.info("="*50)

    return all_passed

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
