#!/usr/bin/env python3
"""
Wrap<PERSON> Sc<PERSON> for Synergy7 Dashboard

This script sets up the Python path correctly and runs the Streamlit dashboard.
This is a standalone entry point for running only the dashboard without starting
the trading system.

NOTE: For full system deployment including the dashboard, use phase_4_deployment/unified_runner.py
"""

import os
import sys
import subprocess

def main():
    """Main function to run the dashboard."""
    # Get the absolute path to the project root
    project_root = os.path.dirname(os.path.abspath(__file__))

    # Add the project root to the Python path
    sys.path.insert(0, project_root)

    # Set the PYTHONPATH environment variable
    os.environ["PYTHONPATH"] = project_root + os.pathsep + os.environ.get("PYTHONPATH", "")

    # Path to the dashboard script
    dashboard_script = os.path.join(project_root, "phase_4_deployment", "monitoring", "streamlit_dashboard.py")

    # Check if the dashboard script exists
    if not os.path.exists(dashboard_script):
        print(f"Error: Dashboard script not found at {dashboard_script}")
        return 1

    # Print information
    print(f"Running dashboard with Python path: {sys.path}")
    print(f"Dashboard script: {dashboard_script}")

    # Run the dashboard using Streamlit
    try:
        result = subprocess.run(
            ["streamlit", "run", dashboard_script],
            env=os.environ,
            check=True
        )
        return result.returncode
    except subprocess.CalledProcessError as e:
        print(f"Error running dashboard: {e}")
        return e.returncode
    except FileNotFoundError:
        print("Error: Streamlit not found. Please install it with 'pip install streamlit'")
        return 1

if __name__ == "__main__":
    sys.exit(main())
