# Deprecated Files List
# This file tracks deprecated files that have been replaced by new implementations

# ===== DEPRECATED DIRECTORIES =====

# Old phase directories (replaced by phase_4_deployment)
phase_0_env_setup/
phase_1_strategy_runner/
phase_2_strategy/
phase_3_rl_agent_training/

# Redundant backtest directories (replaced by phase_4_deployment/backtest)
phase_2_backtest_engine/data/
phase_2_backtest_engine/output/
phase_2_backtest_engine/outputs/

# Backup directories
backup/
backups/
phase_4_deployment/backup_dashboards/

# Redundant output directories
output/
output/dashboard/
output/dashboard_tests/
output/live_trade_test/
output/live_trading_logs/
output/logs/
output/mean_reversion/
output/momentum/
output/paper_trading/
output/production_tests/
output/strategy_comparison/
output/validation/
output/visualizations/

# Redundant data directories
data/
data/prepared/
data/raw/
core/data/

# ===== DEPRECATED FILES =====

# Old data files
phase_0_env_setup/data/historical/sol_usdc.csv
phase_0_env_setup/data/historical/jup_usdc.csv
phase_0_env_setup/data/historical/bonk_usdc.csv

# Old strategy implementations
phase_1_strategy_runner/strategies/momentum_strategy.py
phase_1_strategy_runner/strategies/mean_reversion_strategy.py
phase_1_strategy_runner/strategies/breakout_strategy.py
phase_1_strategy_runner/strategies/basic_ma_strategy.py
phase_1_strategy_runner/strategies/meme_alpha_strategy.py
phase_2_strategy/momentum_strategy.py
phase_2_strategy/run_strategy_finder.py
phase_2_strategy/strategy_finder.py
phase_2_strategy/visualize_strategy.py
core/engine/strategy_runner.py

# Old signal generators
phase_1_strategy_runner/signal_generator.py
phase_1_strategy_runner/signal_processor.py
phase_1_strategy_runner/runners/strategy_runner.py

# Old backtest files
phase_2_backtest_engine/backtest_runner.py
phase_2_backtest_engine/backtest_visualizer.py
phase_2_backtest_engine/backtest_metrics.py
phase_2_backtest_engine/utils/strategy_loader.py

# Old simulation files
phase_4_deployment/simulation/simple_simulator.py
phase_4_deployment/simulation/market_simulator.py

# Redundant dashboard files
phase_4_deployment/backup_dashboards/dashboard_simulator.py
phase_4_deployment/backup_dashboards/run_streamlit_dashboard.py
phase_4_deployment/backup_dashboards/streamlit_dashboard.py
phase_4_deployment/backup_dashboards/test_streamlit_dashboard.py
phase_4_deployment/fix_dashboard.py
phase_4_deployment/remove_old_dashboards.py
phase_4_deployment/verify_metrics_dashboard.py
import_dashboard.py
run_dashboard.py

# Redundant test files
phase_4_deployment/test_birdeye.py
phase_4_deployment/test_helius.py
phase_4_deployment/test_monitoring_components.py
phase_4_deployment/test_monitoring.py
phase_4_deployment/test_paper_trading.py
phase_4_deployment/test_python_comm_layer.py
phase_4_deployment/test_stream_data_ingestor.py
phase_4_deployment/test_system.py
phase_4_deployment/test_transaction.py
test_birdeye_api.py
test_carbon_core.py
test_helius.py
test_solana_tx_utils.py
test_transaction.py

# Redundant configuration files
config_example.yaml
configs/config_example.yaml

# ===== ADDITIONAL DEPRECATED FILES (Integration Plan) =====

# Old market regime detection (replaced by enhanced version)
phase_2_strategy/market_regime.py

# Redundant strategy implementations (replaced by unified core strategies)
phase_2_strategy/momentum_strategy.py
phase_2_strategy/mean_reversion.py
phase_2_strategy/risk_management.py
backup/mean_reversion/

# Old whale watching (replaced by enhanced signal generator)
phase_4_deployment/data_router/whale_watcher.py
phase_4_deployment/data_router/birdeye_scanner.py

# Redundant risk management (replaced by enhanced core risk)
phase_2_strategy/risk_management.py

# Old strategy runners (replaced by unified runner)
phase_1_strategy_runner/runners/strategy_runner.py
core/engine/strategy_runner.py

# Redundant configuration files
live_trade_test_config.yaml
paper_trade_config.yaml
carbon_core_config.yaml
carbon_core_fallback_config.yaml

# Redundant test files
test_telegram.py
test_trade_notification.py
test_trading_alerts.py
direct_telegram_test.py

# Redundant runner files
run_synergy7.py
run_q5_system.py
start_live_trading_local.py
unified_runner.py (root level)
paper_trading_simulator.py

# ===== REPLACEMENT INFORMATION =====

# These deprecated files and directories have been replaced by the new implementations in:
# - core/strategies/ - Enhanced strategy implementations with regime detection
# - core/data/ - Unified data ingestion and whale signal generation
# - core/risk/ - Advanced risk management with VaR/CVaR
# - core/analytics/ - Strategy performance attribution and analysis
# - core/signals/ - Unified signal processing and enrichment
# - phase_4_deployment/unified_runner.py - Single entry point for all modes
# - config.yaml - Centralized configuration with all parameters

# The new integrated system provides:
# 1. Enhanced market regime detection with probabilistic models
# 2. Whale watching integration as complementary alpha source
# 3. Advanced risk management with VaR/CVaR and correlation analysis
# 4. Strategy performance attribution and adaptive weighting
# 5. Configuration-driven architecture avoiding hard-coded values
# 6. Unified entry point with proper mode switching
# 7. Comprehensive monitoring and alerting system
