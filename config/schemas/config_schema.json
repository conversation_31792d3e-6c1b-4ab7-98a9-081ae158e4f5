{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Synergy7 Configuration Schema", "description": "JSON Schema for validating Synergy7 configuration files", "type": "object", "required": ["mode", "solana", "wallet", "risk", "execution"], "properties": {"quantconnect": {"type": "object", "description": "QuantConnect configuration", "properties": {"project_id": {"type": "string", "description": "QuantConnect project ID"}, "backtest_id": {"type": "string", "description": "QuantConnect backtest ID"}}, "additionalProperties": false}}, "additionalProperties": true}