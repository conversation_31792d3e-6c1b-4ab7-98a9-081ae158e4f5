# Synergy7 Trading System Development Configuration

apis:
  birdeye:
    api_key: ${BIRDEYE_API_KEY}
    enabled: false
    endpoint: https://api.birdeye.so/v1
  helius:
    api_key: ${HELIUS_API_KEY}
    enabled: false
    rpc_endpoint: https://mainnet.helius-rpc.com/?api-key=${HELIUS_API_KEY}
    ws_endpoint: wss://mainnet.helius-rpc.com/?api-key=${HELIUS_API_KEY}
  jito:
    enabled: false
    keypair_path: keys/jito_keypair.json
    rpc_url: https://mainnet.block.jito.io
    shredstream_url: wss://mainnet.shredstream.jito.io/stream

communication:
  heartbeat_interval_ms: 1000
  max_message_size: 1048576
  message_format: json
  protocol: zeromq
  zeromq:
    pub_endpoint: tcp://127.0.0.1:5555
    req_endpoint: tcp://127.0.0.1:5557
    sub_endpoint: tcp://127.0.0.1:5556

core:
  binary_path: bin/carbon_core
  enabled: true
  log_level: info
  max_memory_mb: 1024
  update_interval_ms: 100
  worker_threads: 4

market_microstructure:
  enabled: true
  impact_window_size: 50
  liquidity_threshold: 10000
  markets:
  - SOL-USDC
  - JTO-USDC
  order_book_depth: 20
  update_interval_ms: 100

monitoring:
  enabled: true
  health_check_interval_ms: 10000
  log_level: info
  metrics_interval_ms: 5000
  telegram_alerts: false
  telegram_bot_token: ${TELEGRAM_BOT_TOKEN}
  telegram_chat_id: ${TELEGRAM_CHAT_ID}

risk_management:
  enabled: true
  max_drawdown: 0.05
  max_exposure: 0.1
  max_position_size: 0.01
  metrics_interval_ms: 5000
  publish_interval_ms: 1000
  update_interval_ms: 1000
  var_threshold: 0.05

rpc:
  commitment: confirmed
  endpoint: https://api.devnet.solana.com
  max_retries: 3
  retry_delay_ms: 1000

signal_generation:
  enabled: true
  publish_interval_ms: 1000
  update_interval_ms: 1000

strategies:
- enabled: true
  markets:
  - SOL-USDC
  name: momentum_sol_usdc
  parameters:
    max_value: 0.2
    smoothing_factor: 0.3
    threshold: 0.01
    window_size: 20.0
  type: momentum
  weight: 1.0
- enabled: true
  markets:
  - SOL-USDC
  name: order_book_imbalance_sol_usdc
  parameters:
    depth: 10.0
    max_position_size: 0.01
    max_value: 0.5
    smoothing_factor: 0.2
    threshold: 0.1
    window_size: 20.0
  type: order_book_imbalance
  weight: 1.0
- enabled: true
  markets:
  - JTO-USDC
  name: momentum_jto_usdc
  parameters:
    max_value: 0.2
    smoothing_factor: 0.3
    threshold: 0.01
    window_size: 20.0
  type: momentum
  weight: 1.0
# Mean Reversion Strategy - DISABLED
- enabled: false
  markets:
  - SOL-USDC
  name: mean_reversion_sol_usdc
  parameters:
    window_size: 30.0
    std_dev: 2.0
    mean_window: 100.0
    max_position_size: 0.0
  type: mean_reversion
  weight: 0.0

strategy_runner:
  enabled: true
  publish_interval_ms: 1000
  update_interval_ms: 1000

test:
  duration_seconds: 300
  enabled: true
  log_level: debug
  simulate_market_data: true
  simulate_transactions: true

transaction_execution:
  dry_run: true
  enabled: true
  publish_interval_ms: 1000
  simulation_enabled: true
  update_interval_ms: 1000

transaction_preparation:
  enabled: true
  publish_interval_ms: 1000
  update_interval_ms: 1000

wallet:
  address: ${TEST_WALLET_ADDRESS}
  keypair_path: keys/test_wallet_keypair.json
  max_transaction_fee: 10000
