#!/usr/bin/env python3
"""
System Health Monitor for Synergy7 Enhanced Trading System.

This script monitors system health, performance metrics, and generates alerts
for any issues that need attention.
"""

import os
import sys
import asyncio
import logging
import json
import psutil
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, Any, List

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SystemHealthMonitor:
    """Monitors system health and performance metrics."""
    
    def __init__(self):
        self.project_root = Path.cwd()
        self.health_report = {}
        
    def check_system_resources(self) -> Dict[str, Any]:
        """Check system resource usage."""
        try:
            # Memory usage
            memory = psutil.virtual_memory()
            memory_usage = {
                'total_gb': round(memory.total / (1024**3), 1),
                'available_gb': round(memory.available / (1024**3), 1),
                'used_pct': memory.percent,
                'status': 'healthy' if memory.percent < 80 else 'warning' if memory.percent < 90 else 'critical'
            }
            
            # Disk usage
            disk = psutil.disk_usage('.')
            disk_usage = {
                'total_gb': round(disk.total / (1024**3), 1),
                'free_gb': round(disk.free / (1024**3), 1),
                'used_pct': round((disk.used / disk.total) * 100, 1),
                'status': 'healthy' if disk.used / disk.total < 0.8 else 'warning' if disk.used / disk.total < 0.9 else 'critical'
            }
            
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_usage = {
                'cores': psutil.cpu_count(),
                'usage_pct': cpu_percent,
                'status': 'healthy' if cpu_percent < 70 else 'warning' if cpu_percent < 85 else 'critical'
            }
            
            return {
                'memory': memory_usage,
                'disk': disk_usage,
                'cpu': cpu_usage,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error checking system resources: {str(e)}")
            return {}
    
    def check_log_files(self) -> Dict[str, Any]:
        """Check log files for errors and warnings."""
        try:
            log_status = {}
            log_dir = self.project_root / 'logs'
            
            if not log_dir.exists():
                return {'status': 'warning', 'message': 'Log directory not found'}
            
            # Check main log file
            main_log = log_dir / 'synergy7.log'
            if main_log.exists():
                # Get file size and recent errors
                file_size_mb = main_log.stat().st_size / (1024 * 1024)
                
                # Read last 100 lines for recent errors
                try:
                    with open(main_log, 'r') as f:
                        lines = f.readlines()
                        recent_lines = lines[-100:] if len(lines) > 100 else lines
                        
                    error_count = sum(1 for line in recent_lines if 'ERROR' in line)
                    warning_count = sum(1 for line in recent_lines if 'WARNING' in line)
                    
                    log_status['main_log'] = {
                        'size_mb': round(file_size_mb, 2),
                        'recent_errors': error_count,
                        'recent_warnings': warning_count,
                        'status': 'healthy' if error_count == 0 else 'warning' if error_count < 5 else 'critical'
                    }
                except Exception as e:
                    log_status['main_log'] = {'status': 'error', 'message': str(e)}
            
            # Check error log file
            error_log = log_dir / 'errors' / 'synergy7_errors.log'
            if error_log.exists():
                file_size_mb = error_log.stat().st_size / (1024 * 1024)
                modification_time = datetime.fromtimestamp(error_log.stat().st_mtime)
                hours_since_modified = (datetime.now() - modification_time).total_seconds() / 3600
                
                log_status['error_log'] = {
                    'size_mb': round(file_size_mb, 2),
                    'hours_since_modified': round(hours_since_modified, 1),
                    'status': 'healthy' if hours_since_modified > 24 else 'warning'
                }
            
            return log_status
            
        except Exception as e:
            logger.error(f"Error checking log files: {str(e)}")
            return {'status': 'error', 'message': str(e)}
    
    def check_configuration_files(self) -> Dict[str, Any]:
        """Check that all configuration files are present and valid."""
        try:
            config_status = {}
            
            # Check .env file
            env_file = self.project_root / '.env'
            if env_file.exists():
                config_status['env_file'] = {
                    'exists': True,
                    'size_bytes': env_file.stat().st_size,
                    'modified': datetime.fromtimestamp(env_file.stat().st_mtime).isoformat()
                }
            else:
                config_status['env_file'] = {'exists': False, 'status': 'critical'}
            
            # Check config.yaml
            config_file = self.project_root / 'config.yaml'
            if config_file.exists():
                config_status['config_yaml'] = {
                    'exists': True,
                    'size_bytes': config_file.stat().st_size,
                    'modified': datetime.fromtimestamp(config_file.stat().st_mtime).isoformat()
                }
            else:
                config_status['config_yaml'] = {'exists': False, 'status': 'critical'}
            
            # Check wallet config
            wallet_config = self.project_root / 'keys' / 'wallet_config.json'
            if wallet_config.exists():
                config_status['wallet_config'] = {
                    'exists': True,
                    'size_bytes': wallet_config.stat().st_size,
                    'modified': datetime.fromtimestamp(wallet_config.stat().st_mtime).isoformat()
                }
            else:
                config_status['wallet_config'] = {'exists': False, 'status': 'warning'}
            
            return config_status
            
        except Exception as e:
            logger.error(f"Error checking configuration files: {str(e)}")
            return {'status': 'error', 'message': str(e)}
    
    async def check_api_connectivity(self) -> Dict[str, Any]:
        """Check connectivity to external APIs."""
        try:
            import httpx
            
            api_status = {}
            
            # Check Helius API
            helius_key = os.environ.get('HELIUS_API_KEY')
            if helius_key:
                try:
                    async with httpx.AsyncClient() as client:
                        response = await client.post(
                            f"https://rpc.helius.xyz/?api-key={helius_key}",
                            json={"jsonrpc": "2.0", "id": 1, "method": "getHealth"},
                            timeout=10
                        )
                        api_status['helius'] = {
                            'status': 'healthy' if response.status_code == 200 else 'error',
                            'response_code': response.status_code,
                            'response_time_ms': response.elapsed.total_seconds() * 1000
                        }
                except Exception as e:
                    api_status['helius'] = {'status': 'error', 'error': str(e)}
            else:
                api_status['helius'] = {'status': 'not_configured'}
            
            # Check Birdeye API
            birdeye_key = os.environ.get('BIRDEYE_API_KEY')
            if birdeye_key:
                try:
                    async with httpx.AsyncClient() as client:
                        response = await client.get(
                            "https://public-api.birdeye.so/public/tokenlist",
                            headers={"X-API-KEY": birdeye_key},
                            timeout=10
                        )
                        api_status['birdeye'] = {
                            'status': 'healthy' if response.status_code == 200 else 'error',
                            'response_code': response.status_code,
                            'response_time_ms': response.elapsed.total_seconds() * 1000
                        }
                except Exception as e:
                    api_status['birdeye'] = {'status': 'error', 'error': str(e)}
            else:
                api_status['birdeye'] = {'status': 'not_configured'}
            
            return api_status
            
        except Exception as e:
            logger.error(f"Error checking API connectivity: {str(e)}")
            return {'status': 'error', 'message': str(e)}
    
    async def send_health_alert(self, health_report: Dict[str, Any]) -> bool:
        """Send health alert via Telegram if issues detected."""
        try:
            import httpx
            
            bot_token = os.environ.get('TELEGRAM_BOT_TOKEN')
            chat_id = os.environ.get('TELEGRAM_CHAT_ID')
            
            if not bot_token or not chat_id:
                logger.warning("Telegram credentials not configured")
                return False
            
            # Check for critical issues
            critical_issues = []
            warning_issues = []
            
            # Check system resources
            resources = health_report.get('system_resources', {})
            if resources.get('memory', {}).get('status') == 'critical':
                critical_issues.append(f"Memory usage critical: {resources['memory']['used_pct']}%")
            elif resources.get('memory', {}).get('status') == 'warning':
                warning_issues.append(f"Memory usage high: {resources['memory']['used_pct']}%")
            
            if resources.get('disk', {}).get('status') == 'critical':
                critical_issues.append(f"Disk usage critical: {resources['disk']['used_pct']}%")
            elif resources.get('disk', {}).get('status') == 'warning':
                warning_issues.append(f"Disk usage high: {resources['disk']['used_pct']}%")
            
            # Check logs
            logs = health_report.get('log_files', {})
            main_log = logs.get('main_log', {})
            if main_log.get('status') == 'critical':
                critical_issues.append(f"Critical errors in logs: {main_log.get('recent_errors', 0)} errors")
            elif main_log.get('status') == 'warning':
                warning_issues.append(f"Warnings in logs: {main_log.get('recent_warnings', 0)} warnings")
            
            # Send alert if issues found
            if critical_issues or warning_issues:
                alert_level = "🚨 CRITICAL" if critical_issues else "⚠️ WARNING"
                
                message = f"""
{alert_level} *System Health Alert*

*Timestamp*: {datetime.now().isoformat()}

"""
                
                if critical_issues:
                    message += "*Critical Issues*:\n"
                    for issue in critical_issues:
                        message += f"• {issue}\n"
                    message += "\n"
                
                if warning_issues:
                    message += "*Warnings*:\n"
                    for issue in warning_issues:
                        message += f"• {issue}\n"
                    message += "\n"
                
                message += "*System Status*:\n"
                message += f"• Memory: {resources.get('memory', {}).get('used_pct', 0)}% used\n"
                message += f"• Disk: {resources.get('disk', {}).get('used_pct', 0)}% used\n"
                message += f"• CPU: {resources.get('cpu', {}).get('usage_pct', 0)}% used\n"
                
                url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
                payload = {
                    "chat_id": chat_id,
                    "text": message,
                    "parse_mode": "Markdown"
                }
                
                async with httpx.AsyncClient() as client:
                    response = await client.post(url, json=payload)
                    response.raise_for_status()
                    logger.info("Health alert sent successfully")
                    return True
            
            return True
            
        except Exception as e:
            logger.error(f"Error sending health alert: {str(e)}")
            return False
    
    async def generate_health_report(self) -> Dict[str, Any]:
        """Generate comprehensive health report."""
        try:
            logger.info("Generating system health report...")
            
            health_report = {
                'timestamp': datetime.now().isoformat(),
                'system_resources': self.check_system_resources(),
                'log_files': self.check_log_files(),
                'configuration_files': self.check_configuration_files(),
                'api_connectivity': await self.check_api_connectivity()
            }
            
            # Determine overall health status
            statuses = []
            
            # System resources status
            resources = health_report['system_resources']
            statuses.extend([
                resources.get('memory', {}).get('status', 'unknown'),
                resources.get('disk', {}).get('status', 'unknown'),
                resources.get('cpu', {}).get('status', 'unknown')
            ])
            
            # Log files status
            logs = health_report['log_files']
            if isinstance(logs, dict):
                for log_info in logs.values():
                    if isinstance(log_info, dict):
                        statuses.append(log_info.get('status', 'unknown'))
            
            # Overall status
            if 'critical' in statuses:
                overall_status = 'critical'
            elif 'error' in statuses:
                overall_status = 'error'
            elif 'warning' in statuses:
                overall_status = 'warning'
            else:
                overall_status = 'healthy'
            
            health_report['overall_status'] = overall_status
            
            # Save report
            reports_dir = self.project_root / 'reports' / 'daily'
            reports_dir.mkdir(parents=True, exist_ok=True)
            
            report_file = reports_dir / f"health_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(report_file, 'w') as f:
                json.dump(health_report, f, indent=2)
            
            logger.info(f"Health report saved to {report_file}")
            
            # Send alert if needed
            await self.send_health_alert(health_report)
            
            return health_report
            
        except Exception as e:
            logger.error(f"Error generating health report: {str(e)}")
            return {'status': 'error', 'message': str(e)}

async def main():
    """Main function."""
    monitor = SystemHealthMonitor()
    health_report = await monitor.generate_health_report()
    
    # Print summary
    print("\n" + "="*50)
    print("SYSTEM HEALTH REPORT")
    print("="*50)
    
    overall_status = health_report.get('overall_status', 'unknown')
    status_emoji = {
        'healthy': '✅',
        'warning': '⚠️',
        'error': '❌',
        'critical': '🚨'
    }.get(overall_status, '❓')
    
    print(f"Overall Status: {status_emoji} {overall_status.upper()}")
    
    # System resources
    resources = health_report.get('system_resources', {})
    if resources:
        print(f"\nSystem Resources:")
        print(f"  Memory: {resources.get('memory', {}).get('used_pct', 0)}% used")
        print(f"  Disk: {resources.get('disk', {}).get('used_pct', 0)}% used")
        print(f"  CPU: {resources.get('cpu', {}).get('usage_pct', 0)}% used")
    
    # API connectivity
    apis = health_report.get('api_connectivity', {})
    if apis:
        print(f"\nAPI Connectivity:")
        for api, status in apis.items():
            api_status = status.get('status', 'unknown')
            api_emoji = '✅' if api_status == 'healthy' else '❌'
            print(f"  {api}: {api_emoji} {api_status}")
    
    print("="*50)
    
    return overall_status == 'healthy'

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
