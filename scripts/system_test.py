#!/usr/bin/env python3
"""
System Test Script for Synergy7 Trading System

This script performs an end-to-end test of the Synergy7 Trading System,
including configuration loading, API clients, risk management, and trading logic.
"""

import os
import sys
import logging
import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import modules to test
from shared.utils.config_loader import get_config_loader, load_config
from phase_4_deployment.apis.helius_client import Helius<PERSON><PERSON>
from phase_4_deployment.rpc_execution.jito_client import JitoClient
from core.risk.position_sizer import PositionSizer
from core.risk.stop_loss import StopLossManager
from core.risk.portfolio_limits import PortfolioLimits
from core.risk.circuit_breaker import CircuitBreaker
from core.strategies.momentum_optimizer import MomentumOptimizer

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("system_test")

async def test_config_loader():
    """Test the configuration loader."""
    logger.info("Testing configuration loader...")
    
    # Load configuration
    config_loader = get_config_loader()
    config = load_config(environment="development", components=["backtest"])
    
    # Check that configuration is loaded
    assert config is not None, "Failed to load configuration"
    
    # Check that critical sections are present
    assert "mode" in config, "Missing 'mode' section in configuration"
    assert "apis" in config, "Missing 'apis' section in configuration"
    assert "risk_management" in config, "Missing 'risk_management' section in configuration"
    
    # Check boolean conversion
    dry_run = config_loader.get_bool("mode.dry_run")
    assert isinstance(dry_run, bool), "Failed to convert dry_run to boolean"
    
    logger.info("Configuration loader test passed")
    return config

async def test_helius_client(config):
    """Test the Helius client."""
    logger.info("Testing Helius client...")
    
    # Initialize client
    client = HeliusClient(config=config.get("apis", {}).get("helius", {}))
    
    # Test getting a recent blockhash
    blockhash = await client.get_recent_blockhash()
    assert blockhash is not None, "Failed to get recent blockhash"
    
    # Test getting account info
    account_info = await client.get_account_info("11111111111111111111111111111111")
    
    # Test circuit breaker status
    circuit_breaker_status = client.get_circuit_breaker_status()
    assert "state" in circuit_breaker_status, "Missing 'state' in circuit breaker status"
    
    logger.info("Helius client test passed")
    return client

async def test_jito_client(config):
    """Test the Jito client."""
    logger.info("Testing Jito client...")
    
    # Initialize client
    client = JitoClient()
    
    # Test getting metrics
    metrics = client.get_metrics()
    assert "circuit_breaker" in metrics, "Missing 'circuit_breaker' in metrics"
    
    logger.info("Jito client test passed")
    return client

async def test_position_sizer(config):
    """Test the position sizer."""
    logger.info("Testing position sizer...")
    
    # Initialize position sizer
    position_sizer = PositionSizer(config.get("risk_management", {}))
    
    # Create test price data
    dates = pd.date_range(start="2023-01-01", periods=100, freq="D")
    prices = np.linspace(100, 200, 100) + np.random.normal(0, 5, 100)
    
    price_data = pd.DataFrame({
        "close": prices,
        "high": prices + np.random.normal(0, 1, 100),
        "low": prices - np.random.normal(0, 1, 100),
        "volume": np.random.normal(1000, 100, 100)
    }, index=dates)
    
    # Test calculating position size
    position_size_result = position_sizer.calculate_position_size(
        price_data,
        10000,
        "SOL-USDC",
        0.8
    )
    
    assert "position_size" in position_size_result, "Missing 'position_size' in result"
    assert "position_value" in position_size_result, "Missing 'position_value' in result"
    
    # Test calculating stop loss
    stop_loss_result = position_sizer.calculate_stop_loss(
        150.0,
        0.05,
        10000,
        price_data,
        True
    )
    
    assert "stop_loss_price" in stop_loss_result, "Missing 'stop_loss_price' in result"
    assert "risk_amount" in stop_loss_result, "Missing 'risk_amount' in result"
    
    # Test calculating take profit
    take_profit_price = position_sizer.calculate_take_profit(
        150.0,
        145.0,
        True,
        2.0
    )
    
    assert take_profit_price > 150.0, "Take profit price should be higher than entry price for long positions"
    
    logger.info("Position sizer test passed")
    return position_sizer

async def test_stop_loss_manager(config):
    """Test the stop loss manager."""
    logger.info("Testing stop loss manager...")
    
    # Initialize stop loss manager
    stop_loss_manager = StopLossManager(config.get("risk_management", {}))
    
    # Test setting initial stop
    entry_time = pd.Timestamp("2023-01-01 12:00:00")
    stop_info = stop_loss_manager.set_initial_stop(
        "trade1",
        100.0,
        entry_time,
        95.0,
        True
    )
    
    assert stop_info["trade_id"] == "trade1", "Incorrect trade ID in stop info"
    assert stop_info["current_stop_price"] == 95.0, "Incorrect stop price in stop info"
    
    # Test updating stop
    current_time = entry_time + timedelta(hours=1)
    updated_stop_info = stop_loss_manager.update_stop(
        "trade1",
        102.0,
        current_time
    )
    
    assert updated_stop_info["trailing_activated"] == True, "Trailing stop should be activated"
    
    # Test checking if stop is triggered
    triggered = stop_loss_manager.check_stop_triggered(
        "trade1",
        94.0
    )
    
    assert triggered == True, "Stop should be triggered"
    
    logger.info("Stop loss manager test passed")
    return stop_loss_manager

async def test_portfolio_limits(config):
    """Test the portfolio limits."""
    logger.info("Testing portfolio limits...")
    
    # Initialize portfolio limits
    portfolio_limits = PortfolioLimits(config.get("risk_management", {}))
    
    # Test setting initial balance
    portfolio_limits.set_initial_balance(10000)
    
    # Test adding positions
    portfolio_limits.add_position(
        "position1",
        "SOL-USDC",
        0.5,
        100.0,
        True
    )
    
    # Test getting total exposure
    total_exposure = portfolio_limits.get_total_exposure()
    assert total_exposure == 0.005, f"Incorrect total exposure: {total_exposure}"
    
    # Test checking limits
    limits_check = portfolio_limits.check_limits()
    assert "limits_exceeded" in limits_check, "Missing 'limits_exceeded' in limits check"
    
    # Test checking if position can be opened
    can_open, reason = portfolio_limits.can_open_position(
        "SOL-USDC",
        0.1,
        100.0
    )
    
    assert can_open == True, f"Should be able to open position: {reason}"
    
    logger.info("Portfolio limits test passed")
    return portfolio_limits

async def test_circuit_breaker(config):
    """Test the circuit breaker."""
    logger.info("Testing circuit breaker...")
    
    # Initialize circuit breaker
    circuit_breaker = CircuitBreaker(config.get("risk_management", {}))
    
    # Test updating balance
    circuit_breaker.update_balance(10000)
    
    # Test recording trade result
    circuit_breaker.record_trade_result("trade1", 100, 10000)
    
    # Test recording API failure
    circuit_breaker.record_api_failure("helius")
    
    # Test checking if trading is allowed
    can_trade, reason = circuit_breaker.can_trade()
    assert can_trade == True, f"Should be able to trade: {reason}"
    
    # Test getting status
    status = circuit_breaker.get_status()
    assert "state" in status, "Missing 'state' in status"
    
    logger.info("Circuit breaker test passed")
    return circuit_breaker

async def test_momentum_optimizer(config):
    """Test the momentum optimizer."""
    logger.info("Testing momentum optimizer...")
    
    # Initialize momentum optimizer
    optimizer_config = {
        "window_size_range": [5, 10],
        "threshold_range": [0.01, 0.02],
        "smoothing_factor_range": [0.1, 0.2],
        "max_value_range": [0.1, 0.2],
        "train_test_split": 0.7,
        "walk_forward_window": 30,
        "walk_forward_step": 7,
        "min_trades": 5,
        "output_dir": "output/momentum_optimizer"
    }
    momentum_optimizer = MomentumOptimizer(optimizer_config)
    
    # Create test price data
    dates = pd.date_range(start="2023-01-01", periods=100, freq="D")
    prices = np.linspace(100, 200, 100) + np.random.normal(0, 5, 100)
    
    # Add some momentum patterns
    for i in range(30, 40):
        prices[i] += i - 30
    for i in range(60, 70):
        prices[i] -= i - 60
    
    price_data = pd.DataFrame({
        "close": prices,
        "high": prices + np.random.normal(0, 1, 100),
        "low": prices - np.random.normal(0, 1, 100),
        "volume": np.random.normal(1000, 100, 100)
    }, index=dates)
    
    # Test calculating momentum
    signals = momentum_optimizer.calculate_momentum(
        price_data,
        window_size=10,
        threshold=0.01,
        smoothing_factor=0.2,
        max_value=0.1
    )
    
    assert "signal" in signals.columns, "Missing 'signal' column in signals"
    
    # Test calculating performance
    performance = momentum_optimizer.calculate_performance(signals)
    assert "sharpe_ratio" in performance, "Missing 'sharpe_ratio' in performance"
    
    logger.info("Momentum optimizer test passed")
    return momentum_optimizer

async def run_system_test():
    """Run the system test."""
    logger.info("Starting system test...")
    
    try:
        # Test configuration loader
        config = await test_config_loader()
        
        # Test API clients
        helius_client = await test_helius_client(config)
        jito_client = await test_jito_client(config)
        
        # Test risk management
        position_sizer = await test_position_sizer(config)
        stop_loss_manager = await test_stop_loss_manager(config)
        portfolio_limits = await test_portfolio_limits(config)
        circuit_breaker = await test_circuit_breaker(config)
        
        # Test strategy optimizer
        momentum_optimizer = await test_momentum_optimizer(config)
        
        # Clean up
        await helius_client.close()
        await jito_client.close()
        
        logger.info("System test completed successfully")
        return True
    except Exception as e:
        logger.error(f"System test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(run_system_test())
    sys.exit(0 if success else 1)
