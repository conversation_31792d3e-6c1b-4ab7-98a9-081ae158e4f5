#!/usr/bin/env python3
"""
Run Terminal Metrics Display

This script runs the terminal metrics display for the Synergy7 Trading System.
"""

import os
import sys
import argparse
import asyncio

# Add parent directory to path for imports
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

# Import terminal metrics display
from phase_4_deployment.monitoring.terminal_metrics import TerminalMetricsDisplay

async def main():
    """Main function to run the terminal metrics display."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Run terminal metrics display")
    parser.add_argument("--update-interval", type=float, default=1.0, help="Update interval in seconds")
    args = parser.parse_args()
    
    # Initialize terminal metrics display
    terminal_metrics = TerminalMetricsDisplay()
    
    # Try to import rich for enhanced terminal display
    try:
        from rich.live import Live
        
        # Run with rich live display
        with Live(terminal_metrics.render(), refresh_per_second=4) as live:
            while True:
                try:
                    # Update data
                    await terminal_metrics.update_data()
                    
                    # Update display
                    live.update(terminal_metrics.render())
                    
                    # Wait for next update
                    await asyncio.sleep(args.update_interval)
                except KeyboardInterrupt:
                    print("\nExiting terminal metrics display...")
                    break
                except Exception as e:
                    print(f"Error: {str(e)}")
                    await asyncio.sleep(args.update_interval)
    except ImportError:
        # Run with basic display
        while True:
            try:
                # Update data
                await terminal_metrics.update_data()
                
                # Render display
                terminal_metrics.render()
                
                # Wait for next update
                await asyncio.sleep(args.update_interval)
            except KeyboardInterrupt:
                print("\nExiting terminal metrics display...")
                break
            except Exception as e:
                print(f"Error: {str(e)}")
                await asyncio.sleep(args.update_interval)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nExiting terminal metrics display...")
        sys.exit(0)
