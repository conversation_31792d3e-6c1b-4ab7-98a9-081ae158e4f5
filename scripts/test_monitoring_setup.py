#!/usr/bin/env python3
"""
Test script for Synergy7 Enhanced Trading System monitoring setup.

This script validates that all monitoring components are working correctly
including logging, performance monitoring, risk alerts, and system metrics.
"""

import os
import sys
import asyncio
import logging
import json
import yaml
from pathlib import Path
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MonitoringSetupTester:
    """Test monitoring setup and configuration."""

    def __init__(self):
        """Initialize the monitoring tester."""
        self.project_root = Path.cwd()
        self.test_results = {}

    def load_configuration(self) -> dict:
        """Load system configuration."""
        try:
            # Load main config
            config_file = self.project_root / 'config.yaml'
            if config_file.exists():
                with open(config_file, 'r') as f:
                    config = yaml.safe_load(f)
                logger.info("✅ Configuration loaded successfully")
                return config
            else:
                logger.error("❌ Configuration file not found")
                return {}
        except Exception as e:
            logger.error(f"❌ Error loading configuration: {str(e)}")
            return {}

    def test_logging_configuration(self) -> bool:
        """Test logging configuration."""
        try:
            logger.info("🔍 Testing logging configuration...")

            # Check logging config file
            logging_config_file = self.project_root / 'logging_config.json'
            if not logging_config_file.exists():
                logger.error("❌ Logging configuration file not found")
                return False

            # Load logging config
            with open(logging_config_file, 'r') as f:
                logging_config = json.load(f)

            # Check required log directories
            log_dirs = [
                'logs',
                'logs/errors',
                'logs/trading',
                'logs/risk',
                'logs/performance',
                'logs/system',
                'logs/metrics'
            ]

            for log_dir in log_dirs:
                dir_path = self.project_root / log_dir
                if not dir_path.exists():
                    logger.warning(f"⚠️ Creating missing log directory: {log_dir}")
                    dir_path.mkdir(parents=True, exist_ok=True)

            # Test different log levels
            test_logger = logging.getLogger('test')
            test_logger.info("Test INFO message")
            test_logger.warning("Test WARNING message")
            test_logger.error("Test ERROR message")

            logger.info("✅ Logging configuration test passed")
            return True

        except Exception as e:
            logger.error(f"❌ Logging configuration test failed: {str(e)}")
            return False

    def test_performance_monitor(self, config: dict) -> bool:
        """Test performance monitoring component."""
        try:
            logger.info("🔍 Testing performance monitor...")

            # Import performance monitor
            from core.monitoring.performance_monitor import PerformanceMonitor

            # Initialize performance monitor
            perf_monitor = PerformanceMonitor(config)

            # Test system metrics collection
            system_metrics = perf_monitor.collect_system_metrics()
            if not system_metrics:
                logger.error("❌ Failed to collect system metrics")
                return False

            # Test trading metrics collection
            mock_trading_data = {
                'total_trades': 10,
                'successful_trades': 8,
                'failed_trades': 2,
                'total_pnl': 0.05,
                'daily_pnl': 0.02,
                'win_rate': 0.8,
                'avg_trade_duration': 300,
                'active_positions': 3,
                'portfolio_value': 1000.0
            }

            trading_metrics = perf_monitor.collect_trading_metrics(mock_trading_data)
            if not trading_metrics:
                logger.error("❌ Failed to collect trading metrics")
                return False

            # Test risk metrics collection
            mock_risk_data = {
                'portfolio_var': 0.015,
                'portfolio_cvar': 0.018,
                'max_position_size': 0.12,
                'correlation_exposure': 0.25,
                'daily_drawdown': -0.01,
                'max_drawdown': -0.03,
                'risk_limit_breaches': 0,
                'volatility': 0.02
            }

            risk_metrics = perf_monitor.collect_risk_metrics(mock_risk_data)
            if not risk_metrics:
                logger.error("❌ Failed to collect risk metrics")
                return False

            # Test performance summary
            summary = perf_monitor.get_performance_summary()
            if not summary:
                logger.error("❌ Failed to get performance summary")
                return False

            logger.info("✅ Performance monitor test passed")
            return True

        except Exception as e:
            logger.error(f"❌ Performance monitor test failed: {str(e)}")
            return False

    async def test_risk_alerts(self, config: dict) -> bool:
        """Test risk alerting system."""
        try:
            logger.info("🔍 Testing risk alerts...")

            # Import risk alert manager
            from core.monitoring.risk_alerts import RiskAlertManager, AlertType, AlertSeverity

            # Initialize risk alert manager
            risk_manager = RiskAlertManager(config)

            # Test VaR limit checking
            mock_var_data = {
                'portfolio_var': 0.025,  # Above 2% limit
                'portfolio_cvar': 0.030,
                'positions': {
                    'SOL': {'value': 800, 'strategy': 'momentum'},
                    'USDC': {'value': 200, 'strategy': 'mean_reversion'}
                },
                'correlation_matrix': {
                    'SOL': {'SOL': 1.0, 'USDC': 0.1},
                    'USDC': {'SOL': 0.1, 'USDC': 1.0}
                },
                'current_drawdown': -0.02,
                'max_drawdown': -0.04,
                'current_volatility': 0.03,
                'historical_volatility': [0.02, 0.021, 0.019, 0.022, 0.020]
            }

            # Test alert generation (without sending)
            alerts = []

            # Test VaR alerts
            var_alerts = await risk_manager.check_var_limits(
                mock_var_data['portfolio_var'],
                mock_var_data['portfolio_cvar']
            )
            alerts.extend(var_alerts)

            # Test position limit alerts
            position_alerts = await risk_manager.check_position_limits(mock_var_data['positions'])
            alerts.extend(position_alerts)

            # Test drawdown alerts
            drawdown_alerts = await risk_manager.check_drawdown_limits(
                mock_var_data['current_drawdown'],
                mock_var_data['max_drawdown']
            )
            alerts.extend(drawdown_alerts)

            if not alerts:
                logger.warning("⚠️ No alerts generated (this may be expected)")
            else:
                logger.info(f"✅ Generated {len(alerts)} test alerts")
                for alert in alerts:
                    logger.info(f"   - {alert.alert_type.value}: {alert.message}")

            # Test alert summary
            summary = risk_manager.get_alert_summary()
            if summary is None:
                logger.error("❌ Failed to get alert summary")
                return False

            logger.info("✅ Risk alerts test passed")
            return True

        except Exception as e:
            logger.error(f"❌ Risk alerts test failed: {str(e)}")
            return False

    async def test_system_metrics(self, config: dict) -> bool:
        """Test system metrics monitoring."""
        try:
            logger.info("🔍 Testing system metrics...")

            # Import system metrics monitor
            from core.monitoring.system_metrics import SystemMetricsMonitor

            # Initialize system metrics monitor
            system_monitor = SystemMetricsMonitor(config)

            # Test system metrics collection
            system_metrics = system_monitor.collect_system_metrics()
            if not system_metrics:
                logger.error("❌ Failed to collect system metrics")
                return False

            # Verify key metrics are present
            required_metrics = ['memory', 'cpu', 'disk', 'network', 'process', 'system']
            for metric in required_metrics:
                if metric not in system_metrics:
                    logger.error(f"❌ Missing required metric: {metric}")
                    return False

            # Test API health check
            api_metrics = await system_monitor.check_api_health()
            if not api_metrics:
                logger.warning("⚠️ API health check returned no data")
            else:
                logger.info("✅ API health check completed")
                for api_name, api_data in api_metrics.get('apis', {}).items():
                    status = api_data.get('status', 'unknown')
                    response_time = api_data.get('response_time_ms', 0)
                    logger.info(f"   - {api_name}: {status} ({response_time:.0f}ms)")

            # Test log error analysis
            error_metrics = system_monitor.analyze_log_errors()
            if error_metrics:
                error_count = error_metrics.get('error_counts', {}).get('total', 0)
                logger.info(f"✅ Log analysis completed - {error_count} errors found")

            # Test metrics summary
            summary = system_monitor.get_metrics_summary()
            if not summary:
                logger.error("❌ Failed to get metrics summary")
                return False

            logger.info("✅ System metrics test passed")
            return True

        except Exception as e:
            logger.error(f"❌ System metrics test failed: {str(e)}")
            return False

    async def test_integrated_monitoring(self, config: dict) -> bool:
        """Test integrated monitoring service."""
        try:
            logger.info("🔍 Testing integrated monitoring service...")

            # Import integrated monitoring service
            from core.monitoring.performance_monitor import IntegratedMonitoringService

            # Initialize integrated monitoring
            monitoring_service = IntegratedMonitoringService(config)

            # Test comprehensive status
            status = monitoring_service.get_comprehensive_status()
            if not status:
                logger.error("❌ Failed to get comprehensive status")
                return False

            # Test trading cycle monitoring
            mock_trading_data = {
                'total_trades': 5,
                'successful_trades': 4,
                'daily_pnl': 0.01,
                'portfolio_value': 1000.0
            }

            mock_risk_data = {
                'portfolio_var': 0.015,
                'portfolio_cvar': 0.018,
                'positions': {'SOL': {'value': 800}},
                'correlation_matrix': {'SOL': {'SOL': 1.0}}
            }

            mock_strategy_data = {
                'momentum_strategy': {
                    'total_trades': 3,
                    'net_pnl': 0.008,
                    'win_rate': 0.67,
                    'current_weight': 0.4
                }
            }

            cycle_results = await monitoring_service.monitor_trading_cycle(
                mock_trading_data, mock_risk_data, mock_strategy_data
            )

            if not cycle_results:
                logger.error("❌ Failed to monitor trading cycle")
                return False

            logger.info("✅ Integrated monitoring test passed")
            return True

        except Exception as e:
            logger.error(f"❌ Integrated monitoring test failed: {str(e)}")
            return False

    async def test_telegram_connectivity(self) -> bool:
        """Test Telegram bot connectivity."""
        try:
            logger.info("🔍 Testing Telegram connectivity...")

            bot_token = os.environ.get('TELEGRAM_BOT_TOKEN')
            chat_id = os.environ.get('TELEGRAM_CHAT_ID')

            if not bot_token or not chat_id:
                logger.warning("⚠️ Telegram credentials not configured")
                return True  # Not an error if not configured

            # Test basic connectivity (without sending message)
            import httpx

            async def test_telegram():
                try:
                    url = f"https://api.telegram.org/bot{bot_token}/getMe"
                    async with httpx.AsyncClient() as client:
                        response = await client.get(url, timeout=10)
                        if response.status_code == 200:
                            logger.info("✅ Telegram bot connectivity test passed")
                            return True
                        else:
                            logger.error(f"❌ Telegram bot test failed: {response.status_code}")
                            return False
                except Exception as e:
                    logger.error(f"❌ Telegram connectivity test failed: {str(e)}")
                    return False

            return await test_telegram()

        except Exception as e:
            logger.error(f"❌ Telegram connectivity test failed: {str(e)}")
            return False

    async def run_all_tests(self) -> dict:
        """Run all monitoring tests."""
        logger.info("🚀 Starting monitoring setup tests...")
        logger.info("=" * 60)

        # Load configuration
        config = self.load_configuration()

        # Run tests
        test_results = {
            'logging_configuration': self.test_logging_configuration(),
            'performance_monitor': self.test_performance_monitor(config),
            'risk_alerts': await self.test_risk_alerts(config),
            'system_metrics': await self.test_system_metrics(config),
            'integrated_monitoring': await self.test_integrated_monitoring(config),
            'telegram_connectivity': await self.test_telegram_connectivity()
        }

        # Calculate summary
        passed_tests = sum(1 for result in test_results.values() if result)
        total_tests = len(test_results)
        success_rate = (passed_tests / total_tests) * 100

        logger.info("=" * 60)
        logger.info("📊 MONITORING SETUP TEST RESULTS")
        logger.info("=" * 60)

        for test_name, result in test_results.items():
            status = "✅ PASSED" if result else "❌ FAILED"
            logger.info(f"{test_name.replace('_', ' ').title()}: {status}")

        logger.info("=" * 60)
        logger.info(f"Overall Success Rate: {passed_tests}/{total_tests} ({success_rate:.1f}%)")

        if success_rate == 100:
            logger.info("🎉 ALL MONITORING TESTS PASSED!")
        elif success_rate >= 80:
            logger.info("✅ Most monitoring tests passed - system ready")
        else:
            logger.warning("⚠️ Some monitoring tests failed - review configuration")

        return test_results

async def main():
    """Main function."""
    tester = MonitoringSetupTester()
    results = await tester.run_all_tests()

    # Return appropriate exit code
    success_rate = sum(1 for result in results.values() if result) / len(results)
    return 0 if success_rate >= 0.8 else 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
