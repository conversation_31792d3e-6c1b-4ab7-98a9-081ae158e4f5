#!/usr/bin/env python3
"""
Test Risk Components Script for Synergy7 Trading System

This script tests the risk management components added as part of the
strategy_finder.md directives.
"""

import os
import sys
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import modules to test
from core.risk.position_sizer import PositionSizer
from core.risk.stop_loss import StopLossManager
from core.risk.portfolio_limits import PortfolioLimits
from core.risk.circuit_breaker import CircuitBreaker

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("test_risk_components")

def create_test_price_data():
    """Create test price data."""
    # Create test price data
    dates = pd.date_range(start="2023-01-01", periods=100, freq="D")
    prices = np.linspace(100, 200, 100) + np.random.normal(0, 5, 100)
    
    # Add some momentum patterns
    for i in range(30, 40):
        prices[i] += i - 30
    for i in range(60, 70):
        prices[i] -= i - 60
    
    price_data = pd.DataFrame({
        "close": prices,
        "high": prices + np.random.normal(0, 1, 100),
        "low": prices - np.random.normal(0, 1, 100),
        "volume": np.random.normal(1000, 100, 100)
    }, index=dates)
    
    return price_data

def test_position_sizer():
    """Test the position sizer."""
    logger.info("Testing position sizer...")
    
    # Create test configuration
    config = {
        "max_position_size": 0.1,
        "max_portfolio_risk": 0.02,
        "volatility_lookback": 20,
        "volatility_scaling": True,
        "min_position_size": 0.01,
        "position_size_increment": 0.01,
        "risk_per_trade": 0.01,
        "atr_multiplier": 2.0
    }
    
    # Initialize position sizer
    position_sizer = PositionSizer(config)
    
    # Create test price data
    price_data = create_test_price_data()
    
    # Test calculating position size
    position_size_result = position_sizer.calculate_position_size(
        price_data,
        10000,
        "SOL-USDC",
        0.8
    )
    
    logger.info(f"Position size result: {position_size_result}")
    
    # Test calculating stop loss
    stop_loss_result = position_sizer.calculate_stop_loss(
        150.0,
        0.05,
        10000,
        price_data,
        True
    )
    
    logger.info(f"Stop loss result: {stop_loss_result}")
    
    # Test calculating take profit
    take_profit_price = position_sizer.calculate_take_profit(
        150.0,
        145.0,
        True,
        2.0
    )
    
    logger.info(f"Take profit price: {take_profit_price}")
    
    return True

def test_stop_loss_manager():
    """Test the stop loss manager."""
    logger.info("Testing stop loss manager...")
    
    # Create test configuration
    config = {
        "trailing_enabled": True,
        "trailing_activation_pct": 0.01,
        "trailing_distance_pct": 0.02,
        "volatility_multiplier": 2.0,
        "time_based_widening": False,
        "widening_factor": 0.001
    }
    
    # Initialize stop loss manager
    stop_loss_manager = StopLossManager(config)
    
    # Test setting initial stop
    entry_time = pd.Timestamp("2023-01-01 12:00:00")
    stop_info = stop_loss_manager.set_initial_stop(
        "trade1",
        100.0,
        entry_time,
        95.0,
        True
    )
    
    logger.info(f"Stop loss info: {stop_info}")
    
    # Test updating stop
    current_time = entry_time + timedelta(hours=1)
    updated_stop_info = stop_loss_manager.update_stop(
        "trade1",
        102.0,
        current_time
    )
    
    logger.info(f"Updated stop loss info: {updated_stop_info}")
    
    # Test checking if stop is triggered
    triggered = stop_loss_manager.check_stop_triggered(
        "trade1",
        94.0
    )
    
    logger.info(f"Stop loss triggered: {triggered}")
    
    return True

def test_portfolio_limits():
    """Test the portfolio limits."""
    logger.info("Testing portfolio limits...")
    
    # Create test configuration
    config = {
        "max_portfolio_exposure": 0.8,
        "max_single_market_exposure": 0.3,
        "max_correlated_exposure": 0.5,
        "max_daily_drawdown": 0.05,
        "max_weekly_drawdown": 0.1,
        "max_monthly_drawdown": 0.15
    }
    
    # Initialize portfolio limits
    portfolio_limits = PortfolioLimits(config)
    
    # Test setting initial balance
    portfolio_limits.set_initial_balance(10000)
    
    # Test adding position
    portfolio_limits.add_position(
        "position1",
        "SOL-USDC",
        0.5,
        100.0,
        True
    )
    
    logger.info(f"Added position to portfolio limits")
    
    # Test getting total exposure
    total_exposure = portfolio_limits.get_total_exposure()
    logger.info(f"Total exposure: {total_exposure}")
    
    # Test checking limits
    limits_check = portfolio_limits.check_limits()
    logger.info(f"Limits check: {limits_check}")
    
    # Test checking if position can be opened
    can_open, reason = portfolio_limits.can_open_position(
        "SOL-USDC",
        0.1,
        100.0
    )
    
    logger.info(f"Can open position: {can_open}, reason: {reason}")
    
    return True

def test_circuit_breaker():
    """Test the circuit breaker."""
    logger.info("Testing circuit breaker...")
    
    # Create test configuration
    config = {
        "enabled": True,
        "max_consecutive_losses": 3,
        "max_daily_loss_pct": 0.05,
        "max_drawdown_pct": 0.1,
        "cooldown_minutes": 60,
        "volatility_threshold": 0.05,
        "api_failure_threshold": 5
    }
    
    # Initialize circuit breaker
    circuit_breaker = CircuitBreaker(config)
    
    # Test updating balance
    circuit_breaker.update_balance(10000)
    logger.info(f"Updated circuit breaker balance")
    
    # Test recording trade result
    circuit_breaker.record_trade_result("trade1", 100, 10000)
    logger.info(f"Recorded trade result in circuit breaker")
    
    # Test recording API failure
    circuit_breaker.record_api_failure("helius")
    logger.info(f"Recorded API failure in circuit breaker")
    
    # Test checking if trading is allowed
    can_trade, reason = circuit_breaker.can_trade()
    logger.info(f"Can trade: {can_trade}, reason: {reason}")
    
    # Test getting status
    status = circuit_breaker.get_status()
    logger.info(f"Circuit breaker status: {status}")
    
    return True

def test_end_to_end():
    """Test end-to-end workflow."""
    logger.info("Testing end-to-end workflow...")
    
    # Create test configurations
    position_sizer_config = {
        "max_position_size": 0.1,
        "max_portfolio_risk": 0.02,
        "volatility_lookback": 20,
        "volatility_scaling": True,
        "min_position_size": 0.01,
        "position_size_increment": 0.01,
        "risk_per_trade": 0.01,
        "atr_multiplier": 2.0
    }
    
    stop_loss_config = {
        "trailing_enabled": True,
        "trailing_activation_pct": 0.01,
        "trailing_distance_pct": 0.02,
        "volatility_multiplier": 2.0,
        "time_based_widening": False,
        "widening_factor": 0.001
    }
    
    portfolio_limits_config = {
        "max_portfolio_exposure": 0.8,
        "max_single_market_exposure": 0.3,
        "max_correlated_exposure": 0.5,
        "max_daily_drawdown": 0.05,
        "max_weekly_drawdown": 0.1,
        "max_monthly_drawdown": 0.15
    }
    
    circuit_breaker_config = {
        "enabled": True,
        "max_consecutive_losses": 3,
        "max_daily_loss_pct": 0.05,
        "max_drawdown_pct": 0.1,
        "cooldown_minutes": 60,
        "volatility_threshold": 0.05,
        "api_failure_threshold": 5
    }
    
    # Initialize components
    position_sizer = PositionSizer(position_sizer_config)
    stop_loss_manager = StopLossManager(stop_loss_config)
    portfolio_limits = PortfolioLimits(portfolio_limits_config)
    circuit_breaker = CircuitBreaker(circuit_breaker_config)
    
    # Create test price data
    price_data = create_test_price_data()
    
    # Set initial balance
    portfolio_limits.set_initial_balance(10000)
    circuit_breaker.update_balance(10000)
    
    # Simulate a trading cycle
    try:
        # 1. Check if trading is allowed
        can_trade, reason = circuit_breaker.can_trade()
        if not can_trade:
            logger.info(f"Trading not allowed: {reason}")
            return True
        
        # 2. Calculate position size
        position_size_result = position_sizer.calculate_position_size(
            price_data,
            10000,
            "SOL-USDC",
            0.8
        )
        position_size = position_size_result["position_size"]
        
        # 3. Check if position can be opened
        can_open, reason = portfolio_limits.can_open_position(
            "SOL-USDC",
            position_size,
            150.0
        )
        if not can_open:
            logger.info(f"Cannot open position: {reason}")
            return True
        
        # 4. Calculate stop loss and take profit
        stop_loss_result = position_sizer.calculate_stop_loss(
            150.0,
            position_size,
            10000,
            price_data,
            True
        )
        stop_loss_price = stop_loss_result["stop_loss_price"]
        
        take_profit_price = position_sizer.calculate_take_profit(
            150.0,
            stop_loss_price,
            True,
            2.0
        )
        
        # 5. Set initial stop loss
        entry_time = pd.Timestamp.now()
        stop_info = stop_loss_manager.set_initial_stop(
            "trade1",
            150.0,
            entry_time,
            stop_loss_price,
            True
        )
        
        # 6. Add position to portfolio
        portfolio_limits.add_position(
            "trade1",
            "SOL-USDC",
            position_size,
            150.0,
            True
        )
        
        # 7. Simulate price movement
        current_time = entry_time + timedelta(hours=1)
        current_price = 155.0
        
        # 8. Update stop loss
        updated_stop_info = stop_loss_manager.update_stop(
            "trade1",
            current_price,
            current_time
        )
        
        # 9. Check if stop loss is triggered
        triggered = stop_loss_manager.check_stop_triggered(
            "trade1",
            current_price
        )
        
        # 10. Update portfolio
        portfolio_limits.update_position(
            "trade1",
            current_price
        )
        
        # 11. Check portfolio limits
        limits_check = portfolio_limits.check_limits()
        
        # 12. Simulate trade completion
        profit_loss = (current_price - 150.0) * position_size
        circuit_breaker.record_trade_result("trade1", profit_loss, 10000)
        
        # 13. Remove position and stop loss
        portfolio_limits.remove_position("trade1")
        stop_loss_manager.remove_stop("trade1")
        
        # 14. Update balance
        new_balance = 10000 + profit_loss
        portfolio_limits.update_balance(new_balance)
        circuit_breaker.update_balance(new_balance)
        
        logger.info(f"End-to-end workflow completed successfully")
        logger.info(f"Profit/Loss: {profit_loss}")
        logger.info(f"New balance: {new_balance}")
    except Exception as e:
        logger.error(f"Error in end-to-end workflow: {str(e)}")
        return False
    
    return True

def main():
    """Main function."""
    logger.info("Starting risk component tests...")
    
    # Run tests
    position_sizer_result = test_position_sizer()
    stop_loss_result = test_stop_loss_manager()
    portfolio_limits_result = test_portfolio_limits()
    circuit_breaker_result = test_circuit_breaker()
    end_to_end_result = test_end_to_end()
    
    # Print results
    logger.info("Risk component test results:")
    logger.info(f"Position sizer test result: {'PASS' if position_sizer_result else 'FAIL'}")
    logger.info(f"Stop loss manager test result: {'PASS' if stop_loss_result else 'FAIL'}")
    logger.info(f"Portfolio limits test result: {'PASS' if portfolio_limits_result else 'FAIL'}")
    logger.info(f"Circuit breaker test result: {'PASS' if circuit_breaker_result else 'FAIL'}")
    logger.info(f"End-to-end test result: {'PASS' if end_to_end_result else 'FAIL'}")
    
    # Return success/failure
    overall_result = (
        position_sizer_result and
        stop_loss_result and
        portfolio_limits_result and
        circuit_breaker_result and
        end_to_end_result
    )
    
    logger.info(f"Overall result: {'PASS' if overall_result else 'FAIL'}")
    
    return overall_result

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
