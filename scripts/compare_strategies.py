#!/usr/bin/env python3
"""
Strategy Comparison Script for Synergy7 Trading System

This script compares the performance of the optimized momentum strategy
with the mean reversion strategy on historical data.
"""

import os
import sys
import logging
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import json
import argparse
from datetime import datetime, timedelta
from pathlib import Path

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import modules
from core.strategies.momentum_optimizer import MomentumOptimizer
from shared.utils.config_loader import get_config_loader, load_config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("compare_strategies")

def load_price_data(file_path=None, symbol="SOL-USDC", days=180):
    """
    Load price data from file or generate synthetic data for testing.

    Args:
        file_path: Path to price data file (CSV)
        symbol: Symbol to use for synthetic data
        days: Number of days of synthetic data to generate

    Returns:
        DataFrame containing price data
    """
    if file_path and os.path.exists(file_path):
        logger.info(f"Loading price data from {file_path}")
        try:
            df = pd.read_csv(file_path)

            # Convert timestamp to datetime if needed
            if 'timestamp' in df.columns:
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                df.set_index('timestamp', inplace=True)

            return df
        except Exception as e:
            logger.error(f"Error loading price data: {str(e)}")

    # Generate synthetic data for testing
    logger.info(f"Generating synthetic price data for {symbol} ({days} days)")

    # Create date range
    dates = pd.date_range(end=datetime.now(), periods=days, freq="D")

    # Generate price series with some realistic patterns
    base_price = 100.0
    if symbol == "SOL-USDC":
        base_price = 100.0
    elif symbol == "JTO-USDC":
        base_price = 2.0
    elif symbol == "BONK-USDC":
        base_price = 0.00002

    # Generate price series with trend, cycles, and noise
    trend = np.linspace(0, 0.5, days)  # Upward trend
    cycles = 0.1 * np.sin(np.linspace(0, 15, days)) + 0.05 * np.sin(np.linspace(0, 30, days))  # Cycles
    noise = np.random.normal(0, 0.02, days)  # Random noise

    # Combine components
    returns = trend + cycles + noise

    # Convert returns to prices
    prices = base_price * (1 + returns).cumprod()

    # Add some momentum patterns
    momentum_periods = [
        (int(days * 0.2), int(days * 0.3)),  # Strong uptrend
        (int(days * 0.5), int(days * 0.6)),  # Strong downtrend
        (int(days * 0.7), int(days * 0.8)),  # Choppy market
    ]

    for start, end in momentum_periods:
        if start < end and end < days:
            if start == momentum_periods[0][0]:  # Uptrend
                prices[start:end] = prices[start] * (1 + np.linspace(0, 0.3, end - start)).cumprod()
            elif start == momentum_periods[1][0]:  # Downtrend
                prices[start:end] = prices[start] * (1 + np.linspace(0, -0.2, end - start)).cumprod()
            else:  # Choppy
                prices[start:end] = prices[start] * (1 + 0.05 * np.sin(np.linspace(0, 10, end - start)))

    # Create DataFrame
    df = pd.DataFrame({
        "close": prices,
        "high": prices * (1 + np.random.uniform(0, 0.02, days)),
        "low": prices * (1 - np.random.uniform(0, 0.02, days)),
        "volume": np.random.normal(1000000, 200000, days)
    }, index=dates)

    return df

def calculate_momentum_signals(price_data, params):
    """
    Calculate momentum strategy signals.

    Args:
        price_data: DataFrame containing price data
        params: Dictionary containing strategy parameters

    Returns:
        DataFrame containing signals
    """
    # Initialize optimizer (used for signal generation)
    optimizer = MomentumOptimizer()

    # Generate signals
    logger.info(f"Generating momentum signals with parameters: {params}")
    signals = optimizer.calculate_momentum(
        price_data,
        window_size=params["window_size"],
        threshold=params["threshold"],
        smoothing_factor=params["smoothing_factor"],
        max_value=params["max_value"]
    )

    # Calculate returns
    signals["returns"] = signals["close"].pct_change()

    # Calculate strategy returns
    signals["strategy_returns"] = signals["returns"] * signals["position"]

    # Calculate cumulative returns
    signals["cumulative_returns"] = (1 + signals["strategy_returns"]).cumprod()

    # Calculate drawdown
    signals["drawdown"] = 1 - signals["cumulative_returns"] / signals["cumulative_returns"].cummax()

    return signals

def calculate_mean_reversion_signals(price_data, params):
    """
    Calculate mean reversion strategy signals.

    Args:
        price_data: DataFrame containing price data
        params: Dictionary containing strategy parameters

    Returns:
        DataFrame containing signals
    """
    # Create a copy of the price data
    signals = price_data.copy()

    # Calculate returns
    signals["returns"] = signals["close"].pct_change()

    # Calculate moving average and standard deviation
    signals["ma"] = signals["close"].rolling(window=params["mean_window"]).mean()
    signals["std"] = signals["close"].rolling(window=params["window"]).std()

    # Calculate z-score
    signals["z_score"] = (signals["close"] - signals["ma"]) / signals["std"]

    # Generate signals
    signals["signal"] = 0
    signals.loc[signals["z_score"] < -params["std_dev"], "signal"] = 1  # Buy when price is below mean
    signals.loc[signals["z_score"] > params["std_dev"], "signal"] = -1  # Sell when price is above mean

    # Generate entry/exit points
    signals["position"] = signals["signal"].shift(1)
    signals["entry_exit"] = signals["position"].diff()

    # Calculate strategy returns
    signals["strategy_returns"] = signals["returns"] * signals["position"]

    # Calculate cumulative returns
    signals["cumulative_returns"] = (1 + signals["strategy_returns"]).cumprod()

    # Calculate drawdown
    signals["drawdown"] = 1 - signals["cumulative_returns"] / signals["cumulative_returns"].cummax()

    return signals

def calculate_performance(signals):
    """
    Calculate performance metrics for a set of signals.

    Args:
        signals: DataFrame containing signals

    Returns:
        Dictionary of performance metrics
    """
    # Calculate Sharpe ratio
    sharpe_ratio = np.sqrt(252) * signals["strategy_returns"].mean() / signals["strategy_returns"].std() if signals["strategy_returns"].std() > 0 else 0

    # Calculate profit factor
    winning_trades = signals[signals["strategy_returns"] > 0]["strategy_returns"].sum()
    losing_trades = abs(signals[signals["strategy_returns"] < 0]["strategy_returns"].sum())
    profit_factor = winning_trades / losing_trades if losing_trades > 0 else winning_trades

    # Calculate win rate
    win_count = len(signals[signals["strategy_returns"] > 0])
    total_trades = len(signals[signals["strategy_returns"] != 0])
    win_rate = win_count / total_trades if total_trades > 0 else 0

    # Calculate max drawdown
    max_drawdown = signals["drawdown"].max()

    # Calculate total return
    total_return = signals["cumulative_returns"].iloc[-1] - 1

    # Calculate average trade return
    avg_trade_return = signals["strategy_returns"].mean() * 100  # as percentage

    return {
        "sharpe_ratio": sharpe_ratio,
        "profit_factor": profit_factor,
        "win_rate": win_rate,
        "max_drawdown": max_drawdown,
        "total_return": total_return,
        "num_trades": total_trades // 2,  # divide by 2 to count round trips
        "avg_trade_return": avg_trade_return
    }

def compare_strategies(price_data, momentum_params, mean_reversion_params, output_dir="output/strategy_comparison"):
    """
    Compare momentum and mean reversion strategies.

    Args:
        price_data: DataFrame containing price data
        momentum_params: Dictionary containing momentum strategy parameters
        mean_reversion_params: Dictionary containing mean reversion strategy parameters
        output_dir: Output directory for results

    Returns:
        Dictionary containing comparison results
    """
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)

    # Calculate signals
    momentum_signals = calculate_momentum_signals(price_data, momentum_params)
    mean_reversion_signals = calculate_mean_reversion_signals(price_data, mean_reversion_params)

    # Calculate performance
    momentum_performance = calculate_performance(momentum_signals)
    mean_reversion_performance = calculate_performance(mean_reversion_signals)

    # Plot equity curves
    plt.figure(figsize=(12, 6))
    plt.plot(momentum_signals.index, momentum_signals["cumulative_returns"], label="Momentum")
    plt.plot(mean_reversion_signals.index, mean_reversion_signals["cumulative_returns"], label="Mean Reversion")
    plt.plot(price_data.index, (1 + price_data["close"].pct_change().fillna(0)).cumprod(), label="Buy & Hold")
    plt.title("Strategy Comparison: Equity Curves")
    plt.xlabel("Date")
    plt.ylabel("Cumulative Return")
    plt.legend()
    plt.grid(True)

    # Save plot
    output_file = os.path.join(output_dir, f"equity_curves_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png")
    plt.savefig(output_file)
    logger.info(f"Saved equity curves to {output_file}")

    # Plot drawdowns
    plt.figure(figsize=(12, 6))
    plt.plot(momentum_signals.index, momentum_signals["drawdown"], label="Momentum")
    plt.plot(mean_reversion_signals.index, mean_reversion_signals["drawdown"], label="Mean Reversion")
    plt.title("Strategy Comparison: Drawdowns")
    plt.xlabel("Date")
    plt.ylabel("Drawdown")
    plt.legend()
    plt.grid(True)

    # Save plot
    output_file = os.path.join(output_dir, f"drawdowns_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png")
    plt.savefig(output_file)
    logger.info(f"Saved drawdowns to {output_file}")

    # Compare performance metrics
    metrics = pd.DataFrame({
        "Momentum": momentum_performance,
        "Mean Reversion": mean_reversion_performance
    })

    # Save metrics
    metrics_file = os.path.join(output_dir, f"metrics_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv")
    metrics.to_csv(metrics_file)
    logger.info(f"Saved metrics to {metrics_file}")

    # Print metrics
    logger.info("Performance metrics comparison:")
    logger.info(f"\n{metrics}")

    return {
        "momentum": {
            "signals": momentum_signals,
            "performance": momentum_performance
        },
        "mean_reversion": {
            "signals": mean_reversion_signals,
            "performance": mean_reversion_performance
        },
        "metrics": metrics
    }

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Compare momentum and mean reversion strategies")
    parser.add_argument("--data-file", help="Path to price data file (CSV)")
    parser.add_argument("--symbol", default="SOL-USDC", help="Symbol to use for synthetic data")
    parser.add_argument("--days", type=int, default=180, help="Number of days of synthetic data to generate")
    parser.add_argument("--output-dir", default="output/strategy_comparison", help="Output directory for results")
    parser.add_argument("--momentum-params", default='{"window_size": 20, "threshold": 0.01, "smoothing_factor": 0.2, "max_value": 0.1}', help="JSON string of momentum parameters")
    parser.add_argument("--mean-reversion-params", default='{"window": 30, "std_dev": 2.0, "mean_window": 100}', help="JSON string of mean reversion parameters")
    args = parser.parse_args()

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Load price data
    price_data = load_price_data(args.data_file, args.symbol, args.days)

    # Parse parameters
    momentum_params = json.loads(args.momentum_params)
    mean_reversion_params = json.loads(args.mean_reversion_params)

    # Compare strategies
    results = compare_strategies(price_data, momentum_params, mean_reversion_params, output_dir=args.output_dir)

    return 0

if __name__ == "__main__":
    sys.exit(main())
