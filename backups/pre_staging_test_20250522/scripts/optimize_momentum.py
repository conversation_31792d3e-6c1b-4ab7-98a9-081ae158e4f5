#!/usr/bin/env python3
"""
Momentum Strategy Optimizer Script for Synergy7 Trading System

This script runs the momentum strategy optimizer on historical data
and evaluates its performance.
"""

import os
import sys
import logging
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import json
import argparse
from datetime import datetime, timedelta
from pathlib import Path

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import modules
from core.strategies.momentum_optimizer import MomentumOptimizer
from shared.utils.config_loader import get_config_loader, load_config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("optimize_momentum")

def load_price_data(file_path=None, symbol="SOL-USDC", days=180):
    """
    Load price data from file or generate synthetic data for testing.
    
    Args:
        file_path: Path to price data file (CSV)
        symbol: Symbol to use for synthetic data
        days: Number of days of synthetic data to generate
        
    Returns:
        DataFrame containing price data
    """
    if file_path and os.path.exists(file_path):
        logger.info(f"Loading price data from {file_path}")
        try:
            df = pd.read_csv(file_path)
            
            # Convert timestamp to datetime if needed
            if 'timestamp' in df.columns:
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                df.set_index('timestamp', inplace=True)
            
            return df
        except Exception as e:
            logger.error(f"Error loading price data: {str(e)}")
    
    # Generate synthetic data for testing
    logger.info(f"Generating synthetic price data for {symbol} ({days} days)")
    
    # Create date range
    dates = pd.date_range(end=datetime.now(), periods=days, freq="D")
    
    # Generate price series with some realistic patterns
    base_price = 100.0
    if symbol == "SOL-USDC":
        base_price = 100.0
    elif symbol == "JTO-USDC":
        base_price = 2.0
    elif symbol == "BONK-USDC":
        base_price = 0.00002
    
    # Generate price series with trend, cycles, and noise
    trend = np.linspace(0, 0.5, days)  # Upward trend
    cycles = 0.1 * np.sin(np.linspace(0, 15, days)) + 0.05 * np.sin(np.linspace(0, 30, days))  # Cycles
    noise = np.random.normal(0, 0.02, days)  # Random noise
    
    # Combine components
    returns = trend + cycles + noise
    
    # Convert returns to prices
    prices = base_price * (1 + returns).cumprod()
    
    # Add some momentum patterns
    momentum_periods = [
        (int(days * 0.2), int(days * 0.3)),  # Strong uptrend
        (int(days * 0.5), int(days * 0.6)),  # Strong downtrend
        (int(days * 0.7), int(days * 0.8)),  # Choppy market
    ]
    
    for start, end in momentum_periods:
        if start < end and end < days:
            if start == momentum_periods[0][0]:  # Uptrend
                prices[start:end] = prices[start] * (1 + np.linspace(0, 0.3, end - start)).cumprod()
            elif start == momentum_periods[1][0]:  # Downtrend
                prices[start:end] = prices[start] * (1 + np.linspace(0, -0.2, end - start)).cumprod()
            else:  # Choppy
                prices[start:end] = prices[start] * (1 + 0.05 * np.sin(np.linspace(0, 10, end - start)))
    
    # Create DataFrame
    df = pd.DataFrame({
        "close": prices,
        "high": prices * (1 + np.random.uniform(0, 0.02, days)),
        "low": prices * (1 - np.random.uniform(0, 0.02, days)),
        "volume": np.random.normal(1000000, 200000, days)
    }, index=dates)
    
    return df

def run_optimization(price_data, config=None, output_dir="output/momentum_optimizer"):
    """
    Run the momentum strategy optimizer.
    
    Args:
        price_data: DataFrame containing price data
        config: Configuration dictionary
        output_dir: Output directory for results
        
    Returns:
        Dictionary containing optimization results
    """
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Default configuration
    if config is None:
        config = {
            "window_size_range": [5, 10, 15, 20, 25, 30],
            "threshold_range": [0.005, 0.01, 0.015, 0.02, 0.025, 0.03],
            "smoothing_factor_range": [0.1, 0.2, 0.3, 0.4, 0.5],
            "max_value_range": [0.05, 0.1, 0.15, 0.2, 0.25],
            "train_test_split": 0.7,
            "walk_forward_window": 30,
            "walk_forward_step": 7,
            "min_trades": 10,
            "output_dir": output_dir
        }
    
    # Initialize optimizer
    optimizer = MomentumOptimizer(config)
    
    # Run optimization
    logger.info("Running parameter optimization...")
    results = optimizer.optimize_parameters(price_data)
    
    # Run walk-forward optimization
    logger.info("Running walk-forward optimization...")
    wfo_results = optimizer.walk_forward_optimization(price_data)
    
    # Plot optimization results
    logger.info("Plotting optimization results...")
    optimizer.plot_optimization_results(results["all_results"])
    
    # Save combined results
    combined_results = {
        "optimization_results": results,
        "walk_forward_results": wfo_results
    }
    
    output_file = os.path.join(output_dir, f"combined_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
    with open(output_file, "w") as f:
        json.dump(combined_results, f, indent=2, default=str)
    
    logger.info(f"Saved combined results to {output_file}")
    
    return combined_results

def backtest_strategy(price_data, params, output_dir="output/momentum_backtest"):
    """
    Backtest the momentum strategy with the optimized parameters.
    
    Args:
        price_data: DataFrame containing price data
        params: Dictionary containing strategy parameters
        output_dir: Output directory for results
        
    Returns:
        Dictionary containing backtest results
    """
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Initialize optimizer (used for signal generation)
    optimizer = MomentumOptimizer()
    
    # Generate signals
    logger.info(f"Generating signals with parameters: {params}")
    signals = optimizer.calculate_momentum(
        price_data,
        window_size=params["window_size"],
        threshold=params["threshold"],
        smoothing_factor=params["smoothing_factor"],
        max_value=params["max_value"]
    )
    
    # Calculate performance
    performance = optimizer.calculate_performance(signals)
    
    # Plot equity curve
    plt.figure(figsize=(12, 6))
    plt.plot(signals.index, signals["cumulative_returns"], label="Strategy")
    plt.plot(signals.index, (1 + signals["returns"]).cumprod(), label="Buy & Hold")
    plt.title(f"Momentum Strategy Equity Curve\nSharpe: {performance['sharpe_ratio']:.2f}, Win Rate: {performance['win_rate']:.2%}")
    plt.xlabel("Date")
    plt.ylabel("Cumulative Return")
    plt.legend()
    plt.grid(True)
    
    # Save plot
    output_file = os.path.join(output_dir, f"equity_curve_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png")
    plt.savefig(output_file)
    logger.info(f"Saved equity curve to {output_file}")
    
    # Plot drawdown
    plt.figure(figsize=(12, 6))
    plt.plot(signals.index, signals["drawdown"])
    plt.title(f"Momentum Strategy Drawdown\nMax Drawdown: {performance['max_drawdown']:.2%}")
    plt.xlabel("Date")
    plt.ylabel("Drawdown")
    plt.grid(True)
    
    # Save plot
    output_file = os.path.join(output_dir, f"drawdown_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png")
    plt.savefig(output_file)
    logger.info(f"Saved drawdown plot to {output_file}")
    
    # Save signals
    signals_file = os.path.join(output_dir, f"signals_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv")
    signals.to_csv(signals_file)
    logger.info(f"Saved signals to {signals_file}")
    
    # Save performance metrics
    metrics_file = os.path.join(output_dir, f"performance_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
    with open(metrics_file, "w") as f:
        json.dump(performance, f, indent=2, default=str)
    logger.info(f"Saved performance metrics to {metrics_file}")
    
    return {
        "signals": signals,
        "performance": performance
    }

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Optimize and backtest momentum strategy")
    parser.add_argument("--data-file", help="Path to price data file (CSV)")
    parser.add_argument("--symbol", default="SOL-USDC", help="Symbol to use for synthetic data")
    parser.add_argument("--days", type=int, default=180, help="Number of days of synthetic data to generate")
    parser.add_argument("--output-dir", default="output/momentum", help="Output directory for results")
    parser.add_argument("--skip-optimization", action="store_true", help="Skip optimization and use default parameters")
    parser.add_argument("--params", help="JSON string of parameters to use for backtest")
    args = parser.parse_args()
    
    # Create output directories
    os.makedirs(args.output_dir, exist_ok=True)
    optimizer_dir = os.path.join(args.output_dir, "optimizer")
    backtest_dir = os.path.join(args.output_dir, "backtest")
    
    # Load price data
    price_data = load_price_data(args.data_file, args.symbol, args.days)
    
    # Run optimization if not skipped
    if not args.skip_optimization:
        results = run_optimization(price_data, output_dir=optimizer_dir)
        best_params = results["optimization_results"]["best_params"]
    else:
        # Use default or provided parameters
        if args.params:
            best_params = json.loads(args.params)
        else:
            best_params = {
                "window_size": 20,
                "threshold": 0.01,
                "smoothing_factor": 0.2,
                "max_value": 0.1
            }
    
    # Print best parameters
    logger.info(f"Best parameters: {best_params}")
    
    # Backtest strategy
    backtest_results = backtest_strategy(price_data, best_params, output_dir=backtest_dir)
    
    # Print performance metrics
    logger.info("Performance metrics:")
    for key, value in backtest_results["performance"].items():
        logger.info(f"  {key}: {value}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
