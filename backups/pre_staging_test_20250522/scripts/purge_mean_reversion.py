#!/usr/bin/env python3
"""
Purge Mean Reversion Strategy Files

This script identifies and purges files related to the mean reversion strategy
from the Synergy7 Trading System.
"""

import os
import sys
import logging
import argparse
import re
import shutil
from typing import List, Tuple

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("purge_mean_reversion")

def find_mean_reversion_files(root_dir: str) -> List[str]:
    """
    Find files related to the mean reversion strategy.
    
    Args:
        root_dir: Root directory to search
        
    Returns:
        List of file paths
    """
    mean_reversion_files = []
    
    # Patterns to match
    patterns = [
        r"mean_reversion\.py$",
        r"mean_reversion_.*\.py$",
        r"test_mean_reversion.*\.py$"
    ]
    
    # Walk through directory tree
    for dirpath, dirnames, filenames in os.walk(root_dir):
        for filename in filenames:
            file_path = os.path.join(dirpath, filename)
            
            # Check if filename matches any pattern
            if any(re.search(pattern, filename) for pattern in patterns):
                mean_reversion_files.append(file_path)
                continue
            
            # Check file content for mean reversion references
            if filename.endswith(".py"):
                try:
                    with open(file_path, "r") as f:
                        content = f.read()
                        if "mean_reversion" in content.lower() and "strategy" in content.lower():
                            # Check if it's a substantial reference, not just a comment or import
                            if re.search(r"class\s+MeanReversion", content) or re.search(r"def\s+mean_reversion", content):
                                mean_reversion_files.append(file_path)
                except Exception as e:
                    logger.warning(f"Error reading file {file_path}: {str(e)}")
    
    return mean_reversion_files

def analyze_files(files: List[str]) -> Tuple[List[str], List[str]]:
    """
    Analyze files to determine which can be safely removed and which need manual review.
    
    Args:
        files: List of file paths
        
    Returns:
        Tuple of (safe_to_remove, need_review)
    """
    safe_to_remove = []
    need_review = []
    
    for file_path in files:
        filename = os.path.basename(file_path)
        
        # Files that are definitely mean reversion strategy files
        if re.match(r"mean_reversion\.py$", filename) or re.match(r"mean_reversion_.*\.py$", filename):
            safe_to_remove.append(file_path)
        # Test files for mean reversion
        elif re.match(r"test_mean_reversion.*\.py$", filename):
            safe_to_remove.append(file_path)
        # Other files that might have mean reversion references
        else:
            need_review.append(file_path)
    
    return safe_to_remove, need_review

def backup_files(files: List[str], backup_dir: str) -> None:
    """
    Backup files before removal.
    
    Args:
        files: List of file paths
        backup_dir: Backup directory
    """
    os.makedirs(backup_dir, exist_ok=True)
    
    for file_path in files:
        rel_path = os.path.relpath(file_path, os.path.dirname(backup_dir))
        backup_path = os.path.join(backup_dir, rel_path)
        
        # Create directory structure
        os.makedirs(os.path.dirname(backup_path), exist_ok=True)
        
        # Copy file
        shutil.copy2(file_path, backup_path)
        logger.info(f"Backed up {file_path} to {backup_path}")

def remove_files(files: List[str], dry_run: bool = True) -> None:
    """
    Remove files.
    
    Args:
        files: List of file paths
        dry_run: If True, don't actually remove files
    """
    for file_path in files:
        if dry_run:
            logger.info(f"Would remove {file_path}")
        else:
            try:
                os.remove(file_path)
                logger.info(f"Removed {file_path}")
            except Exception as e:
                logger.error(f"Error removing {file_path}: {str(e)}")

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Purge mean reversion strategy files")
    parser.add_argument("--root-dir", default=".", help="Root directory to search")
    parser.add_argument("--backup-dir", default="backup/mean_reversion", help="Backup directory")
    parser.add_argument("--remove", action="store_true", help="Actually remove files (default is dry run)")
    parser.add_argument("--no-backup", action="store_true", help="Skip backup")
    args = parser.parse_args()
    
    # Find mean reversion files
    logger.info(f"Searching for mean reversion files in {args.root_dir}...")
    mean_reversion_files = find_mean_reversion_files(args.root_dir)
    
    if not mean_reversion_files:
        logger.info("No mean reversion files found")
        return
    
    logger.info(f"Found {len(mean_reversion_files)} mean reversion files")
    
    # Analyze files
    safe_to_remove, need_review = analyze_files(mean_reversion_files)
    
    logger.info(f"{len(safe_to_remove)} files can be safely removed")
    logger.info(f"{len(need_review)} files need manual review")
    
    # Print files that need review
    if need_review:
        logger.info("Files that need manual review:")
        for file_path in need_review:
            logger.info(f"  {file_path}")
    
    # Backup files
    if not args.no_backup and safe_to_remove:
        logger.info(f"Backing up files to {args.backup_dir}...")
        backup_files(safe_to_remove, args.backup_dir)
    
    # Remove files
    if safe_to_remove:
        remove_files(safe_to_remove, not args.remove)
        
        if not args.remove:
            logger.info("Dry run completed. Use --remove to actually remove files")
    
    logger.info("Done")

if __name__ == "__main__":
    main()
