#!/usr/bin/env python3
"""
Configuration Validation Script for Synergy7 System

This script validates the configuration files and environment variables for the Synergy7 system.
It checks for common configuration issues and provides suggestions for fixing them.

Usage:
    python scripts/validate_config.py [--config CONFIG_PATH] [--env ENV_PATH] [--fix]
"""

import os
import sys
import argparse
import logging
from pathlib import Path
from typing import Dict, Any, List, Tuple

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.config import ConfigManager, EnvManager, ConfigValidationError

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('validate_config')

def parse_args():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description='Validate Synergy7 configuration')
    parser.add_argument('--config', type=str, default='config.yaml',
                        help='Path to configuration file (default: config.yaml)')
    parser.add_argument('--env', type=str, default='.env',
                        help='Path to environment file (default: .env)')
    parser.add_argument('--fix', action='store_true',
                        help='Attempt to fix common configuration issues')
    parser.add_argument('--verbose', action='store_true',
                        help='Enable verbose output')
    return parser.parse_args()

def check_config_file(config_path: str) -> Tuple[bool, List[str]]:
    """
    Check if the configuration file exists and is valid.
    
    Args:
        config_path: Path to the configuration file
        
    Returns:
        Tuple of (success, issues)
    """
    issues = []
    
    # Check if the file exists
    if not os.path.exists(config_path):
        issues.append(f"Configuration file not found: {config_path}")
        return False, issues
    
    # Check if the file is readable
    try:
        with open(config_path, 'r') as f:
            pass
    except Exception as e:
        issues.append(f"Error reading configuration file: {str(e)}")
        return False, issues
    
    # Initialize configuration manager
    config_manager = ConfigManager(config_path=config_path)
    
    # Try to load the configuration
    try:
        config = config_manager.load()
        logger.info(f"Successfully loaded configuration from {config_path}")
    except ConfigValidationError as e:
        issues.append(f"Configuration validation failed: {str(e)}")
        return False, issues
    except Exception as e:
        issues.append(f"Error loading configuration: {str(e)}")
        return False, issues
    
    # Check for common configuration issues
    if 'mode' in config:
        # Check if any mode is enabled
        enabled_modes = [mode for mode, enabled in config['mode'].items() if enabled]
        if not enabled_modes:
            issues.append("No mode is enabled in configuration")
        elif len(enabled_modes) > 1:
            issues.append(f"Multiple modes are enabled in configuration: {enabled_modes}")
    else:
        issues.append("Missing mode section in configuration")
    
    # Check for required sections
    required_sections = ['solana', 'wallet', 'risk', 'execution']
    for section in required_sections:
        if section not in config:
            issues.append(f"Missing required section in configuration: {section}")
    
    # Check for environment variable references
    env_vars = set()
    
    def extract_env_vars(value):
        if isinstance(value, str) and '${' in value and '}' in value:
            start = value.find('${')
            end = value.find('}', start)
            if start != -1 and end != -1:
                env_var = value[start+2:end]
                env_vars.add(env_var)
    
    def traverse_dict(d):
        for key, value in d.items():
            if isinstance(value, dict):
                traverse_dict(value)
            elif isinstance(value, list):
                for item in value:
                    if isinstance(item, dict):
                        traverse_dict(item)
                    else:
                        extract_env_vars(item)
            else:
                extract_env_vars(value)
    
    traverse_dict(config)
    
    # Check if environment variables are set
    missing_vars = [var for var in env_vars if var not in os.environ]
    if missing_vars:
        issues.append(f"Missing environment variables referenced in configuration: {missing_vars}")
    
    return len(issues) == 0, issues

def check_env_file(env_path: str) -> Tuple[bool, List[str]]:
    """
    Check if the environment file exists and is valid.
    
    Args:
        env_path: Path to the environment file
        
    Returns:
        Tuple of (success, issues)
    """
    issues = []
    
    # Check if the file exists
    if not os.path.exists(env_path):
        issues.append(f"Environment file not found: {env_path}")
        return False, issues
    
    # Check if the file is readable
    try:
        with open(env_path, 'r') as f:
            pass
    except Exception as e:
        issues.append(f"Error reading environment file: {str(e)}")
        return False, issues
    
    # Initialize environment manager
    env_manager = EnvManager(env_file=env_path)
    
    # Try to load the environment variables
    try:
        env_vars = env_manager.load(validate=False)
        logger.info(f"Successfully loaded environment variables from {env_path}")
    except Exception as e:
        issues.append(f"Error loading environment variables: {str(e)}")
        return False, issues
    
    # Check for common environment variable issues
    
    # Check for comments on the same line as values
    with open(env_path, 'r') as f:
        for i, line in enumerate(f, 1):
            line = line.strip()
            if line and not line.startswith('#'):
                if '#' in line and '=' in line:
                    var_name = line.split('=')[0].strip()
                    issues.append(f"Line {i}: Comment on same line as value for {var_name}")
    
    # Check for required environment variables
    required_vars = ['HELIUS_API_KEY', 'WALLET_ADDRESS']
    for var in required_vars:
        if not env_manager.is_set(var):
            issues.append(f"Missing required environment variable: {var}")
    
    # Check for mode consistency
    mode_vars = {
        'TRADING_MODE': env_manager.get('TRADING_MODE'),
        'TRADING_ENABLED': env_manager.get_bool('TRADING_ENABLED'),
        'PAPER_TRADING': env_manager.get_bool('PAPER_TRADING'),
        'BACKTESTING_ENABLED': env_manager.get_bool('BACKTESTING_ENABLED'),
        'DRY_RUN': env_manager.get_bool('DRY_RUN')
    }
    
    # Check if TRADING_MODE is consistent with individual mode flags
    if mode_vars['TRADING_MODE'] == 'live':
        if not mode_vars['TRADING_ENABLED']:
            issues.append("TRADING_MODE is 'live' but TRADING_ENABLED is not true")
        if mode_vars['PAPER_TRADING']:
            issues.append("TRADING_MODE is 'live' but PAPER_TRADING is true")
        if mode_vars['BACKTESTING_ENABLED']:
            issues.append("TRADING_MODE is 'live' but BACKTESTING_ENABLED is true")
    elif mode_vars['TRADING_MODE'] == 'paper':
        if not mode_vars['TRADING_ENABLED']:
            issues.append("TRADING_MODE is 'paper' but TRADING_ENABLED is not true")
        if not mode_vars['PAPER_TRADING']:
            issues.append("TRADING_MODE is 'paper' but PAPER_TRADING is not true")
        if mode_vars['BACKTESTING_ENABLED']:
            issues.append("TRADING_MODE is 'paper' but BACKTESTING_ENABLED is true")
    elif mode_vars['TRADING_MODE'] == 'backtest':
        if mode_vars['TRADING_ENABLED']:
            issues.append("TRADING_MODE is 'backtest' but TRADING_ENABLED is true")
        if mode_vars['PAPER_TRADING']:
            issues.append("TRADING_MODE is 'backtest' but PAPER_TRADING is true")
        if not mode_vars['BACKTESTING_ENABLED']:
            issues.append("TRADING_MODE is 'backtest' but BACKTESTING_ENABLED is not true")
    
    return len(issues) == 0, issues

def fix_config_issues(config_path: str, issues: List[str]) -> bool:
    """
    Attempt to fix common configuration issues.
    
    Args:
        config_path: Path to the configuration file
        issues: List of issues to fix
        
    Returns:
        True if any issues were fixed, False otherwise
    """
    fixed = False
    
    # Initialize configuration manager
    config_manager = ConfigManager(config_path=config_path)
    
    try:
        # Load the configuration
        config = config_manager.load()
        
        # Fix mode issues
        if 'mode' in config:
            # Check if multiple modes are enabled
            enabled_modes = [mode for mode, enabled in config['mode'].items() if enabled]
            if len(enabled_modes) > 1:
                logger.info(f"Fixing multiple enabled modes: {enabled_modes}")
                # Keep only the first enabled mode
                for mode in enabled_modes[1:]:
                    config['mode'][mode] = False
                fixed = True
            elif len(enabled_modes) == 0:
                logger.info("Fixing no enabled modes")
                # Enable paper trading mode by default
                config['mode']['paper_trading'] = True
                fixed = True
        
        # TODO: Add more fixes for common issues
        
        # Save the fixed configuration
        if fixed:
            import yaml
            with open(config_path, 'w') as f:
                yaml.dump(config, f, default_flow_style=False)
            logger.info(f"Fixed configuration issues and saved to {config_path}")
    except Exception as e:
        logger.error(f"Error fixing configuration issues: {str(e)}")
        return False
    
    return fixed

def fix_env_issues(env_path: str, issues: List[str]) -> bool:
    """
    Attempt to fix common environment variable issues.
    
    Args:
        env_path: Path to the environment file
        issues: List of issues to fix
        
    Returns:
        True if any issues were fixed, False otherwise
    """
    fixed = False
    
    # Fix comments on the same line as values
    comment_issues = [issue for issue in issues if "Comment on same line as value" in issue]
    if comment_issues:
        try:
            with open(env_path, 'r') as f:
                lines = f.readlines()
            
            with open(env_path, 'w') as f:
                for line in lines:
                    if '=' in line and '#' in line and not line.strip().startswith('#'):
                        # Split at the first occurrence of '='
                        var_part, value_part = line.split('=', 1)
                        
                        # Check if there's a comment in the value part
                        if '#' in value_part:
                            # Split at the first occurrence of '#'
                            value, comment = value_part.split('#', 1)
                            # Write the variable and value on one line, and the comment on the next
                            f.write(f"{var_part}={value.strip()}\n")
                            f.write(f"# {comment}")
                            fixed = True
                        else:
                            f.write(line)
                    else:
                        f.write(line)
            
            if fixed:
                logger.info(f"Fixed comment issues in {env_path}")
        except Exception as e:
            logger.error(f"Error fixing environment file issues: {str(e)}")
            return False
    
    # TODO: Add more fixes for common issues
    
    return fixed

def main():
    """Main function."""
    args = parse_args()
    
    # Set logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    logger.info("Validating Synergy7 configuration")
    logger.info(f"Configuration file: {args.config}")
    logger.info(f"Environment file: {args.env}")
    
    # Check configuration file
    config_success, config_issues = check_config_file(args.config)
    if config_success:
        logger.info("Configuration file is valid")
    else:
        logger.error("Configuration file has issues:")
        for issue in config_issues:
            logger.error(f"  - {issue}")
        
        # Try to fix issues
        if args.fix:
            logger.info("Attempting to fix configuration issues")
            if fix_config_issues(args.config, config_issues):
                logger.info("Fixed some configuration issues")
            else:
                logger.warning("Could not fix configuration issues")
    
    # Check environment file
    env_success, env_issues = check_env_file(args.env)
    if env_success:
        logger.info("Environment file is valid")
    else:
        logger.error("Environment file has issues:")
        for issue in env_issues:
            logger.error(f"  - {issue}")
        
        # Try to fix issues
        if args.fix:
            logger.info("Attempting to fix environment issues")
            if fix_env_issues(args.env, env_issues):
                logger.info("Fixed some environment issues")
            else:
                logger.warning("Could not fix environment issues")
    
    # Overall result
    if config_success and env_success:
        logger.info("Validation successful: Configuration is valid")
        return 0
    else:
        logger.error("Validation failed: Configuration has issues")
        return 1

if __name__ == "__main__":
    sys.exit(main())
