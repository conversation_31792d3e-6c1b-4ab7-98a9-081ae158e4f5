# Synergy7 Trading System Tests

This directory contains tests for the Synergy7 Trading System. The tests are organized by test type to improve test discovery and execution.

## Test Types

- **Unit Tests**: Tests that test a specific function or class in isolation.
- **Integration Tests**: Tests that check the interaction between components.
- **Functional Tests**: Tests that check the functionality of the system.
- **Performance Tests**: Tests that check the performance of the system.
- **End-to-End Tests**: Tests that check the entire system.

## Directory Structure

```
tests/
├── conftest.py                # Global test configuration and fixtures
├── unit/                      # Unit tests
│   ├── core/                  # Unit tests for core components
│   │   ├── strategies/        # Unit tests for strategies
│   │   ├── engine/            # Unit tests for engine components
│   │   ├── risk/              # Unit tests for risk management components
│   │   ├── data/              # Unit tests for data ingestion components
│   │   └── execution/         # Unit tests for execution components
│   └── shared/                # Unit tests for shared components
│       ├── utils/             # Unit tests for utility functions
│       ├── rust/              # Unit tests for Rust integration
│       └── solana_utils/      # Unit tests for Solana utilities
├── integration/               # Integration tests
│   ├── core/                  # Integration tests for core components
│   └── shared/                # Integration tests for shared components
├── functional/                # Functional tests
│   ├── core/                  # Functional tests for core components
│   └── shared/                # Functional tests for shared components
├── performance/               # Performance tests
│   ├── core/                  # Performance tests for core components
│   └── shared/                # Performance tests for shared components
└── e2e/                       # End-to-end tests
    ├── core/                  # End-to-end tests for core components
    └── shared/                # End-to-end tests for shared components
```

## Running Tests

### Running All Tests

```bash
pytest
```

### Running Tests by Type

```bash
# Run unit tests
pytest tests/unit/

# Run integration tests
pytest tests/integration/ --run-integration

# Run functional tests
pytest tests/functional/

# Run performance tests
pytest tests/performance/

# Run end-to-end tests
pytest tests/e2e/ --run-e2e
```

### Running Tests by Component

```bash
# Run tests for strategies
pytest tests/unit/core/strategies/

# Run tests for engine components
pytest tests/unit/core/engine/

# Run tests for risk management components
pytest tests/unit/core/risk/

# Run tests for data ingestion components
pytest tests/unit/core/data/

# Run tests for execution components
pytest tests/unit/core/execution/
```

### Running Tests with Markers

```bash
# Run tests marked as slow
pytest -m slow --run-slow

# Run tests marked as integration
pytest -m integration --run-integration

# Run tests marked as e2e
pytest -m e2e --run-e2e
```

## Environment Variables

The tests require the following environment variables to be set:

- `HELIUS_API_KEY`: Your Helius API key
- `WALLET_ADDRESS`: Your Solana wallet address
- `BIRDEYE_API_KEY`: Your Birdeye API key (for some tests)

## Writing Tests

### Test Naming Conventions

- Test files should be named `test_*.py`.
- Test classes should be named `Test*`.
- Test functions should be named `test_*`.

### Test Fixtures

Global test fixtures are defined in `conftest.py`. These fixtures are available to all tests.

### Test Markers

Tests can be marked with the following markers:

- `@pytest.mark.unit`: Unit tests
- `@pytest.mark.integration`: Integration tests
- `@pytest.mark.functional`: Functional tests
- `@pytest.mark.performance`: Performance tests
- `@pytest.mark.e2e`: End-to-end tests
- `@pytest.mark.slow`: Slow tests

### Test Dependencies

Tests may require the following dependencies:

- `pytest`: Testing framework
- `pytest-asyncio`: Async support for pytest
- `pytest-mock`: Mocking support for pytest
- `pytest-cov`: Coverage support for pytest

Install these dependencies with:

```bash
pip install pytest pytest-asyncio pytest-mock pytest-cov
```

## Test Coverage

To run tests with coverage:

```bash
pytest --cov=core --cov=shared
```

To generate a coverage report:

```bash
pytest --cov=core --cov=shared --cov-report=html
```

The coverage report will be generated in the `htmlcov` directory.

## Legacy Tests

The following legacy tests are still available:

- `test_helius_integration.py`: Tests for the Helius RPC integration
- `test_jito_integration.py`: Tests for the Jito RPC integration
- `test_solders.py`: Tests for Solana Solders integration
- `test_solders_fix.py`: Tests for Solders fixes and workarounds
- `test_transaction.py`: Tests for transaction creation and signing
- `test_tx_creation.py`: Tests for transaction building
- `verify_simulation.py`: Script to verify simulation results

## Continuous Integration

These tests are designed to be run as part of the CI/CD pipeline to ensure that all components work correctly before deployment.
