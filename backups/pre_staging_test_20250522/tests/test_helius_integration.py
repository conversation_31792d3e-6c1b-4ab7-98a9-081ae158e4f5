#!/usr/bin/env python3
"""
Helius Integration Test

This module contains tests for the Helius RPC integration.
"""

import os
import sys
import json
import asyncio
import unittest
from pathlib import Path

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import from phase_4_deployment directory
from phase_4_deployment.rpc_execution.helius_client import HeliusClient
from phase_4_deployment.rpc_execution.helius_executor import HeliusExecutor

class TestHeliusIntegration(unittest.TestCase):
    """Test cases for Helius integration."""

    def setUp(self):
        """Set up test environment."""
        # Ensure environment variables are set
        self.api_key = os.getenv("HELIUS_API_KEY")
        self.wallet_address = os.getenv("WALLET_ADDRESS")

        # Skip tests if API key is not available
        if not self.api_key:
            self.skipTest("HELIUS_API_KEY environment variable not set")

        # Create output directory if it doesn't exist
        self.output_dir = Path("output")
        self.output_dir.mkdir(exist_ok=True)

    def test_helius_client_initialization(self):
        """Test that the Helius client initializes correctly."""
        client = HeliusClient()
        self.assertIsNotNone(client)
        self.assertEqual(client.api_key, self.api_key)
        self.assertTrue(client.rpc_url.startswith("https://mainnet.helius-rpc.com/"))

    def test_helius_executor_initialization(self):
        """Test that the Helius executor initializes correctly."""
        executor = HeliusExecutor()
        self.assertIsNotNone(executor)
        # The executor might use either URL format depending on configuration
        self.assertTrue(
            executor.helius_rpc_url.startswith("https://mainnet.helius-rpc.com/") or
            executor.helius_rpc_url.startswith("https://rpc.helius.xyz/")
        )
        self.assertIsNotNone(executor.helius_client)

    def test_get_system_program_balance(self):
        """Test getting the system program balance."""
        async def run_test():
            client = HeliusClient()

            # Test system program address
            test_address = "********************************"
            payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "getBalance",
                "params": [test_address]
            }

            response = await client.http_client.post(
                client.rpc_url,
                json=payload
            )
            response.raise_for_status()
            result = response.json()

            self.assertIn('result', result)
            self.assertIn('value', result['result'])
            self.assertEqual(result['result']['value'], 1)  # System program has 1 lamport

            await client.close()

        asyncio.run(run_test())

    def test_get_wallet_balance(self):
        """Test getting a wallet balance."""
        # Skip test if wallet address is not available
        if not self.wallet_address:
            self.skipTest("WALLET_ADDRESS environment variable not set")

        async def run_test():
            client = HeliusClient()

            balance_result = await client.get_balance(self.wallet_address)

            self.assertTrue(balance_result['success'])
            self.assertIn('balance_sol', balance_result)
            self.assertGreater(balance_result['balance_sol'], 0)

            await client.close()

        asyncio.run(run_test())

    def test_executor_metrics(self):
        """Test that the executor metrics are initialized correctly."""
        executor = HeliusExecutor()
        metrics = executor.get_metrics()

        self.assertIn('executor', metrics)
        self.assertIn('client', metrics)

        # Check executor metrics
        executor_metrics = metrics['executor']
        self.assertEqual(executor_metrics['total_transactions'], 0)
        self.assertEqual(executor_metrics['successful_transactions'], 0)
        self.assertEqual(executor_metrics['failed_transactions'], 0)

        # Check client metrics
        client_metrics = metrics['client']
        self.assertEqual(client_metrics['total_requests'], 0)
        self.assertEqual(client_metrics['successful_requests'], 0)
        self.assertEqual(client_metrics['failed_requests'], 0)

    def tearDown(self):
        """Clean up after tests."""
        # Nothing to clean up for now
        pass

if __name__ == "__main__":
    # Set environment variables for testing
    if not os.getenv("HELIUS_API_KEY"):
        os.environ["HELIUS_API_KEY"] = "dda9f776-9a40-447d-9ca4-22a27c21169e"

    if not os.getenv("WALLET_ADDRESS"):
        os.environ["WALLET_ADDRESS"] = "J2FkQP683JsCsABxTCx7iGisdQZQPgFDMSvgPhGPE3bz"

    unittest.main()
