#!/usr/bin/env python3
"""
Test script for the transaction executor.
"""

import os
import sys
import json
import unittest
import asyncio
from pathlib import Path

# Add the parent directory to the path
sys.path.append(str(Path(__file__).parent.parent))

try:
    from phase_4_deployment.rpc_execution.transaction_executor import TransactionExecutor, RpcClientInterface
    from phase_4_deployment.rpc_execution.helius_client import HeliusClient
    EXECUTOR_AVAILABLE = True
except ImportError:
    EXECUTOR_AVAILABLE = False
    print("WARNING: transaction_executor not available, skipping tests")

# Mock RPC client for testing
class MockRpcClient(RpcClientInterface):
    """Mock RPC client for testing."""
    
    def __init__(self, should_succeed=True):
        """Initialize the mock client."""
        self.should_succeed = should_succeed
        self.transactions = []
        self.bundles = []
        self.simulations = []
        self.metrics = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0
        }
    
    async def send_transaction(self, transaction, opts=None):
        """Mock sending a transaction."""
        self.transactions.append((transaction, opts))
        self.metrics['total_requests'] += 1
        
        if self.should_succeed:
            self.metrics['successful_requests'] += 1
            return {
                'success': True,
                'signature': 'mock_signature_12345',
                'provider': 'mock'
            }
        else:
            self.metrics['failed_requests'] += 1
            return {
                'success': False,
                'error': 'Mock error',
                'provider': 'mock'
            }
    
    async def send_bundle(self, transactions, opts=None):
        """Mock sending a bundle."""
        self.bundles.append((transactions, opts))
        self.metrics['total_requests'] += 1
        
        if self.should_succeed:
            self.metrics['successful_requests'] += 1
            return {
                'success': True,
                'bundle_id': 'mock_bundle_12345',
                'provider': 'mock'
            }
        else:
            self.metrics['failed_requests'] += 1
            return {
                'success': False,
                'error': 'Mock error',
                'provider': 'mock'
            }
    
    async def simulate_transaction(self, transaction, opts=None):
        """Mock simulating a transaction."""
        self.simulations.append((transaction, opts))
        self.metrics['total_requests'] += 1
        
        if self.should_succeed:
            self.metrics['successful_requests'] += 1
            return {
                'success': True,
                'result': {'value': {'logs': ['Program log: Hello, World!']}},
                'provider': 'mock'
            }
        else:
            self.metrics['failed_requests'] += 1
            return {
                'success': False,
                'error': 'Mock error',
                'provider': 'mock'
            }
    
    async def simulate_bundle(self, transactions, opts=None):
        """Mock simulating a bundle."""
        self.simulations.append((transactions, opts))
        self.metrics['total_requests'] += 1
        
        if self.should_succeed:
            self.metrics['successful_requests'] += 1
            return {
                'success': True,
                'results': [{'value': {'logs': ['Program log: Hello, World!']}}],
                'provider': 'mock'
            }
        else:
            self.metrics['failed_requests'] += 1
            return {
                'success': False,
                'error': 'Mock error',
                'provider': 'mock'
            }
    
    async def get_transaction_status(self, signature):
        """Mock getting transaction status."""
        self.metrics['total_requests'] += 1
        
        if self.should_succeed:
            self.metrics['successful_requests'] += 1
            return {
                'success': True,
                'status': {'confirmations': 32, 'status': {'Ok': None}},
                'provider': 'mock'
            }
        else:
            self.metrics['failed_requests'] += 1
            return {
                'success': False,
                'error': 'Mock error',
                'provider': 'mock'
            }
    
    async def close(self):
        """Mock closing the client."""
        pass
    
    def get_metrics(self):
        """Get client metrics."""
        return self.metrics


@unittest.skipIf(not EXECUTOR_AVAILABLE, "transaction_executor not available")
class TestTransactionExecutor(unittest.TestCase):
    """Test cases for the transaction executor."""
    
    def setUp(self):
        """Set up the test environment."""
        # Create a mock RPC client
        self.mock_client = MockRpcClient(should_succeed=True)
        
        # Create the executor
        self.executor = TransactionExecutor(
            rpc_client=self.mock_client,
            max_retries=2,
            retry_delay=0.1,
            circuit_breaker_threshold=3,
            circuit_breaker_reset_time=1.0
        )
    
    async def async_test_execute_transaction(self):
        """Test executing a transaction."""
        # Execute a transaction
        result = await self.executor.execute_transaction("mock_transaction")
        
        # Check the result
        self.assertTrue(result['success'])
        self.assertEqual(result['signature'], 'mock_signature_12345')
        self.assertEqual(result['provider'], 'mock')
        
        # Check that the transaction was sent
        self.assertEqual(len(self.mock_client.transactions), 1)
        self.assertEqual(self.mock_client.transactions[0][0], "mock_transaction")
    
    async def async_test_execute_bundle(self):
        """Test executing a bundle."""
        # Execute a bundle
        result = await self.executor.execute_bundle(["tx1", "tx2"])
        
        # Check the result
        self.assertTrue(result['success'])
        self.assertEqual(result['bundle_id'], 'mock_bundle_12345')
        self.assertEqual(result['provider'], 'mock')
        
        # Check that the bundle was sent
        self.assertEqual(len(self.mock_client.bundles), 1)
        self.assertEqual(self.mock_client.bundles[0][0], ["tx1", "tx2"])
    
    async def async_test_simulate_bundle(self):
        """Test simulating a bundle."""
        # Simulate a bundle
        result = await self.executor.simulate_bundle(["tx1", "tx2"])
        
        # Check the result
        self.assertTrue(result['success'])
        self.assertIn('results', result)
        self.assertEqual(result['provider'], 'mock')
    
    async def async_test_circuit_breaker(self):
        """Test circuit breaker functionality."""
        # Create a failing client
        failing_client = MockRpcClient(should_succeed=False)
        
        # Create an executor with a low threshold
        executor = TransactionExecutor(
            rpc_client=failing_client,
            max_retries=1,
            retry_delay=0.1,
            circuit_breaker_threshold=2,
            circuit_breaker_reset_time=1.0
        )
        
        # Execute transactions until circuit breaker trips
        result1 = await executor.execute_transaction("tx1")
        self.assertFalse(result1['success'])
        self.assertFalse(executor.circuit_breaker_open)
        
        result2 = await executor.execute_transaction("tx2")
        self.assertFalse(result2['success'])
        self.assertTrue(executor.circuit_breaker_open)
        
        # Try another transaction, should be rejected by circuit breaker
        result3 = await executor.execute_transaction("tx3")
        self.assertFalse(result3['success'])
        self.assertEqual(result3['error'], 'Circuit breaker open')
        
        # Wait for circuit breaker to reset
        await asyncio.sleep(1.1)
        
        # Try again, should be accepted
        result4 = await executor.execute_transaction("tx4")
        self.assertFalse(result4['success'])
        self.assertNotEqual(result4['error'], 'Circuit breaker open')
    
    def test_execute_transaction(self):
        """Run the async test for executing a transaction."""
        asyncio.run(self.async_test_execute_transaction())
    
    def test_execute_bundle(self):
        """Run the async test for executing a bundle."""
        asyncio.run(self.async_test_execute_bundle())
    
    def test_simulate_bundle(self):
        """Run the async test for simulating a bundle."""
        asyncio.run(self.async_test_simulate_bundle())
    
    def test_circuit_breaker(self):
        """Run the async test for circuit breaker functionality."""
        asyncio.run(self.async_test_circuit_breaker())


if __name__ == "__main__":
    unittest.main()
