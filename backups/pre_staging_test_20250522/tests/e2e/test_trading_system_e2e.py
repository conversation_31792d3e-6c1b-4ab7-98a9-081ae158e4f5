"""
End-to-end tests for the trading system.
"""

import pytest
import asyncio
import os
import json
from pathlib import Path
from unittest.mock import MagicMock, patch
from core.engine.strategy_runner import StrategyRunner
from core.risk.risk_manager import RiskManager
from core.execution.transaction_executor import TransactionExecutor
from core.strategies.momentum import MomentumStrategy
from core.strategies.order_book_imbalance import OrderBookImbalanceStrategy

@pytest.mark.e2e
class TestTradingSystemE2E:
    """End-to-end tests for the trading system."""
    
    @pytest.fixture
    def config(self):
        """Create a test configuration."""
        return {
            "strategies": [
                {
                    "name": "momentum",
                    "markets": ["SOL-USDC", "JTO-USDC"],
                    "weight": 0.6,
                    "parameters": {
                        "window_size": 20,
                        "threshold": 0.01,
                        "max_value": 0.05,
                        "smoothing_factor": 0.1,
                        "max_position_size": 1.0
                    }
                },
                {
                    "name": "order_book_imbalance",
                    "markets": ["SOL-USDC", "JTO-USDC"],
                    "weight": 0.4,
                    "parameters": {
                        "window_size": 20,
                        "threshold": 0.1,
                        "max_value": 0.5,
                        "depth": 10,
                        "smoothing_factor": 0.2,
                        "max_position_size": 1.0
                    }
                }
            ],
            "market_microstructure": {
                "markets": ["SOL-USDC", "JTO-USDC"]
            },
            "strategy_runner": {
                "update_interval_ms": 1000,
                "publish_interval_ms": 1000
            },
            "risk_management": {
                "max_position_size": 0.1,
                "max_exposure": 0.5,
                "update_interval_ms": 1000,
                "publish_interval_ms": 1000,
                "metrics_interval_ms": 5000
            },
            "transaction_execution": {
                "update_interval_ms": 1000,
                "publish_interval_ms": 1000,
                "simulation_enabled": True,
                "dry_run": True
            },
            "rpc": {
                "endpoint": "https://api.mainnet-beta.solana.com"
            }
        }
    
    @pytest.fixture
    def mock_client(self):
        """Create a mock RustCommClient."""
        mock = MagicMock()
        mock.connect = MagicMock(return_value=asyncio.Future())
        mock.connect.return_value.set_result(None)
        mock.disconnect = MagicMock(return_value=asyncio.Future())
        mock.disconnect.return_value.set_result(None)
        mock.subscribe = MagicMock(return_value=asyncio.Future())
        mock.subscribe.return_value.set_result(None)
        mock.publish = MagicMock(return_value=asyncio.Future())
        mock.publish.return_value.set_result(None)
        return mock
    
    @pytest.fixture
    def mock_http_client(self):
        """Create a mock HTTP client."""
        mock = MagicMock()
        mock.post = MagicMock(return_value=asyncio.Future())
        mock.post.return_value.set_result(MagicMock())
        mock.post.return_value.result().json = MagicMock(return_value={"result": "success"})
        mock.post.return_value.result().raise_for_status = MagicMock()
        mock.aclose = MagicMock(return_value=asyncio.Future())
        mock.aclose.return_value.set_result(None)
        return mock
    
    @pytest.fixture
    def market_data(self):
        """Create test market data."""
        return {
            "order_books": {
                "SOL-USDC": {
                    "bids": [
                        {"price": 100.0, "size": 1.0},
                        {"price": 99.0, "size": 2.0}
                    ],
                    "asks": [
                        {"price": 101.0, "size": 1.0},
                        {"price": 102.0, "size": 2.0}
                    ],
                    "metrics": {
                        "mid_price": 100.5,
                        "spread": 1.0,
                        "spread_pct": 0.01,
                        "bid_ask_imbalance": 0.5
                    }
                },
                "JTO-USDC": {
                    "bids": [
                        {"price": 10.0, "size": 1.0},
                        {"price": 9.9, "size": 2.0}
                    ],
                    "asks": [
                        {"price": 10.1, "size": 1.0},
                        {"price": 10.2, "size": 2.0}
                    ],
                    "metrics": {
                        "mid_price": 10.05,
                        "spread": 0.1,
                        "spread_pct": 0.01,
                        "bid_ask_imbalance": 0.5
                    }
                }
            }
        }
    
    @pytest.mark.asyncio
    async def test_trading_system_e2e(self, config, mock_client, mock_http_client, market_data, tmp_path):
        """Test the end-to-end trading system."""
        with patch("core.engine.strategy_runner.RustCommClient", return_value=mock_client), \
             patch("core.risk.risk_manager.RustCommClient", return_value=mock_client), \
             patch("core.execution.transaction_executor.RustCommClient", return_value=mock_client), \
             patch("core.execution.transaction_executor.httpx.AsyncClient", return_value=mock_http_client):
            # Create output directory
            output_dir = tmp_path / "output"
            output_dir.mkdir()
            
            # Create components
            strategy_runner = StrategyRunner(config)
            risk_manager = RiskManager(config)
            transaction_executor = TransactionExecutor(config)
            
            # Start components
            await strategy_runner.start()
            await risk_manager.start()
            await transaction_executor.start()
            
            # Create strategies
            momentum_strategy = MomentumStrategy(config["strategies"][0])
            order_book_imbalance_strategy = OrderBookImbalanceStrategy(config["strategies"][1])
            
            # Generate signals
            momentum_signals = momentum_strategy.generate_signals(market_data)
            order_book_imbalance_signals = order_book_imbalance_strategy.generate_signals(market_data)
            
            # Simulate receiving signals
            await strategy_runner._handle_signal_update({
                "data": {
                    "strategy": "momentum",
                    "signals": momentum_signals
                }
            })
            
            await strategy_runner._handle_signal_update({
                "data": {
                    "strategy": "order_book_imbalance",
                    "signals": order_book_imbalance_signals
                }
            })
            
            # Process signals
            await strategy_runner._process_signals()
            
            # Simulate publishing trade signals
            for market, trade_signal in strategy_runner.trade_signals.items():
                await mock_client.publish(f"trade_signals/{market}", {
                    "market": market,
                    "trade_signal": trade_signal,
                    "timestamp": trade_signal["timestamp"]
                })
            
            # Simulate receiving trade signals in risk manager
            for market, trade_signal in strategy_runner.trade_signals.items():
                await risk_manager._handle_trade_signal_update({
                    "data": {
                        "market": market,
                        "trade_signal": trade_signal
                    }
                })
            
            # Process trade signals
            await risk_manager._process_trade_signals()
            
            # Simulate publishing trade orders
            for market, trade_order in risk_manager.trade_orders.items():
                await mock_client.publish(f"trade_orders/{market}", {
                    "market": market,
                    "trade_order": trade_order,
                    "timestamp": trade_order["timestamp"]
                })
            
            # Create prepared transactions
            prepared_transactions = {}
            for market, trade_order in risk_manager.trade_orders.items():
                prepared_transactions[market] = {
                    "action": trade_order["action"],
                    "size": trade_order["size"],
                    "price": trade_order["price"],
                    "type": trade_order["type"],
                    "serialized_transaction": "test_serialized_transaction",
                    "timestamp": trade_order["timestamp"]
                }
            
            # Simulate receiving prepared transactions in transaction executor
            for market, prepared_transaction in prepared_transactions.items():
                await transaction_executor._handle_prepared_transaction_update({
                    "data": {
                        "market": market,
                        "transaction": prepared_transaction
                    }
                })
            
            # Execute prepared transactions
            await transaction_executor._execute_prepared_transactions()
            
            # Check that executed transactions were created
            assert len(transaction_executor.executed_transactions) > 0
            
            # Save results to files
            with open(output_dir / "trade_signals.json", "w") as f:
                json.dump(strategy_runner.trade_signals, f, indent=2)
            
            with open(output_dir / "trade_orders.json", "w") as f:
                json.dump(risk_manager.trade_orders, f, indent=2)
            
            with open(output_dir / "executed_transactions.json", "w") as f:
                json.dump(transaction_executor.executed_transactions, f, indent=2)
            
            # Stop components
            await strategy_runner.stop()
            await risk_manager.stop()
            await transaction_executor.stop()
