#!/usr/bin/env python3
"""
Test module for the Stop Loss Manager.
"""

import unittest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
import sys

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the module to test
from core.risk.stop_loss import StopLossManager

class TestStopLossManager(unittest.TestCase):
    """Test cases for the StopLossManager class."""

    def setUp(self):
        """Set up test fixtures."""
        # Create test configuration
        self.config = {
            "trailing_enabled": True,
            "trailing_activation_pct": 0.01,
            "trailing_distance_pct": 0.02,
            "volatility_multiplier": 2.0,
            "time_based_widening": True,
            "widening_factor": 0.001
        }

        # Initialize stop loss manager
        self.stop_loss_manager = StopLossManager(self.config)

        # Create test data
        self.entry_price = 100.0
        self.entry_time = pd.Timestamp("2023-01-01 12:00:00")
        self.initial_stop_price = 95.0
        self.trade_id = "trade1"

    def test_set_initial_stop(self):
        """Test setting initial stop loss."""
        # Set initial stop
        stop_info = self.stop_loss_manager.set_initial_stop(
            self.trade_id,
            self.entry_price,
            self.entry_time,
            self.initial_stop_price,
            True
        )

        # Check that stop info contains expected keys
        expected_keys = ["trade_id", "entry_price", "entry_time", "initial_stop_price",
                        "current_stop_price", "is_long", "volatility", "highest_price",
                        "lowest_price", "trailing_activated", "stop_updated_time", "stop_type"]
        for key in expected_keys:
            self.assertIn(key, stop_info)

        # Check that stop info is set correctly
        self.assertEqual(stop_info["trade_id"], self.trade_id)
        self.assertEqual(stop_info["entry_price"], self.entry_price)
        self.assertEqual(stop_info["entry_time"], self.entry_time)
        self.assertEqual(stop_info["initial_stop_price"], self.initial_stop_price)
        self.assertEqual(stop_info["current_stop_price"], self.initial_stop_price)
        self.assertEqual(stop_info["is_long"], True)
        self.assertEqual(stop_info["highest_price"], self.entry_price)
        self.assertEqual(stop_info["trailing_activated"], False)
        self.assertEqual(stop_info["stop_type"], "initial")

        # Check that stop is stored in active stops
        self.assertIn(self.trade_id, self.stop_loss_manager.active_stops)

    def test_update_stop_long(self):
        """Test updating stop loss for a long position."""
        # Set initial stop
        self.stop_loss_manager.set_initial_stop(
            self.trade_id,
            self.entry_price,
            self.entry_time,
            self.initial_stop_price,
            True
        )

        # Update stop with price below activation threshold
        current_time = self.entry_time + timedelta(hours=1)
        stop_info = self.stop_loss_manager.update_stop(
            self.trade_id,
            100.5,  # Price up 0.5%, below activation threshold
            current_time
        )

        # Check that trailing stop is not activated
        self.assertFalse(stop_info["trailing_activated"])
        self.assertEqual(stop_info["current_stop_price"], self.initial_stop_price)

        # Update stop with price above activation threshold
        current_time = self.entry_time + timedelta(hours=2)
        stop_info = self.stop_loss_manager.update_stop(
            self.trade_id,
            102.0,  # Price up 2%, above activation threshold
            current_time
        )

        # Check that trailing stop is activated
        self.assertTrue(stop_info["trailing_activated"])
        self.assertEqual(stop_info["stop_type"], "trailing")

        # Calculate expected stop price
        expected_stop_price = 102.0 * (1 - self.config["trailing_distance_pct"])

        # Check that stop price is updated
        self.assertAlmostEqual(stop_info["current_stop_price"], expected_stop_price)

        # Update stop with higher price
        current_time = self.entry_time + timedelta(hours=3)
        stop_info = self.stop_loss_manager.update_stop(
            self.trade_id,
            105.0,
            current_time
        )

        # Calculate expected stop price
        expected_stop_price = 105.0 * (1 - self.config["trailing_distance_pct"])

        # Check that stop price is updated
        self.assertAlmostEqual(stop_info["current_stop_price"], expected_stop_price)

        # Update stop with lower price (stop should not move down)
        current_time = self.entry_time + timedelta(hours=4)
        previous_stop_price = stop_info["current_stop_price"]
        stop_info = self.stop_loss_manager.update_stop(
            self.trade_id,
            103.0,
            current_time
        )

        # Check that stop price is not updated
        self.assertAlmostEqual(stop_info["current_stop_price"], previous_stop_price)

    def test_update_stop_short(self):
        """Test updating stop loss for a short position."""
        # Set initial stop
        self.stop_loss_manager.set_initial_stop(
            self.trade_id,
            self.entry_price,
            self.entry_time,
            105.0,  # Initial stop above entry price for short
            False
        )

        # Update stop with price above activation threshold
        current_time = self.entry_time + timedelta(hours=1)
        stop_info = self.stop_loss_manager.update_stop(
            self.trade_id,
            99.5,  # Price down 0.5%, below activation threshold
            current_time
        )

        # Check that trailing stop is not activated
        self.assertFalse(stop_info["trailing_activated"])
        self.assertEqual(stop_info["current_stop_price"], 105.0)

        # Update stop with price below activation threshold
        current_time = self.entry_time + timedelta(hours=2)
        stop_info = self.stop_loss_manager.update_stop(
            self.trade_id,
            98.0,  # Price down 2%, above activation threshold
            current_time
        )

        # Check that trailing stop is activated
        self.assertTrue(stop_info["trailing_activated"])
        self.assertEqual(stop_info["stop_type"], "trailing")

        # Calculate expected stop price
        expected_stop_price = 98.0 * (1 + self.config["trailing_distance_pct"])

        # Check that stop price is updated
        self.assertAlmostEqual(stop_info["current_stop_price"], expected_stop_price)

        # Update stop with lower price
        current_time = self.entry_time + timedelta(hours=3)
        stop_info = self.stop_loss_manager.update_stop(
            self.trade_id,
            95.0,
            current_time
        )

        # Calculate expected stop price
        expected_stop_price = 95.0 * (1 + self.config["trailing_distance_pct"])

        # Check that stop price is updated
        self.assertAlmostEqual(stop_info["current_stop_price"], expected_stop_price)

        # Update stop with higher price (stop should not move up)
        current_time = self.entry_time + timedelta(hours=4)
        previous_stop_price = stop_info["current_stop_price"]
        stop_info = self.stop_loss_manager.update_stop(
            self.trade_id,
            97.0,
            current_time
        )

        # Check that stop price is not updated
        self.assertAlmostEqual(stop_info["current_stop_price"], previous_stop_price)

    def test_time_based_widening(self):
        """Test time-based widening of stop loss."""
        # Create a test configuration with time-based widening enabled
        config = {
            "trailing_enabled": True,
            "trailing_activation_pct": 0.01,
            "trailing_distance_pct": 0.02,
            "volatility_multiplier": 2.0,
            "time_based_widening": True,
            "widening_factor": 0.001
        }

        # Initialize stop loss manager with the test configuration
        stop_loss_manager = StopLossManager(config)

        # Set initial stop
        stop_loss_manager.set_initial_stop(
            self.trade_id,
            self.entry_price,
            self.entry_time,
            self.initial_stop_price,
            True
        )

        # Update stop after 10 hours
        current_time = self.entry_time + timedelta(hours=10)
        stop_info = stop_loss_manager.update_stop(
            self.trade_id,
            self.entry_price,  # Price unchanged
            current_time
        )

        # Calculate expected widening amount
        hours_elapsed = 10
        widening_amount = self.entry_price * config["widening_factor"] * hours_elapsed
        expected_stop_price = self.initial_stop_price - widening_amount

        # Print debug information
        print(f"Initial stop price: {self.initial_stop_price}")
        print(f"Widening amount: {widening_amount}")
        print(f"Expected stop price: {expected_stop_price}")
        print(f"Actual stop price: {stop_info['current_stop_price']}")
        print(f"Time-based widening enabled: {config['time_based_widening']}")

        # Check that stop price is widened
        self.assertAlmostEqual(stop_info["current_stop_price"], expected_stop_price, places=1)

    def test_check_stop_triggered(self):
        """Test checking if stop loss is triggered."""
        # Set initial stop
        self.stop_loss_manager.set_initial_stop(
            self.trade_id,
            self.entry_price,
            self.entry_time,
            self.initial_stop_price,
            True
        )

        # Check if stop is triggered with price above stop
        triggered = self.stop_loss_manager.check_stop_triggered(
            self.trade_id,
            98.0  # Price above stop
        )

        # Check that stop is not triggered
        self.assertFalse(triggered)

        # Check if stop is triggered with price below stop
        triggered = self.stop_loss_manager.check_stop_triggered(
            self.trade_id,
            94.0  # Price below stop
        )

        # Check that stop is triggered
        self.assertTrue(triggered)

    def test_remove_stop(self):
        """Test removing a stop loss."""
        # Set initial stop
        self.stop_loss_manager.set_initial_stop(
            self.trade_id,
            self.entry_price,
            self.entry_time,
            self.initial_stop_price,
            True
        )

        # Remove stop
        self.stop_loss_manager.remove_stop(self.trade_id)

        # Check that stop is removed
        self.assertNotIn(self.trade_id, self.stop_loss_manager.active_stops)

    def test_get_all_stops(self):
        """Test getting all stop losses."""
        # Set initial stops
        self.stop_loss_manager.set_initial_stop(
            "trade1",
            100.0,
            self.entry_time,
            95.0,
            True
        )
        self.stop_loss_manager.set_initial_stop(
            "trade2",
            200.0,
            self.entry_time,
            210.0,
            False
        )

        # Get all stops
        all_stops = self.stop_loss_manager.get_all_stops()

        # Check that all stops are returned
        self.assertEqual(len(all_stops), 2)
        self.assertIn("trade1", all_stops)
        self.assertIn("trade2", all_stops)

    def test_get_stop_info(self):
        """Test getting stop loss information."""
        # Set initial stop
        self.stop_loss_manager.set_initial_stop(
            self.trade_id,
            self.entry_price,
            self.entry_time,
            self.initial_stop_price,
            True
        )

        # Get stop info
        stop_info = self.stop_loss_manager.get_stop_info(self.trade_id)

        # Check that stop info is returned
        self.assertIsNotNone(stop_info)
        self.assertEqual(stop_info["trade_id"], self.trade_id)

if __name__ == "__main__":
    unittest.main()
