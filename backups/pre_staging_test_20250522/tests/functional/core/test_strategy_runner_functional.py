"""
Functional tests for the strategy runner.
"""

import pytest
import asyncio
import json
import os
from pathlib import Path
from unittest.mock import MagicMock, patch
from core.engine.strategy_runner import StrategyRunner
from core.strategies.momentum import MomentumStrategy
from core.strategies.order_book_imbalance import OrderBookImbalanceStrategy

@pytest.mark.functional
class TestStrategyRunnerFunctional:
    """Functional tests for the strategy runner."""
    
    @pytest.fixture
    def config(self):
        """Create a test configuration."""
        return {
            "strategies": [
                {
                    "name": "momentum",
                    "markets": ["SOL-USDC", "JTO-USDC"],
                    "weight": 0.6,
                    "parameters": {
                        "window_size": 20,
                        "threshold": 0.01,
                        "max_value": 0.05,
                        "smoothing_factor": 0.1,
                        "max_position_size": 1.0
                    }
                },
                {
                    "name": "order_book_imbalance",
                    "markets": ["SOL-USDC", "JTO-USDC"],
                    "weight": 0.4,
                    "parameters": {
                        "window_size": 20,
                        "threshold": 0.1,
                        "max_value": 0.5,
                        "depth": 10,
                        "smoothing_factor": 0.2,
                        "max_position_size": 1.0
                    }
                }
            ],
            "strategy_runner": {
                "update_interval_ms": 1000,
                "publish_interval_ms": 1000
            }
        }
    
    @pytest.fixture
    def mock_client(self):
        """Create a mock RustCommClient."""
        mock = MagicMock()
        mock.connect = MagicMock(return_value=asyncio.Future())
        mock.connect.return_value.set_result(None)
        mock.disconnect = MagicMock(return_value=asyncio.Future())
        mock.disconnect.return_value.set_result(None)
        mock.subscribe = MagicMock(return_value=asyncio.Future())
        mock.subscribe.return_value.set_result(None)
        mock.publish = MagicMock(return_value=asyncio.Future())
        mock.publish.return_value.set_result(None)
        return mock
    
    @pytest.fixture
    def market_data(self):
        """Create test market data."""
        return {
            "order_books": {
                "SOL-USDC": {
                    "bids": [
                        {"price": 100.0, "size": 1.0},
                        {"price": 99.0, "size": 2.0}
                    ],
                    "asks": [
                        {"price": 101.0, "size": 1.0},
                        {"price": 102.0, "size": 2.0}
                    ],
                    "metrics": {
                        "mid_price": 100.5,
                        "spread": 1.0,
                        "spread_pct": 0.01,
                        "bid_ask_imbalance": 0.5
                    }
                },
                "JTO-USDC": {
                    "bids": [
                        {"price": 10.0, "size": 1.0},
                        {"price": 9.9, "size": 2.0}
                    ],
                    "asks": [
                        {"price": 10.1, "size": 1.0},
                        {"price": 10.2, "size": 2.0}
                    ],
                    "metrics": {
                        "mid_price": 10.05,
                        "spread": 0.1,
                        "spread_pct": 0.01,
                        "bid_ask_imbalance": 0.5
                    }
                }
            }
        }
    
    @pytest.mark.asyncio
    async def test_strategy_runner_with_real_strategies(self, config, mock_client, market_data):
        """Test the strategy runner with real strategies."""
        with patch("core.engine.strategy_runner.RustCommClient", return_value=mock_client):
            # Create a strategy runner
            strategy_runner = StrategyRunner(config)
            
            # Start the strategy runner
            await strategy_runner.start()
            
            # Create strategies
            momentum_strategy = MomentumStrategy(config["strategies"][0])
            order_book_imbalance_strategy = OrderBookImbalanceStrategy(config["strategies"][1])
            
            # Generate signals
            momentum_signals = momentum_strategy.generate_signals(market_data)
            order_book_imbalance_signals = order_book_imbalance_strategy.generate_signals(market_data)
            
            # Simulate receiving signals
            await strategy_runner._handle_signal_update({
                "data": {
                    "strategy": "momentum",
                    "signals": momentum_signals
                }
            })
            
            await strategy_runner._handle_signal_update({
                "data": {
                    "strategy": "order_book_imbalance",
                    "signals": order_book_imbalance_signals
                }
            })
            
            # Process signals
            await strategy_runner._process_signals()
            
            # Check that trade signals were generated
            assert "SOL-USDC" in strategy_runner.trade_signals
            assert "JTO-USDC" in strategy_runner.trade_signals
            
            # Check that the trade signals are correct
            for market in ["SOL-USDC", "JTO-USDC"]:
                trade_signal = strategy_runner.trade_signals[market]
                assert trade_signal["market"] == market
                assert "action" in trade_signal
                assert "position_size" in trade_signal
                assert "confidence" in trade_signal
                assert "signals" in trade_signal
                assert "momentum" in trade_signal["signals"]
                assert "order_book_imbalance" in trade_signal["signals"]
                assert "timestamp" in trade_signal
            
            # Stop the strategy runner
            await strategy_runner.stop()
    
    @pytest.mark.asyncio
    async def test_strategy_runner_saves_trade_signals(self, config, mock_client, market_data, tmp_path):
        """Test that the strategy runner saves trade signals to a file."""
        with patch("core.engine.strategy_runner.RustCommClient", return_value=mock_client):
            # Create a strategy runner
            strategy_runner = StrategyRunner(config)
            
            # Set output directory
            output_dir = tmp_path / "output"
            output_dir.mkdir()
            
            # Start the strategy runner
            await strategy_runner.start()
            
            # Create strategies
            momentum_strategy = MomentumStrategy(config["strategies"][0])
            order_book_imbalance_strategy = OrderBookImbalanceStrategy(config["strategies"][1])
            
            # Generate signals
            momentum_signals = momentum_strategy.generate_signals(market_data)
            order_book_imbalance_signals = order_book_imbalance_strategy.generate_signals(market_data)
            
            # Simulate receiving signals
            await strategy_runner._handle_signal_update({
                "data": {
                    "strategy": "momentum",
                    "signals": momentum_signals
                }
            })
            
            await strategy_runner._handle_signal_update({
                "data": {
                    "strategy": "order_book_imbalance",
                    "signals": order_book_imbalance_signals
                }
            })
            
            # Process signals
            await strategy_runner._process_signals()
            
            # Save trade signals
            trade_signals_file = output_dir / "trade_signals.json"
            with open(trade_signals_file, "w") as f:
                json.dump(strategy_runner.trade_signals, f, indent=2)
            
            # Check that the trade signals file exists
            assert trade_signals_file.exists()
            
            # Load trade signals
            with open(trade_signals_file, "r") as f:
                loaded_trade_signals = json.load(f)
            
            # Check that the loaded trade signals are correct
            assert "SOL-USDC" in loaded_trade_signals
            assert "JTO-USDC" in loaded_trade_signals
            
            # Stop the strategy runner
            await strategy_runner.stop()
