"""
Performance tests for the strategy runner.
"""

import pytest
import asyncio
import time
from unittest.mock import MagicMock, patch
from core.engine.strategy_runner import StrategyRunner
from core.strategies.momentum import MomentumStrategy
from core.strategies.order_book_imbalance import OrderBookImbalanceStrategy

@pytest.mark.performance
class TestStrategyRunnerPerformance:
    """Performance tests for the strategy runner."""
    
    @pytest.fixture
    def config(self):
        """Create a test configuration."""
        return {
            "strategies": [
                {
                    "name": "momentum",
                    "markets": ["SOL-USDC", "JTO-USDC", "BTC-USDC", "ETH-USDC", "BONK-USDC",
                               "RNDR-USDC", "PYTH-USDC", "MSOL-USDC", "RAY-USDC", "SRM-USDC"],
                    "weight": 0.6,
                    "parameters": {
                        "window_size": 20,
                        "threshold": 0.01,
                        "max_value": 0.05,
                        "smoothing_factor": 0.1,
                        "max_position_size": 1.0
                    }
                },
                {
                    "name": "order_book_imbalance",
                    "markets": ["SOL-USDC", "JTO-USDC", "BTC-USDC", "ETH-USDC", "BONK-USDC",
                               "RNDR-USDC", "PYTH-USDC", "MSOL-USDC", "RAY-USDC", "SRM-USDC"],
                    "weight": 0.4,
                    "parameters": {
                        "window_size": 20,
                        "threshold": 0.1,
                        "max_value": 0.5,
                        "depth": 10,
                        "smoothing_factor": 0.2,
                        "max_position_size": 1.0
                    }
                }
            ],
            "strategy_runner": {
                "update_interval_ms": 1000,
                "publish_interval_ms": 1000
            }
        }
    
    @pytest.fixture
    def mock_client(self):
        """Create a mock RustCommClient."""
        mock = MagicMock()
        mock.connect = MagicMock(return_value=asyncio.Future())
        mock.connect.return_value.set_result(None)
        mock.disconnect = MagicMock(return_value=asyncio.Future())
        mock.disconnect.return_value.set_result(None)
        mock.subscribe = MagicMock(return_value=asyncio.Future())
        mock.subscribe.return_value.set_result(None)
        mock.publish = MagicMock(return_value=asyncio.Future())
        mock.publish.return_value.set_result(None)
        return mock
    
    @pytest.fixture
    def market_data(self):
        """Create test market data."""
        markets = ["SOL-USDC", "JTO-USDC", "BTC-USDC", "ETH-USDC", "BONK-USDC",
                  "RNDR-USDC", "PYTH-USDC", "MSOL-USDC", "RAY-USDC", "SRM-USDC"]
        
        order_books = {}
        for market in markets:
            order_books[market] = {
                "bids": [
                    {"price": 100.0, "size": 1.0},
                    {"price": 99.0, "size": 2.0}
                ],
                "asks": [
                    {"price": 101.0, "size": 1.0},
                    {"price": 102.0, "size": 2.0}
                ],
                "metrics": {
                    "mid_price": 100.5,
                    "spread": 1.0,
                    "spread_pct": 0.01,
                    "bid_ask_imbalance": 0.5
                }
            }
        
        return {
            "order_books": order_books
        }
    
    @pytest.mark.asyncio
    async def test_strategy_runner_performance(self, config, mock_client, market_data):
        """Test the performance of the strategy runner."""
        with patch("core.engine.strategy_runner.RustCommClient", return_value=mock_client):
            # Create a strategy runner
            strategy_runner = StrategyRunner(config)
            
            # Start the strategy runner
            await strategy_runner.start()
            
            # Create strategies
            momentum_strategy = MomentumStrategy(config["strategies"][0])
            order_book_imbalance_strategy = OrderBookImbalanceStrategy(config["strategies"][1])
            
            # Measure signal generation time
            start_time = time.time()
            
            # Generate signals
            momentum_signals = momentum_strategy.generate_signals(market_data)
            order_book_imbalance_signals = order_book_imbalance_strategy.generate_signals(market_data)
            
            # Calculate signal generation time
            signal_generation_time = time.time() - start_time
            
            # Simulate receiving signals
            await strategy_runner._handle_signal_update({
                "data": {
                    "strategy": "momentum",
                    "signals": momentum_signals
                }
            })
            
            await strategy_runner._handle_signal_update({
                "data": {
                    "strategy": "order_book_imbalance",
                    "signals": order_book_imbalance_signals
                }
            })
            
            # Measure signal processing time
            start_time = time.time()
            
            # Process signals
            await strategy_runner._process_signals()
            
            # Calculate signal processing time
            signal_processing_time = time.time() - start_time
            
            # Check that trade signals were generated
            assert len(strategy_runner.trade_signals) == 10
            
            # Print performance metrics
            print(f"Signal generation time: {signal_generation_time:.6f} seconds")
            print(f"Signal processing time: {signal_processing_time:.6f} seconds")
            
            # Check that the performance is acceptable
            assert signal_generation_time < 1.0, "Signal generation is too slow"
            assert signal_processing_time < 1.0, "Signal processing is too slow"
            
            # Stop the strategy runner
            await strategy_runner.stop()
    
    @pytest.mark.asyncio
    async def test_strategy_runner_performance_with_many_iterations(self, config, mock_client, market_data):
        """Test the performance of the strategy runner with many iterations."""
        with patch("core.engine.strategy_runner.RustCommClient", return_value=mock_client):
            # Create a strategy runner
            strategy_runner = StrategyRunner(config)
            
            # Start the strategy runner
            await strategy_runner.start()
            
            # Create strategies
            momentum_strategy = MomentumStrategy(config["strategies"][0])
            order_book_imbalance_strategy = OrderBookImbalanceStrategy(config["strategies"][1])
            
            # Number of iterations
            num_iterations = 100
            
            # Measure total time
            start_time = time.time()
            
            for i in range(num_iterations):
                # Generate signals
                momentum_signals = momentum_strategy.generate_signals(market_data)
                order_book_imbalance_signals = order_book_imbalance_strategy.generate_signals(market_data)
                
                # Simulate receiving signals
                await strategy_runner._handle_signal_update({
                    "data": {
                        "strategy": "momentum",
                        "signals": momentum_signals
                    }
                })
                
                await strategy_runner._handle_signal_update({
                    "data": {
                        "strategy": "order_book_imbalance",
                        "signals": order_book_imbalance_signals
                    }
                })
                
                # Process signals
                await strategy_runner._process_signals()
            
            # Calculate total time
            total_time = time.time() - start_time
            
            # Calculate average time per iteration
            avg_time_per_iteration = total_time / num_iterations
            
            # Print performance metrics
            print(f"Total time for {num_iterations} iterations: {total_time:.6f} seconds")
            print(f"Average time per iteration: {avg_time_per_iteration:.6f} seconds")
            
            # Check that the performance is acceptable
            assert avg_time_per_iteration < 0.1, "Average time per iteration is too slow"
            
            # Stop the strategy runner
            await strategy_runner.stop()
