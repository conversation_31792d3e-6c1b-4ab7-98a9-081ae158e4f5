"""
Integration tests for the strategy runner and risk manager.
"""

import pytest
import asyncio
from unittest.mock import MagicMock, patch
from core.engine.strategy_runner import StrategyRunner
from core.risk.risk_manager import RiskManager

@pytest.mark.integration
class TestStrategyRiskIntegration:
    """Integration tests for the strategy runner and risk manager."""

    @pytest.fixture
    def config(self):
        """Create a test configuration."""
        return {
            "strategies": [
                {
                    "name": "momentum",
                    "markets": ["SOL-USDC", "JTO-USDC"],
                    "weight": 0.6
                },
                {
                    "name": "order_book_imbalance",
                    "markets": ["SOL-USDC", "JTO-USDC"],
                    "weight": 0.4
                }
            ],
            "market_microstructure": {
                "markets": ["SOL-USDC", "JTO-USDC"]
            },
            "strategy_runner": {
                "update_interval_ms": 1000,
                "publish_interval_ms": 1000
            },
            "risk_management": {
                "max_position_size": 0.1,
                "max_exposure": 0.5,
                "update_interval_ms": 1000,
                "publish_interval_ms": 1000,
                "metrics_interval_ms": 5000
            }
        }

    @pytest.fixture
    def mock_client(self):
        """Create a mock RustCommClient."""
        mock = MagicMock()
        mock.connect = MagicMock(return_value=asyncio.Future())
        mock.connect.return_value.set_result(None)
        mock.disconnect = MagicMock(return_value=asyncio.Future())
        mock.disconnect.return_value.set_result(None)
        mock.subscribe = MagicMock(return_value=asyncio.Future())
        mock.subscribe.return_value.set_result(None)
        mock.publish = MagicMock(return_value=asyncio.Future())
        mock.publish.return_value.set_result(None)
        return mock

    @pytest.mark.asyncio
    async def test_strategy_risk_integration(self, config, mock_client):
        """Test the integration between the strategy runner and risk manager."""
        with patch("core.engine.strategy_runner.RustCommClient", return_value=mock_client), \
             patch("core.risk.risk_manager.RustCommClient", return_value=mock_client):
            # Create a strategy runner and risk manager
            strategy_runner = StrategyRunner(config)
            risk_manager = RiskManager(config)

            # Start the strategy runner and risk manager
            await strategy_runner.start()
            await risk_manager.start()

            # Add signals to the strategy runner
            strategy_runner.signals = {
                "momentum": {
                    "signals": {
                        "SOL-USDC": 0.5
                    },
                    "confidence": 0.8
                },
                "order_book_imbalance": {
                    "signals": {
                        "SOL-USDC": -0.3
                    },
                    "confidence": 0.6
                }
            }

            # Process signals
            await strategy_runner._process_signals()

            # Manually publish the trade signals
            for market, trade_signal in strategy_runner.trade_signals.items():
                await mock_client.publish(f"trade_signals/{market}", {
                    "market": market,
                    "trade_signal": trade_signal,
                    "timestamp": trade_signal.get("timestamp", "")
                })

            # Check that the trade signals were created
            assert "SOL-USDC" in strategy_runner.trade_signals

            # Simulate receiving the trade signal in the risk manager
            trade_signal = strategy_runner.trade_signals["SOL-USDC"]
            risk_manager.trade_signals["SOL-USDC"] = trade_signal

            # Process trade signals
            await risk_manager._process_trade_signals()

            # Check that the trade orders were created
            assert "SOL-USDC" in risk_manager.trade_orders

            # Check that the trade order is correct
            trade_order = risk_manager.trade_orders["SOL-USDC"]
            assert trade_order["market"] == "SOL-USDC"
            assert trade_order["action"] == "buy"
            assert trade_order["size"] > 0.0
            assert trade_order["type"] == "market"

            # Stop the strategy runner and risk manager
            await strategy_runner.stop()
            await risk_manager.stop()

    @pytest.mark.asyncio
    async def test_strategy_risk_integration_with_existing_position(self, config, mock_client):
        """Test the integration between the strategy runner and risk manager with an existing position."""
        with patch("core.engine.strategy_runner.RustCommClient", return_value=mock_client), \
             patch("core.risk.risk_manager.RustCommClient", return_value=mock_client):
            # Create a strategy runner and risk manager
            strategy_runner = StrategyRunner(config)
            risk_manager = RiskManager(config)

            # Add an existing position
            risk_manager.portfolio["SOL-USDC"] = {
                "position": 0.05,
                "value": 500.0
            }

            # Start the strategy runner and risk manager
            await strategy_runner.start()
            await risk_manager.start()

            # Add signals to the strategy runner
            strategy_runner.signals = {
                "momentum": {
                    "signals": {
                        "SOL-USDC": 0.5
                    },
                    "confidence": 0.8
                },
                "order_book_imbalance": {
                    "signals": {
                        "SOL-USDC": -0.3
                    },
                    "confidence": 0.6
                }
            }

            # Process signals
            await strategy_runner._process_signals()

            # Manually publish the trade signals
            for market, trade_signal in strategy_runner.trade_signals.items():
                await mock_client.publish(f"trade_signals/{market}", {
                    "market": market,
                    "trade_signal": trade_signal,
                    "timestamp": trade_signal.get("timestamp", "")
                })

            # Simulate receiving the trade signal in the risk manager
            trade_signal = strategy_runner.trade_signals["SOL-USDC"]
            risk_manager.trade_signals["SOL-USDC"] = trade_signal

            # Process trade signals
            await risk_manager._process_trade_signals()

            # Check that the trade orders were created
            assert "SOL-USDC" in risk_manager.trade_orders

            # Check that the trade order is correct
            trade_order = risk_manager.trade_orders["SOL-USDC"]
            assert trade_order["market"] == "SOL-USDC"
            assert trade_order["action"] == "buy"
            assert trade_order["size"] > 0.0
            assert trade_order["size"] <= 0.05  # Limited by max_position_size - existing position
            assert trade_order["type"] == "market"

            # Stop the strategy runner and risk manager
            await strategy_runner.stop()
            await risk_manager.stop()
