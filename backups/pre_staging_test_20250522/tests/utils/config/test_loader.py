"""
Tests for the configuration loader.
"""

import os
import pytest
import tempfile
import yaml
from pathlib import Path
from utils.config.loader import (
    load_yaml_file,
    merge_configs,
    get_config_value,
    set_config_value,
    load_config,
    load_environment_config,
    load_component_config
)

# Test data
TEST_CONFIG = {
    "apis": {
        "helius": {
            "api_key": "test_api_key",
            "enabled": True
        }
    },
    "mode": {
        "live_trading": False,
        "paper_trading": True
    }
}

TEST_OVERRIDE = {
    "apis": {
        "helius": {
            "enabled": False
        },
        "birdeye": {
            "api_key": "birdeye_key"
        }
    }
}

def test_load_yaml_file():
    """Test loading a YAML file."""
    # Create a temporary YAML file
    with tempfile.NamedTemporaryFile(suffix=".yaml", mode="w+") as f:
        yaml.dump(TEST_CONFIG, f)
        f.flush()
        
        # Load the file
        config = load_yaml_file(f.name)
        
        # Check that the config was loaded correctly
        assert config is not None
        assert config["apis"]["helius"]["api_key"] == "test_api_key"
        assert config["mode"]["live_trading"] is False
        assert config["mode"]["paper_trading"] is True

def test_merge_configs():
    """Test merging configuration dictionaries."""
    # Merge the test configs
    merged = merge_configs(TEST_CONFIG, TEST_OVERRIDE)
    
    # Check that the configs were merged correctly
    assert merged["apis"]["helius"]["api_key"] == "test_api_key"
    assert merged["apis"]["helius"]["enabled"] is False
    assert merged["apis"]["birdeye"]["api_key"] == "birdeye_key"
    assert merged["mode"]["live_trading"] is False
    assert merged["mode"]["paper_trading"] is True

def test_get_config_value():
    """Test getting a value from the configuration."""
    # Get values from the test config
    api_key = get_config_value(TEST_CONFIG, "apis.helius.api_key")
    enabled = get_config_value(TEST_CONFIG, "apis.helius.enabled")
    live_trading = get_config_value(TEST_CONFIG, "mode.live_trading")
    
    # Check that the values were retrieved correctly
    assert api_key == "test_api_key"
    assert enabled is True
    assert live_trading is False
    
    # Test getting a non-existent value
    non_existent = get_config_value(TEST_CONFIG, "apis.birdeye.api_key", default="default_value")
    assert non_existent == "default_value"

def test_set_config_value():
    """Test setting a value in the configuration."""
    # Create a copy of the test config
    config = TEST_CONFIG.copy()
    
    # Set values in the config
    config = set_config_value(config, "apis.helius.api_key", "new_api_key")
    config = set_config_value(config, "apis.birdeye.api_key", "birdeye_key")
    config = set_config_value(config, "mode.live_trading", True)
    
    # Check that the values were set correctly
    assert config["apis"]["helius"]["api_key"] == "new_api_key"
    assert config["apis"]["birdeye"]["api_key"] == "birdeye_key"
    assert config["mode"]["live_trading"] is True

def test_load_config():
    """Test loading configuration from files."""
    # This test requires the configuration files to be in place
    # It's more of an integration test than a unit test
    config = load_config()
    
    # Check that the config was loaded
    assert config is not None
    
    # Check that the config contains expected keys
    assert "mode" in config
    assert "apis" in config
