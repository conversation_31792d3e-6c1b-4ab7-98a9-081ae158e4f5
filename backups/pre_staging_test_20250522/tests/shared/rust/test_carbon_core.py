"""
Tests for the Carbon Core manager.
"""

import os
import yaml
import pytest
import tempfile
import asyncio
from shared.rust.carbon_core import CarbonCoreManager

# Test configuration loading
def test_carbon_core_manager_config_loading():
    """Test that CarbonCoreManager loads configuration correctly."""
    # Create a temporary configuration file
    with tempfile.NamedTemporaryFile(suffix=".yaml", mode="w+") as f:
        # Write test configuration
        yaml.dump({
            "core": {
                "enabled": True,
                "binary_path": "/path/to/carbon_core",
                "log_level": "info"
            }
        }, f)
        f.flush()
        
        # Create a CarbonCoreManager with the test configuration
        manager = CarbonCoreManager(config_path=f.name)
        
        # Check that the configuration was loaded correctly
        assert manager.config["core"]["enabled"] is True
        assert manager.config["core"]["binary_path"] == "/path/to/carbon_core"
        assert manager.config["core"]["log_level"] == "info"

# Test fallback configuration
def test_carbon_core_manager_fallback_config():
    """Test that CarbonCoreManager falls back to fallback configuration."""
    # Create a temporary fallback configuration file
    with tempfile.NamedTemporaryFile(suffix=".yaml", mode="w+") as f:
        # Write test fallback configuration
        yaml.dump({
            "core": {
                "enabled": True,
                "binary_path": "/path/to/fallback_carbon_core",
                "log_level": "debug"
            }
        }, f)
        f.flush()
        
        # Set environment variable for fallback configuration
        os.environ["CARBON_CORE_FALLBACK_CONFIG_PATH"] = f.name
        
        # Create a CarbonCoreManager with a non-existent configuration file
        manager = CarbonCoreManager(config_path="non_existent_config.yaml")
        
        # Check that the fallback configuration was loaded correctly
        assert manager.using_fallback is True
        assert manager.config["core"]["enabled"] is True
        assert manager.config["core"]["binary_path"] == "/path/to/fallback_carbon_core"
        assert manager.config["core"]["log_level"] == "debug"
        
        # Clean up
        del os.environ["CARBON_CORE_FALLBACK_CONFIG_PATH"]

# Test starting and stopping
@pytest.mark.asyncio
async def test_carbon_core_manager_start_stop():
    """Test that CarbonCoreManager starts and stops correctly."""
    # Create a mock binary file
    with tempfile.NamedTemporaryFile(mode="w+", delete=False) as f:
        # Make the file executable
        os.chmod(f.name, 0o755)
        
        # Write a simple script that sleeps for a while
        f.write("#!/bin/sh\nsleep 10\n")
        f.flush()
        
        # Create a CarbonCoreManager with the mock binary
        manager = CarbonCoreManager(binary_path=f.name)
        
        # Start the Carbon Core process
        result = await manager.start()
        
        # Check that the process was started successfully
        assert result is True
        assert manager.process is not None
        assert manager.process.poll() is None
        
        # Check that the process is healthy
        assert manager.is_healthy() is True
        
        # Stop the Carbon Core process
        result = await manager.stop()
        
        # Check that the process was stopped successfully
        assert result is True
        assert manager.process is None
        
        # Clean up
        os.unlink(f.name)

# Test metrics
@pytest.mark.asyncio
async def test_carbon_core_manager_metrics():
    """Test that CarbonCoreManager returns metrics correctly."""
    # Create a mock binary file
    with tempfile.NamedTemporaryFile(mode="w+", delete=False) as f:
        # Make the file executable
        os.chmod(f.name, 0o755)
        
        # Write a simple script that sleeps for a while
        f.write("#!/bin/sh\nsleep 10\n")
        f.flush()
        
        # Create a CarbonCoreManager with the mock binary
        manager = CarbonCoreManager(binary_path=f.name)
        
        # Start the Carbon Core process
        result = await manager.start()
        
        # Check that the process was started successfully
        assert result is True
        
        # Get metrics
        metrics = await manager.get_metrics()
        
        # Check that metrics were returned
        assert metrics["status"] == "running"
        assert metrics["using_fallback"] is False
        assert "market_microstructure" in metrics
        assert "statistical_signals" in metrics
        
        # Stop the Carbon Core process
        await manager.stop()
        
        # Clean up
        os.unlink(f.name)

# Test error handling
@pytest.mark.asyncio
async def test_carbon_core_manager_error_handling():
    """Test that CarbonCoreManager handles errors correctly."""
    # Create a CarbonCoreManager with a non-existent binary
    manager = CarbonCoreManager(binary_path="non_existent_binary")
    
    # Try to start the Carbon Core process
    result = await manager.start()
    
    # Check that the process was not started successfully
    assert result is False
    assert manager.process is None
    
    # Check that the process is not healthy
    assert manager.is_healthy() is False
    
    # Get metrics
    metrics = await manager.get_metrics()
    
    # Check that metrics reflect the error
    assert metrics["status"] == "not_running"
