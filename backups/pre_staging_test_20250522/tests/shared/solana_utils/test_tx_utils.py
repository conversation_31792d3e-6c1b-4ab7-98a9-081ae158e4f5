"""
Tests for the Solana transaction utilities.
"""

import os
import json
import base64
import pytest
import tempfile
from shared.solana_utils.tx_utils import NATIVE_EXTENSION_AVAILABLE

# Skip tests if native extension is not available
if not NATIVE_EXTENSION_AVAILABLE:
    from shared.solana_utils.fallback import (
        encode_base58,
        decode_base58,
        encode_base64,
        decode_base64,
        sha256,
        keccak256,
        generate_random_bytes,
        create_keypair,
        load_keypair_from_file,
        save_keypair_to_file,
        sign_message,
        verify_signature
    )
else:
    from shared.solana_utils.tx_utils import (
        encode_base58,
        decode_base58,
        encode_base64,
        decode_base64,
        sha256,
        keccak256,
        generate_random_bytes,
        create_keypair,
        load_keypair_from_file,
        save_keypair_to_file,
        sign_message,
        verify_signature
    )

# Test encoding and decoding
def test_base58_encoding():
    """Test Base58 encoding and decoding."""
    # Test encoding
    data = b"Hello, world!"
    encoded = encode_base58(data)
    assert isinstance(encoded, str)
    
    # Test decoding
    decoded = decode_base58(encoded)
    assert isinstance(decoded, bytes)
    assert decoded == data

def test_base64_encoding():
    """Test Base64 encoding and decoding."""
    # Test encoding
    data = b"Hello, world!"
    encoded = encode_base64(data)
    assert isinstance(encoded, str)
    
    # Test decoding
    decoded = decode_base64(encoded)
    assert isinstance(decoded, bytes)
    assert decoded == data
    
    # Test against standard library
    assert encoded == base64.b64encode(data).decode("utf-8")

# Test hashing
def test_sha256():
    """Test SHA-256 hashing."""
    data = b"Hello, world!"
    hashed = sha256(data)
    assert isinstance(hashed, bytes)
    assert len(hashed) == 32  # SHA-256 produces a 32-byte hash
    
    # Test against known hash
    expected = bytes.fromhex("315f5bdb76d078c43b8ac0064e4a0164612b1fce77c869345bfc94c75894edd3")
    assert hashed == expected

def test_keccak256():
    """Test Keccak-256 hashing."""
    data = b"Hello, world!"
    hashed = keccak256(data)
    assert isinstance(hashed, bytes)
    assert len(hashed) == 32  # Keccak-256 produces a 32-byte hash

# Test keypair generation
def test_generate_random_bytes():
    """Test random bytes generation."""
    # Generate random bytes
    random_bytes = generate_random_bytes(32)
    assert isinstance(random_bytes, bytes)
    assert len(random_bytes) == 32
    
    # Generate another set of random bytes
    random_bytes2 = generate_random_bytes(32)
    
    # Check that they are different
    assert random_bytes != random_bytes2

def test_create_keypair():
    """Test keypair creation."""
    # Create a keypair
    keypair = create_keypair()
    
    # Check that the keypair has the expected properties
    assert hasattr(keypair, "public_key")
    assert hasattr(keypair, "secret_key")
    
    # Check that the public key is derived from the secret key
    assert len(keypair.public_key) == 32
    assert len(keypair.secret_key) == 64
    assert keypair.public_key == keypair.secret_key[32:]

# Test keypair file operations
def test_keypair_file_operations():
    """Test keypair file operations."""
    # Create a keypair
    keypair = create_keypair()
    
    # Save the keypair to a file
    with tempfile.NamedTemporaryFile(suffix=".json", delete=False) as f:
        save_keypair_to_file(keypair, f.name)
        
        # Check that the file exists
        assert os.path.exists(f.name)
        
        # Load the keypair from the file
        loaded_keypair = load_keypair_from_file(f.name)
        
        # Check that the loaded keypair matches the original
        assert loaded_keypair.public_key == keypair.public_key
        assert loaded_keypair.secret_key == keypair.secret_key
        
        # Clean up
        os.unlink(f.name)

# Test signing and verification
def test_sign_verify():
    """Test message signing and signature verification."""
    # Create a keypair
    keypair = create_keypair()
    
    # Sign a message
    message = b"Hello, world!"
    signature = sign_message(keypair, message)
    
    # Check that the signature has the expected length
    assert len(signature) == 64
    
    # Verify the signature
    assert verify_signature(keypair.public_key, message, signature) is True
    
    # Verify with a different message
    assert verify_signature(keypair.public_key, b"Different message", signature) is False
    
    # Verify with a different keypair
    keypair2 = create_keypair()
    assert verify_signature(keypair2.public_key, message, signature) is False
