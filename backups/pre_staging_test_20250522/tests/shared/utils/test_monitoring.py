"""
Tests for the shared monitoring service.
"""

import time
import asyncio
import pytest
from datetime import datetime
from shared.utils.monitoring import (
    MetricsTracker,
    MonitoringService,
    get_monitoring_service
)

# Test MetricsTracker
def test_metrics_tracker():
    """Test that MetricsTracker works correctly."""
    tracker = MetricsTracker()
    
    # Test counter
    tracker.increment_counter("test_counter")
    tracker.increment_counter("test_counter")
    tracker.increment_counter("test_counter", {"label": "value"})
    
    # Test gauge
    tracker.set_gauge("test_gauge", 42)
    tracker.set_gauge("test_gauge", 43, {"label": "value"})
    
    # Test histogram
    tracker.observe_histogram("test_histogram", 1.0)
    tracker.observe_histogram("test_histogram", 2.0)
    tracker.observe_histogram("test_histogram", 3.0, {"label": "value"})
    
    # Get metrics
    metrics = tracker.get_metrics()
    
    # Check counters
    assert metrics["counters"]["test_counter"] == 2
    assert metrics["counters"]["test_counter{label=value}"] == 1
    
    # Check gauges
    assert metrics["gauges"]["test_gauge"] == 42
    assert metrics["gauges"]["test_gauge{label=value}"] == 43
    
    # Check histograms
    assert metrics["histograms"]["test_histogram"]["count"] == 2
    assert metrics["histograms"]["test_histogram"]["sum"] == 3.0
    assert metrics["histograms"]["test_histogram"]["min"] == 1.0
    assert metrics["histograms"]["test_histogram"]["max"] == 2.0
    assert metrics["histograms"]["test_histogram"]["avg"] == 1.5
    
    assert metrics["histograms"]["test_histogram{label=value}"]["count"] == 1
    assert metrics["histograms"]["test_histogram{label=value}"]["sum"] == 3.0
    assert metrics["histograms"]["test_histogram{label=value}"]["min"] == 3.0
    assert metrics["histograms"]["test_histogram{label=value}"]["max"] == 3.0
    assert metrics["histograms"]["test_histogram{label=value}"]["avg"] == 3.0
    
    # Reset histograms
    tracker.reset_histograms()
    
    # Check that histograms are reset
    metrics = tracker.get_metrics()
    assert "test_histogram" not in metrics["histograms"]
    assert "test_histogram{label=value}" not in metrics["histograms"]

# Test MonitoringService
def test_monitoring_service():
    """Test that MonitoringService works correctly."""
    monitoring = MonitoringService(health_check_interval=0.1)
    
    # Test component registration
    monitoring.register_component("test_component", lambda: True)
    monitoring.register_component("failing_component", lambda: False)
    
    # Test health checks
    results = monitoring.run_health_checks()
    assert results["test_component"] is True
    assert results["failing_component"] is False
    
    # Test metrics tracking
    monitoring.track_api_request("test_api", "test_endpoint", "success", 0.1)
    monitoring.track_transaction("success", "test_type", 0.2)
    monitoring.track_trading_signal("test_source", "BUY", "SOL-USDC")
    monitoring.update_wallet_balance("test_wallet", 10.0)
    monitoring.update_component_status("test_component", "running")
    monitoring.update_circuit_breaker_status("test_api", "CLOSED")
    
    # Get metrics
    metrics = monitoring.get_metrics()
    
    # Check that metrics were tracked
    assert metrics["counters"]["api_requests{api=test_api,endpoint=test_endpoint,status=success}"] == 1
    assert metrics["counters"]["transactions{status=success,type=test_type,paper_trading=false}"] == 1
    assert metrics["counters"]["trading_signals{source=test_source,action=BUY,market=SOL-USDC}"] == 1
    assert metrics["gauges"]["wallet_balance{wallet=test_wallet}"] == 10.0
    assert metrics["gauges"]["component_status{component=test_component,using_fallback=false}"] == 1
    assert metrics["gauges"]["circuit_breaker{api=test_api,state=CLOSED}"] == 1
    
    # Check that health check results are included
    assert metrics["health"]["test_component"] is True
    assert metrics["health"]["failing_component"] is False

# Test get_monitoring_service
def test_get_monitoring_service():
    """Test that get_monitoring_service returns a singleton instance."""
    monitoring1 = get_monitoring_service()
    monitoring2 = get_monitoring_service()
    
    # Check that both variables reference the same instance
    assert monitoring1 is monitoring2
    
    # Test that the instance works correctly
    monitoring1.track_api_request("test_api", "test_endpoint", "success", 0.1)
    
    # Check that the metrics were tracked in the shared instance
    metrics = monitoring2.get_metrics()
    assert metrics["counters"]["api_requests{api=test_api,endpoint=test_endpoint,status=success}"] == 1

# Test health check thread
def test_health_check_thread():
    """Test that health check thread works correctly."""
    monitoring = MonitoringService(health_check_interval=0.1)
    
    # Register a component that alternates between healthy and unhealthy
    healthy = True
    def check_health():
        nonlocal healthy
        healthy = not healthy
        return healthy
    
    monitoring.register_component("test_component", check_health)
    
    # Start health checks
    monitoring.start_health_checks()
    
    # Wait for a few health check cycles
    time.sleep(0.3)
    
    # Stop health checks
    monitoring.stop_health_checks()
    
    # Check that health checks were run
    assert "test_component" in monitoring.last_health_check
