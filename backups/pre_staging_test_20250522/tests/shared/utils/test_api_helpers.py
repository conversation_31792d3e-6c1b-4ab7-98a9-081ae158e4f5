"""
Tests for the shared API helpers.
"""

import os
import time
import asyncio
import pytest
from datetime import datetime, timedelta
from shared.utils.api_helpers import (
    CircuitBreaker,
    MemoryAPICache,
    PersistentAPICache,
    retry_with_backoff,
    retry_policy
)

# Test CircuitBreaker
async def test_circuit_breaker_success():
    """Test that CircuitBreaker allows successful calls."""
    circuit_breaker = CircuitBreaker("test")
    
    # Define a test function that always succeeds
    async def test_func():
        return "success"
    
    # Call the function through the circuit breaker
    result = await circuit_breaker.call(test_func)
    
    # Check that the result is correct
    assert result == "success"
    
    # Check that the circuit breaker state is still CLOSED
    assert circuit_breaker.state == "CLOSED"
    assert circuit_breaker.failure_count == 0

async def test_circuit_breaker_failure():
    """Test that CircuitBreaker opens after too many failures."""
    circuit_breaker = CircuitBreaker("test", failure_threshold=2)
    
    # Define a test function that always fails
    async def test_func():
        raise Exception("test failure")
    
    # Call the function through the circuit breaker and expect it to fail
    with pytest.raises(Exception):
        await circuit_breaker.call(test_func)
    
    # Check that the circuit breaker state is still CLOSED but failure count increased
    assert circuit_breaker.state == "CLOSED"
    assert circuit_breaker.failure_count == 1
    
    # Call the function again and expect it to fail
    with pytest.raises(Exception):
        await circuit_breaker.call(test_func)
    
    # Check that the circuit breaker state is now OPEN
    assert circuit_breaker.state == "OPEN"
    assert circuit_breaker.failure_count == 2
    
    # Call the function again and expect it to fail fast
    with pytest.raises(Exception) as excinfo:
        await circuit_breaker.call(test_func)
    
    # Check that the exception is from the circuit breaker, not the function
    assert "Circuit test is open" in str(excinfo.value)

async def test_circuit_breaker_recovery():
    """Test that CircuitBreaker recovers after a timeout."""
    circuit_breaker = CircuitBreaker("test", failure_threshold=1, recovery_timeout=0.1)
    
    # Define a test function that fails once then succeeds
    test_func_called = 0
    async def test_func():
        nonlocal test_func_called
        test_func_called += 1
        if test_func_called == 1:
            raise Exception("test failure")
        return "success"
    
    # Call the function through the circuit breaker and expect it to fail
    with pytest.raises(Exception):
        await circuit_breaker.call(test_func)
    
    # Check that the circuit breaker state is now OPEN
    assert circuit_breaker.state == "OPEN"
    
    # Wait for the recovery timeout
    await asyncio.sleep(0.2)
    
    # Call the function again and expect it to succeed
    result = await circuit_breaker.call(test_func)
    
    # Check that the result is correct
    assert result == "success"
    
    # Check that the circuit breaker state is now CLOSED
    assert circuit_breaker.state == "CLOSED"

# Test MemoryAPICache
def test_memory_api_cache():
    """Test that MemoryAPICache works correctly."""
    cache = MemoryAPICache(max_size=2, ttl=0.1)
    
    # Set some values
    cache.set("key1", "value1")
    cache.set("key2", "value2")
    
    # Check that the values are in the cache
    assert cache.get("key1") == "value1"
    assert cache.get("key2") == "value2"
    
    # Set a third value, which should evict the oldest value
    cache.set("key3", "value3")
    
    # Check that key1 is no longer in the cache
    assert cache.get("key1") is None
    assert cache.get("key2") == "value2"
    assert cache.get("key3") == "value3"
    
    # Wait for the TTL to expire
    time.sleep(0.2)
    
    # Check that the values are no longer in the cache
    assert cache.get("key2") is None
    assert cache.get("key3") is None
    
    # Clear the cache
    cache.clear()
    
    # Check that the cache is empty
    assert cache.get("key2") is None
    assert cache.get("key3") is None

# Test PersistentAPICache
def test_persistent_api_cache(tmpdir):
    """Test that PersistentAPICache works correctly."""
    cache_dir = str(tmpdir)
    cache = PersistentAPICache(cache_dir=cache_dir, default_ttl=0.1)
    
    # Set some values
    cache.set("key1", "value1")
    cache.set("key2", {"nested": "value2"})
    
    # Check that the values are in the cache
    assert cache.get("key1") == "value1"
    assert cache.get("key2") == {"nested": "value2"}
    
    # Check that the files were created
    assert os.path.exists(os.path.join(cache_dir, "key1.json"))
    assert os.path.exists(os.path.join(cache_dir, "key2.json"))
    
    # Wait for the TTL to expire
    time.sleep(0.2)
    
    # Check that the values are no longer in the cache
    assert cache.get("key1") is None
    assert cache.get("key2") is None
    
    # Check that the files were removed
    assert not os.path.exists(os.path.join(cache_dir, "key1.json"))
    assert not os.path.exists(os.path.join(cache_dir, "key2.json"))
    
    # Set a value with a custom TTL
    cache.set("key3", "value3", ttl=1.0)
    
    # Check that the value is in the cache
    assert cache.get("key3") == "value3"
    
    # Invalidate the cache entry
    cache.invalidate("key3")
    
    # Check that the value is no longer in the cache
    assert cache.get("key3") is None
    
    # Clear the cache
    cache.clear()
    
    # Check that the cache is empty
    assert cache.get("key1") is None
    assert cache.get("key2") is None
    assert cache.get("key3") is None

# Test retry_with_backoff
async def test_retry_with_backoff():
    """Test that retry_with_backoff works correctly."""
    # Define a test function that fails twice then succeeds
    attempts = 0
    async def test_func():
        nonlocal attempts
        attempts += 1
        if attempts <= 2:
            raise Exception(f"test failure {attempts}")
        return "success"
    
    # Call the function with retry_with_backoff
    result = await retry_with_backoff(test_func, max_retries=3, base_delay=0.01)
    
    # Check that the result is correct
    assert result == "success"
    assert attempts == 3

async def test_retry_with_backoff_max_retries():
    """Test that retry_with_backoff respects max_retries."""
    # Define a test function that always fails
    attempts = 0
    async def test_func():
        nonlocal attempts
        attempts += 1
        raise Exception(f"test failure {attempts}")
    
    # Call the function with retry_with_backoff and expect it to fail
    with pytest.raises(Exception) as excinfo:
        await retry_with_backoff(test_func, max_retries=2, base_delay=0.01)
    
    # Check that the exception is from the last attempt
    assert "test failure 3" in str(excinfo.value)
    assert attempts == 3

# Test retry_policy decorator
async def test_retry_policy_decorator():
    """Test that retry_policy decorator works correctly."""
    # Define a test function that fails twice then succeeds
    attempts = 0
    
    @retry_policy(max_retries=3, base_delay=0.01)
    async def test_func():
        nonlocal attempts
        attempts += 1
        if attempts <= 2:
            raise Exception(f"test failure {attempts}")
        return "success"
    
    # Call the decorated function
    result = await test_func()
    
    # Check that the result is correct
    assert result == "success"
    assert attempts == 3
