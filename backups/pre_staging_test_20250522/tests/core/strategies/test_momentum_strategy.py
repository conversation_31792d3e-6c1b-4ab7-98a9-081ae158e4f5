"""
Tests for the momentum strategy.
"""

import pytest
from core.strategies.momentum import MomentumStrategy

class TestMomentumStrategy:
    """Tests for the MomentumStrategy class."""
    
    def test_initialization(self):
        """Test that the MomentumStrategy initializes correctly."""
        # Create a test configuration
        config = {
            "name": "test_momentum_strategy",
            "markets": ["SOL-USDC", "JTO-USDC"],
            "parameters": {
                "window_size": 20,
                "threshold": 0.01,
                "max_value": 0.05,
                "smoothing_factor": 0.1
            }
        }
        
        # Create a test strategy
        strategy = MomentumStrategy(config)
        
        # Check that the strategy was initialized correctly
        assert strategy.name == "test_momentum_strategy"
        assert strategy.markets == ["SOL-USDC", "JTO-USDC"]
        assert strategy.window_size == 20
        assert strategy.threshold == 0.01
        assert strategy.max_value == 0.05
        assert strategy.smoothing_factor == 0.1
        assert strategy.price_history == {}
    
    def test_calculate_momentum_empty_history(self):
        """Test that _calculate_momentum returns 0.0 for empty price history."""
        # Create a test configuration
        config = {
            "name": "test_momentum_strategy",
            "markets": ["SOL-USDC"],
            "parameters": {
                "window_size": 20,
                "threshold": 0.01,
                "max_value": 0.05,
                "smoothing_factor": 0.1
            }
        }
        
        # Create a test strategy
        strategy = MomentumStrategy(config)
        
        # Calculate momentum
        momentum = strategy._calculate_momentum("SOL-USDC")
        
        # Check that the momentum is 0.0
        assert momentum == 0.0
    
    def test_calculate_momentum_single_price(self):
        """Test that _calculate_momentum returns 0.0 for a single price."""
        # Create a test configuration
        config = {
            "name": "test_momentum_strategy",
            "markets": ["SOL-USDC"],
            "parameters": {
                "window_size": 20,
                "threshold": 0.01,
                "max_value": 0.05,
                "smoothing_factor": 0.1
            }
        }
        
        # Create a test strategy
        strategy = MomentumStrategy(config)
        
        # Add a single price
        strategy.price_history["SOL-USDC"] = [100.0]
        
        # Calculate momentum
        momentum = strategy._calculate_momentum("SOL-USDC")
        
        # Check that the momentum is 0.0
        assert momentum == 0.0
    
    def test_calculate_momentum_multiple_prices(self):
        """Test that _calculate_momentum returns the correct momentum for multiple prices."""
        # Create a test configuration
        config = {
            "name": "test_momentum_strategy",
            "markets": ["SOL-USDC"],
            "parameters": {
                "window_size": 20,
                "threshold": 0.01,
                "max_value": 0.05,
                "smoothing_factor": 0.1
            }
        }
        
        # Create a test strategy
        strategy = MomentumStrategy(config)
        
        # Add multiple prices
        strategy.price_history["SOL-USDC"] = [100.0, 101.0, 102.0, 103.0, 104.0]
        
        # Calculate momentum
        momentum = strategy._calculate_momentum("SOL-USDC")
        
        # Check that the momentum is positive
        assert momentum > 0.0
    
    def test_generate_signals_empty_market_data(self):
        """Test that generate_signals returns empty signals for empty market data."""
        # Create a test configuration
        config = {
            "name": "test_momentum_strategy",
            "markets": ["SOL-USDC"],
            "parameters": {
                "window_size": 20,
                "threshold": 0.01,
                "max_value": 0.05,
                "smoothing_factor": 0.1,
                "max_position_size": 1.0
            }
        }
        
        # Create a test strategy
        strategy = MomentumStrategy(config)
        
        # Generate signals
        signals = strategy.generate_signals({})
        
        # Check that the signals are empty
        assert signals["signals"] == {}
        assert signals["confidence"] == 0.0
        assert signals["position_size"] == 0.0
    
    def test_generate_signals_with_market_data(self):
        """Test that generate_signals returns correct signals for market data."""
        # Create a test configuration
        config = {
            "name": "test_momentum_strategy",
            "markets": ["SOL-USDC"],
            "parameters": {
                "window_size": 20,
                "threshold": 0.01,
                "max_value": 0.05,
                "smoothing_factor": 0.1,
                "max_position_size": 1.0
            }
        }
        
        # Create a test strategy
        strategy = MomentumStrategy(config)
        
        # Create test market data
        market_data = {
            "order_books": {
                "SOL-USDC": {
                    "bids": [
                        {"price": 100.0, "size": 1.0},
                        {"price": 99.0, "size": 2.0}
                    ],
                    "asks": [
                        {"price": 101.0, "size": 1.0},
                        {"price": 102.0, "size": 2.0}
                    ],
                    "metrics": {
                        "mid_price": 100.5,
                        "spread": 1.0,
                        "spread_pct": 0.01,
                        "bid_ask_imbalance": 0.5
                    }
                }
            }
        }
        
        # Generate signals
        signals = strategy.generate_signals(market_data)
        
        # Check that the signals are correct
        assert "SOL-USDC" in signals["signals"]
        assert "confidence" in signals
        assert "position_size" in signals
        assert "timestamp" in signals
    
    def test_generate_signals_with_multiple_markets(self):
        """Test that generate_signals returns correct signals for multiple markets."""
        # Create a test configuration
        config = {
            "name": "test_momentum_strategy",
            "markets": ["SOL-USDC", "JTO-USDC"],
            "parameters": {
                "window_size": 20,
                "threshold": 0.01,
                "max_value": 0.05,
                "smoothing_factor": 0.1,
                "max_position_size": 1.0
            }
        }
        
        # Create a test strategy
        strategy = MomentumStrategy(config)
        
        # Create test market data
        market_data = {
            "order_books": {
                "SOL-USDC": {
                    "bids": [
                        {"price": 100.0, "size": 1.0},
                        {"price": 99.0, "size": 2.0}
                    ],
                    "asks": [
                        {"price": 101.0, "size": 1.0},
                        {"price": 102.0, "size": 2.0}
                    ],
                    "metrics": {
                        "mid_price": 100.5,
                        "spread": 1.0,
                        "spread_pct": 0.01,
                        "bid_ask_imbalance": 0.5
                    }
                },
                "JTO-USDC": {
                    "bids": [
                        {"price": 10.0, "size": 1.0},
                        {"price": 9.9, "size": 2.0}
                    ],
                    "asks": [
                        {"price": 10.1, "size": 1.0},
                        {"price": 10.2, "size": 2.0}
                    ],
                    "metrics": {
                        "mid_price": 10.05,
                        "spread": 0.1,
                        "spread_pct": 0.01,
                        "bid_ask_imbalance": 0.5
                    }
                }
            }
        }
        
        # Generate signals
        signals = strategy.generate_signals(market_data)
        
        # Check that the signals are correct
        assert "SOL-USDC" in signals["signals"]
        assert "JTO-USDC" in signals["signals"]
        assert "confidence" in signals
        assert "position_size" in signals
        assert "timestamp" in signals
