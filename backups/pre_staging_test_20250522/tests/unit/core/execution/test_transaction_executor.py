"""
Tests for the transaction executor.
"""

import pytest
import asyncio
import time
from unittest.mock import MagicMock, patch
from core.execution.transaction_executor import TransactionExecutor, TransactionExecutionError

class TestTransactionExecutor:
    """Tests for the TransactionExecutor class."""
    
    @pytest.fixture
    def config(self):
        """Create a test configuration."""
        return {
            "market_microstructure": {
                "markets": ["SOL-USDC", "JTO-USDC"]
            },
            "transaction_execution": {
                "update_interval_ms": 1000,
                "publish_interval_ms": 1000,
                "simulation_enabled": True,
                "dry_run": True
            },
            "rpc": {
                "endpoint": "https://api.mainnet-beta.solana.com"
            }
        }
    
    @pytest.fixture
    def mock_client(self):
        """Create a mock RustCommClient."""
        mock = MagicMock()
        mock.connect = MagicMock(return_value=asyncio.Future())
        mock.connect.return_value.set_result(None)
        mock.disconnect = MagicMock(return_value=asyncio.Future())
        mock.disconnect.return_value.set_result(None)
        mock.subscribe = MagicMock(return_value=asyncio.Future())
        mock.subscribe.return_value.set_result(None)
        mock.publish = MagicMock(return_value=asyncio.Future())
        mock.publish.return_value.set_result(None)
        return mock
    
    @pytest.fixture
    def mock_http_client(self):
        """Create a mock HTTP client."""
        mock = MagicMock()
        mock.post = MagicMock(return_value=asyncio.Future())
        mock.post.return_value.set_result(MagicMock())
        mock.post.return_value.result().json = MagicMock(return_value={"result": "success"})
        mock.post.return_value.result().raise_for_status = MagicMock()
        mock.aclose = MagicMock(return_value=asyncio.Future())
        mock.aclose.return_value.set_result(None)
        return mock
    
    @pytest.mark.asyncio
    async def test_initialization(self, config, mock_client, mock_http_client):
        """Test that the TransactionExecutor initializes correctly."""
        with patch("core.execution.transaction_executor.RustCommClient", return_value=mock_client), \
             patch("core.execution.transaction_executor.httpx.AsyncClient", return_value=mock_http_client):
            # Create a transaction executor
            transaction_executor = TransactionExecutor(config)
            
            # Check that the transaction executor was initialized correctly
            assert transaction_executor.config == config
            assert transaction_executor.prepared_transactions == {}
            assert transaction_executor.executed_transactions == {}
            assert transaction_executor.running is False
            assert transaction_executor.tasks == []
    
    @pytest.mark.asyncio
    async def test_start_stop(self, config, mock_client, mock_http_client):
        """Test that the TransactionExecutor starts and stops correctly."""
        with patch("core.execution.transaction_executor.RustCommClient", return_value=mock_client), \
             patch("core.execution.transaction_executor.httpx.AsyncClient", return_value=mock_http_client):
            # Create a transaction executor
            transaction_executor = TransactionExecutor(config)
            
            # Start the transaction executor
            await transaction_executor.start()
            
            # Check that the transaction executor was started correctly
            assert transaction_executor.running is True
            assert len(transaction_executor.tasks) == 2
            
            # Check that the client was connected
            mock_client.connect.assert_called_once()
            
            # Check that the client subscribed to prepared transactions
            assert mock_client.subscribe.call_count == 2
            mock_client.subscribe.assert_any_call("prepared_transactions/SOL-USDC", transaction_executor._handle_prepared_transaction_update)
            mock_client.subscribe.assert_any_call("prepared_transactions/JTO-USDC", transaction_executor._handle_prepared_transaction_update)
            
            # Stop the transaction executor
            await transaction_executor.stop()
            
            # Check that the transaction executor was stopped correctly
            assert transaction_executor.running is False
            assert transaction_executor.tasks == []
            
            # Check that the HTTP client was closed
            mock_http_client.aclose.assert_called_once()
            
            # Check that the client was disconnected
            mock_client.disconnect.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_execute_transaction_dry_run(self, config, mock_client, mock_http_client):
        """Test that _execute_transaction executes transactions correctly in dry run mode."""
        with patch("core.execution.transaction_executor.RustCommClient", return_value=mock_client), \
             patch("core.execution.transaction_executor.httpx.AsyncClient", return_value=mock_http_client), \
             patch("core.execution.transaction_executor.time.time", return_value=12345):
            # Create a transaction executor
            transaction_executor = TransactionExecutor(config)
            
            # Create a test prepared transaction
            prepared_transaction = {
                "action": "buy",
                "size": 0.1,
                "price": 100.0,
                "type": "market",
                "serialized_transaction": "test_serialized_transaction"
            }
            
            # Execute transaction
            executed_transaction = await transaction_executor._execute_transaction("SOL-USDC", prepared_transaction)
            
            # Check that the executed transaction is correct
            assert executed_transaction["market"] == "SOL-USDC"
            assert executed_transaction["action"] == "buy"
            assert executed_transaction["size"] == 0.1
            assert executed_transaction["price"] == 100.0
            assert executed_transaction["type"] == "market"
            assert executed_transaction["signature"] == "DRY_RUN_12345"
            assert executed_transaction["status"] == "success"
            assert "timestamp" in executed_transaction
    
    @pytest.mark.asyncio
    async def test_execute_transaction_missing_serialized_transaction(self, config, mock_client, mock_http_client):
        """Test that _execute_transaction raises an error for missing serialized transaction."""
        with patch("core.execution.transaction_executor.RustCommClient", return_value=mock_client), \
             patch("core.execution.transaction_executor.httpx.AsyncClient", return_value=mock_http_client):
            # Create a transaction executor
            transaction_executor = TransactionExecutor(config)
            
            # Create a test prepared transaction without serialized_transaction
            prepared_transaction = {
                "action": "buy",
                "size": 0.1,
                "price": 100.0,
                "type": "market"
            }
            
            # Execute transaction
            executed_transaction = await transaction_executor._execute_transaction("SOL-USDC", prepared_transaction)
            
            # Check that the executed transaction has failed status
            assert executed_transaction["market"] == "SOL-USDC"
            assert executed_transaction["action"] == "buy"
            assert executed_transaction["size"] == 0.1
            assert executed_transaction["price"] == 100.0
            assert executed_transaction["type"] == "market"
            assert executed_transaction["signature"] == ""
            assert executed_transaction["status"] == "failed"
            assert "Missing serialized transaction" in executed_transaction["error"]
            assert "timestamp" in executed_transaction
    
    @pytest.mark.asyncio
    async def test_handle_prepared_transaction_update(self, config, mock_client, mock_http_client):
        """Test that _handle_prepared_transaction_update updates prepared transactions correctly."""
        with patch("core.execution.transaction_executor.RustCommClient", return_value=mock_client), \
             patch("core.execution.transaction_executor.httpx.AsyncClient", return_value=mock_http_client):
            # Create a transaction executor
            transaction_executor = TransactionExecutor(config)
            
            # Create a test message
            message = {
                "data": {
                    "market": "SOL-USDC",
                    "transaction": {
                        "action": "buy",
                        "size": 0.1,
                        "price": 100.0,
                        "type": "market",
                        "serialized_transaction": "test_serialized_transaction"
                    }
                }
            }
            
            # Handle prepared transaction update
            await transaction_executor._handle_prepared_transaction_update(message)
            
            # Check that the prepared transactions were updated
            assert "SOL-USDC" in transaction_executor.prepared_transactions
            assert transaction_executor.prepared_transactions["SOL-USDC"]["action"] == "buy"
            assert transaction_executor.prepared_transactions["SOL-USDC"]["size"] == 0.1
            assert transaction_executor.prepared_transactions["SOL-USDC"]["price"] == 100.0
            assert transaction_executor.prepared_transactions["SOL-USDC"]["type"] == "market"
            assert transaction_executor.prepared_transactions["SOL-USDC"]["serialized_transaction"] == "test_serialized_transaction"
