"""
Tests for the risk manager.
"""

import pytest
import asyncio
from unittest.mock import MagicMock, patch
from core.risk.risk_manager import RiskManager

class TestRiskManager:
    """Tests for the RiskManager class."""
    
    @pytest.fixture
    def config(self):
        """Create a test configuration."""
        return {
            "market_microstructure": {
                "markets": ["SOL-USDC", "JTO-USDC"]
            },
            "risk_management": {
                "max_position_size": 0.1,
                "max_exposure": 0.5,
                "update_interval_ms": 1000,
                "publish_interval_ms": 1000,
                "metrics_interval_ms": 5000
            }
        }
    
    @pytest.fixture
    def mock_client(self):
        """Create a mock RustCommClient."""
        mock = MagicMock()
        mock.connect = MagicMock(return_value=asyncio.Future())
        mock.connect.return_value.set_result(None)
        mock.disconnect = MagicMock(return_value=asyncio.Future())
        mock.disconnect.return_value.set_result(None)
        mock.subscribe = MagicMock(return_value=asyncio.Future())
        mock.subscribe.return_value.set_result(None)
        mock.publish = MagicMock(return_value=asyncio.Future())
        mock.publish.return_value.set_result(None)
        return mock
    
    @pytest.mark.asyncio
    async def test_initialization(self, config, mock_client):
        """Test that the RiskManager initializes correctly."""
        with patch("core.risk.risk_manager.RustCommClient", return_value=mock_client):
            # Create a risk manager
            risk_manager = RiskManager(config)
            
            # Check that the risk manager was initialized correctly
            assert risk_manager.config == config
            assert risk_manager.trade_signals == {}
            assert risk_manager.trade_orders == {}
            assert risk_manager.portfolio == {}
            assert "total_value" in risk_manager.risk_metrics
            assert "total_exposure" in risk_manager.risk_metrics
            assert "max_drawdown" in risk_manager.risk_metrics
            assert "var_95" in risk_manager.risk_metrics
            assert "var_99" in risk_manager.risk_metrics
            assert "sharpe_ratio" in risk_manager.risk_metrics
            assert "volatility" in risk_manager.risk_metrics
            assert "last_update" in risk_manager.risk_metrics
            assert risk_manager.running is False
            assert risk_manager.tasks == []
    
    @pytest.mark.asyncio
    async def test_start_stop(self, config, mock_client):
        """Test that the RiskManager starts and stops correctly."""
        with patch("core.risk.risk_manager.RustCommClient", return_value=mock_client):
            # Create a risk manager
            risk_manager = RiskManager(config)
            
            # Start the risk manager
            await risk_manager.start()
            
            # Check that the risk manager was started correctly
            assert risk_manager.running is True
            assert len(risk_manager.tasks) == 3
            
            # Check that the client was connected
            mock_client.connect.assert_called_once()
            
            # Check that the client subscribed to trade signals
            assert mock_client.subscribe.call_count == 2
            mock_client.subscribe.assert_any_call("trade_signals/SOL-USDC", risk_manager._handle_trade_signal_update)
            mock_client.subscribe.assert_any_call("trade_signals/JTO-USDC", risk_manager._handle_trade_signal_update)
            
            # Stop the risk manager
            await risk_manager.stop()
            
            # Check that the risk manager was stopped correctly
            assert risk_manager.running is False
            assert risk_manager.tasks == []
            
            # Check that the client was disconnected
            mock_client.disconnect.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_apply_risk_management(self, config, mock_client):
        """Test that _apply_risk_management applies risk management correctly."""
        with patch("core.risk.risk_manager.RustCommClient", return_value=mock_client):
            # Create a risk manager
            risk_manager = RiskManager(config)
            
            # Create a test trade signal
            trade_signal = {
                "action": "buy",
                "position_size": 0.2,
                "confidence": 0.8
            }
            
            # Apply risk management
            trade_order = risk_manager._apply_risk_management("SOL-USDC", trade_signal)
            
            # Check that the trade order is correct
            assert trade_order["market"] == "SOL-USDC"
            assert trade_order["action"] == "buy"
            assert trade_order["size"] == 0.1  # Limited by max_position_size
            assert trade_order["price"] == 0.0
            assert trade_order["type"] == "market"
            assert "timestamp" in trade_order
    
    @pytest.mark.asyncio
    async def test_apply_risk_management_with_existing_position(self, config, mock_client):
        """Test that _apply_risk_management applies risk management correctly with an existing position."""
        with patch("core.risk.risk_manager.RustCommClient", return_value=mock_client):
            # Create a risk manager
            risk_manager = RiskManager(config)
            
            # Add an existing position
            risk_manager.portfolio["SOL-USDC"] = {
                "position": 0.05,
                "value": 500.0
            }
            
            # Create a test trade signal
            trade_signal = {
                "action": "buy",
                "position_size": 0.2,
                "confidence": 0.8
            }
            
            # Apply risk management
            trade_order = risk_manager._apply_risk_management("SOL-USDC", trade_signal)
            
            # Check that the trade order is correct
            assert trade_order["market"] == "SOL-USDC"
            assert trade_order["action"] == "buy"
            assert trade_order["size"] == 0.05  # Limited by max_position_size - existing position
            assert trade_order["price"] == 0.0
            assert trade_order["type"] == "market"
            assert "timestamp" in trade_order
    
    @pytest.mark.asyncio
    async def test_apply_risk_management_with_max_exposure(self, config, mock_client):
        """Test that _apply_risk_management applies risk management correctly with max exposure."""
        with patch("core.risk.risk_manager.RustCommClient", return_value=mock_client):
            # Create a risk manager
            risk_manager = RiskManager(config)
            
            # Add existing positions
            risk_manager.portfolio["SOL-USDC"] = {
                "position": 0.05,
                "value": 500.0
            }
            risk_manager.portfolio["JTO-USDC"] = {
                "position": 0.4,
                "value": 4000.0
            }
            
            # Create a test trade signal
            trade_signal = {
                "action": "buy",
                "position_size": 0.2,
                "confidence": 0.8
            }
            
            # Apply risk management
            trade_order = risk_manager._apply_risk_management("SOL-USDC", trade_signal)
            
            # Check that the trade order is correct
            assert trade_order["market"] == "SOL-USDC"
            assert trade_order["action"] == "buy"
            assert trade_order["size"] < 0.1  # Limited by max_exposure
            assert trade_order["price"] == 0.0
            assert trade_order["type"] == "market"
            assert "timestamp" in trade_order
    
    @pytest.mark.asyncio
    async def test_handle_trade_signal_update(self, config, mock_client):
        """Test that _handle_trade_signal_update updates trade signals correctly."""
        with patch("core.risk.risk_manager.RustCommClient", return_value=mock_client):
            # Create a risk manager
            risk_manager = RiskManager(config)
            
            # Create a test message
            message = {
                "data": {
                    "market": "SOL-USDC",
                    "trade_signal": {
                        "action": "buy",
                        "position_size": 0.2,
                        "confidence": 0.8
                    }
                }
            }
            
            # Handle trade signal update
            await risk_manager._handle_trade_signal_update(message)
            
            # Check that the trade signal was updated
            assert "SOL-USDC" in risk_manager.trade_signals
            assert risk_manager.trade_signals["SOL-USDC"]["action"] == "buy"
            assert risk_manager.trade_signals["SOL-USDC"]["position_size"] == 0.2
            assert risk_manager.trade_signals["SOL-USDC"]["confidence"] == 0.8
