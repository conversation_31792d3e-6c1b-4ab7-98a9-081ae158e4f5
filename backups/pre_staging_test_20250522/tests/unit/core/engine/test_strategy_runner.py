"""
Tests for the strategy runner.
"""

import pytest
import asyncio
from unittest.mock import MagicMock, patch
from core.engine.strategy_runner import StrategyRunner

class TestStrategyRunner:
    """Tests for the StrategyRunner class."""

    @pytest.fixture
    def config(self):
        """Create a test configuration."""
        return {
            "strategies": [
                {
                    "name": "momentum",
                    "markets": ["SOL-USDC", "JTO-USDC"],
                    "weight": 0.6
                },
                {
                    "name": "order_book_imbalance",
                    "markets": ["SOL-USDC", "JTO-USDC"],
                    "weight": 0.4
                }
            ],
            "strategy_runner": {
                "update_interval_ms": 1000,
                "publish_interval_ms": 1000
            }
        }

    @pytest.fixture
    def mock_client(self):
        """Create a mock RustCommClient."""
        mock = MagicMock()
        mock.connect = MagicMock(return_value=asyncio.Future())
        mock.connect.return_value.set_result(None)
        mock.disconnect = MagicMock(return_value=asyncio.Future())
        mock.disconnect.return_value.set_result(None)
        mock.subscribe = MagicMock(return_value=asyncio.Future())
        mock.subscribe.return_value.set_result(None)
        mock.publish = MagicMock(return_value=asyncio.Future())
        mock.publish.return_value.set_result(None)
        return mock

    @pytest.mark.asyncio
    async def test_initialization(self, config, mock_client):
        """Test that the StrategyRunner initializes correctly."""
        with patch("core.engine.strategy_runner.RustCommClient", return_value=mock_client):
            # Create a strategy runner
            strategy_runner = StrategyRunner(config)

            # Check that the strategy runner was initialized correctly
            assert strategy_runner.config == config
            assert strategy_runner.signals == {}
            assert strategy_runner.trade_signals == {}
            assert strategy_runner.running is False
            assert strategy_runner.tasks == []

    @pytest.mark.asyncio
    async def test_start_stop(self, config, mock_client):
        """Test that the StrategyRunner starts and stops correctly."""
        with patch("core.engine.strategy_runner.RustCommClient", return_value=mock_client):
            # Create a strategy runner
            strategy_runner = StrategyRunner(config)

            # Start the strategy runner
            await strategy_runner.start()

            # Check that the strategy runner was started correctly
            assert strategy_runner.running is True
            assert len(strategy_runner.tasks) == 2

            # Check that the client was connected
            mock_client.connect.assert_called_once()

            # Check that the client subscribed to signals
            assert mock_client.subscribe.call_count == 2
            mock_client.subscribe.assert_any_call("signals/momentum", strategy_runner._handle_signal_update)
            mock_client.subscribe.assert_any_call("signals/order_book_imbalance", strategy_runner._handle_signal_update)

            # Stop the strategy runner
            await strategy_runner.stop()

            # Check that the strategy runner was stopped correctly
            assert strategy_runner.running is False
            assert strategy_runner.tasks == []

            # Check that the client was disconnected
            mock_client.disconnect.assert_called_once()

    @pytest.mark.asyncio
    async def test_generate_trade_signal(self, config, mock_client):
        """Test that _generate_trade_signal generates trade signals correctly."""
        with patch("core.engine.strategy_runner.RustCommClient", return_value=mock_client):
            # Create a strategy runner
            strategy_runner = StrategyRunner(config)

            # Add signals
            strategy_runner.signals = {
                "momentum": {
                    "signals": {
                        "SOL-USDC": 0.5
                    },
                    "confidence": 0.8
                },
                "order_book_imbalance": {
                    "signals": {
                        "SOL-USDC": -0.3
                    },
                    "confidence": 0.6
                }
            }

            # Generate trade signal
            trade_signal = strategy_runner._generate_trade_signal("SOL-USDC")

            # Check that the trade signal is correct
            assert trade_signal["market"] == "SOL-USDC"
            assert trade_signal["signals"]["momentum"] == 0.5
            assert trade_signal["signals"]["order_book_imbalance"] == -0.3

            # Calculate expected values
            # Weighted signal = (0.5 * 0.6 * 0.8) + (-0.3 * 0.4 * 0.6) = 0.24 - 0.072 = 0.168
            # Confidence = (0.8 * 0.6 + 0.6 * 0.4) / (0.6 + 0.4) = 0.48 + 0.24 = 0.72
            # Position size = 0.168 * 0.72 = 0.12096
            # Action = "buy" (position_size > 0.1)
            assert trade_signal["action"] == "buy"
            assert round(trade_signal["position_size"], 4) == 0.168
            assert round(trade_signal["confidence"], 4) == 0.72

    @pytest.mark.asyncio
    async def test_handle_signal_update(self, config, mock_client):
        """Test that _handle_signal_update updates signals correctly."""
        with patch("core.engine.strategy_runner.RustCommClient", return_value=mock_client):
            # Create a strategy runner
            strategy_runner = StrategyRunner(config)

            # Create a test message
            message = {
                "data": {
                    "strategy": "momentum",
                    "signals": {
                        "signals": {
                            "SOL-USDC": 0.5
                        },
                        "confidence": 0.8
                    }
                }
            }

            # Handle signal update
            await strategy_runner._handle_signal_update(message)

            # Check that the signals were updated
            assert "momentum" in strategy_runner.signals
            assert strategy_runner.signals["momentum"]["signals"]["SOL-USDC"] == 0.5
            assert strategy_runner.signals["momentum"]["confidence"] == 0.8
