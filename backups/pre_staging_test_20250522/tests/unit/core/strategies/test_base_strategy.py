"""
Tests for the base strategy.
"""

import pytest
from core.strategies.base import BaseStrategy

class TestBaseStrategy:
    """Tests for the BaseStrategy class."""
    
    def test_initialization(self):
        """Test that the BaseStrategy initializes correctly."""
        # Create a test configuration
        config = {
            "name": "test_strategy",
            "markets": ["SOL-USDC", "JTO-USDC"],
            "parameters": {
                "window_size": 20,
                "threshold": 0.01,
                "max_value": 0.05
            }
        }
        
        # Create a test strategy
        strategy = BaseStrategy(config)
        
        # Check that the strategy was initialized correctly
        assert strategy.name == "test_strategy"
        assert strategy.markets == ["SOL-USDC", "JTO-USDC"]
        assert strategy.parameters == {
            "window_size": 20,
            "threshold": 0.01,
            "max_value": 0.05
        }
        assert strategy.state == {}
    
    def test_generate_signals_not_implemented(self):
        """Test that generate_signals raises NotImplementedError."""
        # Create a test configuration
        config = {
            "name": "test_strategy",
            "markets": ["SOL-USDC"],
            "parameters": {}
        }
        
        # Create a test strategy
        strategy = BaseStrategy(config)
        
        # Check that generate_signals raises NotImplementedError
        with pytest.raises(NotImplementedError):
            strategy.generate_signals({})
    
    def test_get_order_book(self):
        """Test that get_order_book returns the correct order book."""
        # Create a test configuration
        config = {
            "name": "test_strategy",
            "markets": ["SOL-USDC"],
            "parameters": {}
        }
        
        # Create a test strategy
        strategy = BaseStrategy(config)
        
        # Create test market data
        market_data = {
            "order_books": {
                "SOL-USDC": {
                    "bids": [
                        {"price": 100.0, "size": 1.0},
                        {"price": 99.0, "size": 2.0}
                    ],
                    "asks": [
                        {"price": 101.0, "size": 1.0},
                        {"price": 102.0, "size": 2.0}
                    ],
                    "metrics": {
                        "mid_price": 100.5,
                        "spread": 1.0,
                        "spread_pct": 0.01,
                        "bid_ask_imbalance": 0.5
                    }
                }
            }
        }
        
        # Get order book
        order_book = strategy.get_order_book(market_data, "SOL-USDC")
        
        # Check that the order book is correct
        assert order_book["bids"][0]["price"] == 100.0
        assert order_book["bids"][0]["size"] == 1.0
        assert order_book["asks"][0]["price"] == 101.0
        assert order_book["asks"][0]["size"] == 1.0
        assert order_book["metrics"]["mid_price"] == 100.5
        assert order_book["metrics"]["spread"] == 1.0
        assert order_book["metrics"]["spread_pct"] == 0.01
        assert order_book["metrics"]["bid_ask_imbalance"] == 0.5
    
    def test_get_mid_price(self):
        """Test that get_mid_price returns the correct mid price."""
        # Create a test configuration
        config = {
            "name": "test_strategy",
            "markets": ["SOL-USDC"],
            "parameters": {}
        }
        
        # Create a test strategy
        strategy = BaseStrategy(config)
        
        # Create test order book
        order_book = {
            "metrics": {
                "mid_price": 100.5
            }
        }
        
        # Get mid price
        mid_price = strategy.get_mid_price(order_book)
        
        # Check that the mid price is correct
        assert mid_price == 100.5
    
    def test_calculate_signal_strength(self):
        """Test that calculate_signal_strength returns the correct signal strength."""
        # Create a test configuration
        config = {
            "name": "test_strategy",
            "markets": ["SOL-USDC"],
            "parameters": {}
        }
        
        # Create a test strategy
        strategy = BaseStrategy(config)
        
        # Test cases
        test_cases = [
            # value, threshold, max_value, expected
            (0.0, 0.01, 0.05, 0.0),  # Below threshold
            (0.01, 0.01, 0.05, 0.0),  # At threshold
            (0.03, 0.01, 0.05, 0.5),  # Between threshold and max_value
            (0.05, 0.01, 0.05, 1.0),  # At max_value
            (0.07, 0.01, 0.05, 1.0),  # Above max_value
            (-0.03, 0.01, 0.05, -0.5),  # Negative value
        ]
        
        for value, threshold, max_value, expected in test_cases:
            signal_strength = strategy.calculate_signal_strength(value, threshold, max_value)
            assert signal_strength == expected
    
    def test_calculate_confidence(self):
        """Test that calculate_confidence returns the correct confidence."""
        # Create a test configuration
        config = {
            "name": "test_strategy",
            "markets": ["SOL-USDC"],
            "parameters": {}
        }
        
        # Create a test strategy
        strategy = BaseStrategy(config)
        
        # Test cases
        test_cases = [
            # signals, expected
            ({}, 0.0),  # Empty signals
            ({"signal1": 0.5}, 0.5),  # Single signal
            ({"signal1": 0.5, "signal2": 0.7}, 0.6),  # Multiple signals
            ({"signal1": -0.5, "signal2": 0.7}, 0.6),  # Mixed signals
        ]
        
        for signals, expected in test_cases:
            confidence = strategy.calculate_confidence(signals)
            assert confidence == expected
    
    def test_calculate_position_size(self):
        """Test that calculate_position_size returns the correct position size."""
        # Create a test configuration
        config = {
            "name": "test_strategy",
            "markets": ["SOL-USDC"],
            "parameters": {}
        }
        
        # Create a test strategy
        strategy = BaseStrategy(config)
        
        # Test cases
        test_cases = [
            # signals, confidence, max_position_size, expected
            ({}, 0.5, 1.0, 0.0),  # Empty signals
            ({"signal1": 0.5}, 0.5, 1.0, 0.25),  # Single signal
            ({"signal1": 0.5, "signal2": 0.7}, 0.5, 1.0, 0.3),  # Multiple signals
            ({"signal1": -0.5, "signal2": 0.7}, 0.5, 1.0, 0.05),  # Mixed signals
            ({"signal1": 0.5}, 0.5, 2.0, 0.5),  # Different max_position_size
        ]
        
        for signals, confidence, max_position_size, expected in test_cases:
            position_size = strategy.calculate_position_size(signals, confidence, max_position_size)
            assert position_size == expected
