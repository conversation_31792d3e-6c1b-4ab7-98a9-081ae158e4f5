"""
Tests for the order book imbalance strategy.
"""

import pytest
from core.strategies.order_book_imbalance import OrderBookImbalanceStrategy

class TestOrderBookImbalanceStrategy:
    """Tests for the OrderBookImbalanceStrategy class."""
    
    def test_initialization(self):
        """Test that the OrderBookImbalanceStrategy initializes correctly."""
        # Create a test configuration
        config = {
            "name": "test_order_book_imbalance_strategy",
            "markets": ["SOL-USDC", "JTO-USDC"],
            "parameters": {
                "window_size": 20,
                "threshold": 0.1,
                "max_value": 0.5,
                "depth": 10,
                "smoothing_factor": 0.2
            }
        }
        
        # Create a test strategy
        strategy = OrderBookImbalanceStrategy(config)
        
        # Check that the strategy was initialized correctly
        assert strategy.name == "test_order_book_imbalance_strategy"
        assert strategy.markets == ["SOL-USDC", "JTO-USDC"]
        assert strategy.window_size == 20
        assert strategy.threshold == 0.1
        assert strategy.max_value == 0.5
        assert strategy.depth == 10
        assert strategy.smoothing_factor == 0.2
        assert strategy.imbalance_history == {}
    
    def test_calculate_imbalance(self):
        """Test that _calculate_imbalance returns the correct imbalance."""
        # Create a test configuration
        config = {
            "name": "test_order_book_imbalance_strategy",
            "markets": ["SOL-USDC"],
            "parameters": {
                "window_size": 20,
                "threshold": 0.1,
                "max_value": 0.5,
                "depth": 2,
                "smoothing_factor": 0.2
            }
        }
        
        # Create a test strategy
        strategy = OrderBookImbalanceStrategy(config)
        
        # Create test order book
        order_book = {
            "bids": [
                {"price": 100.0, "size": 1.0},
                {"price": 99.0, "size": 2.0}
            ],
            "asks": [
                {"price": 101.0, "size": 1.0},
                {"price": 102.0, "size": 2.0}
            ]
        }
        
        # Calculate imbalance
        imbalance = strategy._calculate_imbalance(order_book)
        
        # Check that the imbalance is correct
        # Bid liquidity = 100.0 * 1.0 + 99.0 * 2.0 = 100.0 + 198.0 = 298.0
        # Ask liquidity = 101.0 * 1.0 + 102.0 * 2.0 = 101.0 + 204.0 = 305.0
        # Total liquidity = 298.0 + 305.0 = 603.0
        # Imbalance = (298.0 - 305.0) / 603.0 = -7.0 / 603.0 = -0.0116
        assert round(imbalance, 4) == -0.0116
    
    def test_calculate_smoothed_imbalance_empty_history(self):
        """Test that _calculate_smoothed_imbalance returns 0.0 for empty imbalance history."""
        # Create a test configuration
        config = {
            "name": "test_order_book_imbalance_strategy",
            "markets": ["SOL-USDC"],
            "parameters": {
                "window_size": 20,
                "threshold": 0.1,
                "max_value": 0.5,
                "depth": 10,
                "smoothing_factor": 0.2
            }
        }
        
        # Create a test strategy
        strategy = OrderBookImbalanceStrategy(config)
        
        # Calculate smoothed imbalance
        smoothed_imbalance = strategy._calculate_smoothed_imbalance("SOL-USDC")
        
        # Check that the smoothed imbalance is 0.0
        assert smoothed_imbalance == 0.0
    
    def test_calculate_smoothed_imbalance_single_value(self):
        """Test that _calculate_smoothed_imbalance returns the correct smoothed imbalance for a single value."""
        # Create a test configuration
        config = {
            "name": "test_order_book_imbalance_strategy",
            "markets": ["SOL-USDC"],
            "parameters": {
                "window_size": 20,
                "threshold": 0.1,
                "max_value": 0.5,
                "depth": 10,
                "smoothing_factor": 0.2
            }
        }
        
        # Create a test strategy
        strategy = OrderBookImbalanceStrategy(config)
        
        # Add a single imbalance
        strategy.imbalance_history["SOL-USDC"] = [0.1]
        
        # Calculate smoothed imbalance
        smoothed_imbalance = strategy._calculate_smoothed_imbalance("SOL-USDC")
        
        # Check that the smoothed imbalance is 0.1
        assert smoothed_imbalance == 0.1
    
    def test_calculate_smoothed_imbalance_multiple_values(self):
        """Test that _calculate_smoothed_imbalance returns the correct smoothed imbalance for multiple values."""
        # Create a test configuration
        config = {
            "name": "test_order_book_imbalance_strategy",
            "markets": ["SOL-USDC"],
            "parameters": {
                "window_size": 20,
                "threshold": 0.1,
                "max_value": 0.5,
                "depth": 10,
                "smoothing_factor": 0.2
            }
        }
        
        # Create a test strategy
        strategy = OrderBookImbalanceStrategy(config)
        
        # Add multiple imbalances
        strategy.imbalance_history["SOL-USDC"] = [0.1, 0.2, 0.3, 0.4, 0.5]
        
        # Calculate smoothed imbalance
        smoothed_imbalance = strategy._calculate_smoothed_imbalance("SOL-USDC")
        
        # Check that the smoothed imbalance is positive
        assert smoothed_imbalance > 0.0
    
    def test_generate_signals_empty_market_data(self):
        """Test that generate_signals returns empty signals for empty market data."""
        # Create a test configuration
        config = {
            "name": "test_order_book_imbalance_strategy",
            "markets": ["SOL-USDC"],
            "parameters": {
                "window_size": 20,
                "threshold": 0.1,
                "max_value": 0.5,
                "depth": 10,
                "smoothing_factor": 0.2,
                "max_position_size": 1.0
            }
        }
        
        # Create a test strategy
        strategy = OrderBookImbalanceStrategy(config)
        
        # Generate signals
        signals = strategy.generate_signals({})
        
        # Check that the signals are empty
        assert signals["signals"] == {}
        assert signals["confidence"] == 0.0
        assert signals["position_size"] == 0.0
    
    def test_generate_signals_with_market_data(self):
        """Test that generate_signals returns correct signals for market data."""
        # Create a test configuration
        config = {
            "name": "test_order_book_imbalance_strategy",
            "markets": ["SOL-USDC"],
            "parameters": {
                "window_size": 20,
                "threshold": 0.1,
                "max_value": 0.5,
                "depth": 10,
                "smoothing_factor": 0.2,
                "max_position_size": 1.0
            }
        }
        
        # Create a test strategy
        strategy = OrderBookImbalanceStrategy(config)
        
        # Create test market data
        market_data = {
            "order_books": {
                "SOL-USDC": {
                    "bids": [
                        {"price": 100.0, "size": 1.0},
                        {"price": 99.0, "size": 2.0}
                    ],
                    "asks": [
                        {"price": 101.0, "size": 1.0},
                        {"price": 102.0, "size": 2.0}
                    ],
                    "metrics": {
                        "mid_price": 100.5,
                        "spread": 1.0,
                        "spread_pct": 0.01,
                        "bid_ask_imbalance": 0.5
                    }
                }
            }
        }
        
        # Generate signals
        signals = strategy.generate_signals(market_data)
        
        # Check that the signals are correct
        assert "SOL-USDC" in signals["signals"]
        assert "confidence" in signals
        assert "position_size" in signals
        assert "timestamp" in signals
    
    def test_generate_signals_with_multiple_markets(self):
        """Test that generate_signals returns correct signals for multiple markets."""
        # Create a test configuration
        config = {
            "name": "test_order_book_imbalance_strategy",
            "markets": ["SOL-USDC", "JTO-USDC"],
            "parameters": {
                "window_size": 20,
                "threshold": 0.1,
                "max_value": 0.5,
                "depth": 10,
                "smoothing_factor": 0.2,
                "max_position_size": 1.0
            }
        }
        
        # Create a test strategy
        strategy = OrderBookImbalanceStrategy(config)
        
        # Create test market data
        market_data = {
            "order_books": {
                "SOL-USDC": {
                    "bids": [
                        {"price": 100.0, "size": 1.0},
                        {"price": 99.0, "size": 2.0}
                    ],
                    "asks": [
                        {"price": 101.0, "size": 1.0},
                        {"price": 102.0, "size": 2.0}
                    ],
                    "metrics": {
                        "mid_price": 100.5,
                        "spread": 1.0,
                        "spread_pct": 0.01,
                        "bid_ask_imbalance": 0.5
                    }
                },
                "JTO-USDC": {
                    "bids": [
                        {"price": 10.0, "size": 1.0},
                        {"price": 9.9, "size": 2.0}
                    ],
                    "asks": [
                        {"price": 10.1, "size": 1.0},
                        {"price": 10.2, "size": 2.0}
                    ],
                    "metrics": {
                        "mid_price": 10.05,
                        "spread": 0.1,
                        "spread_pct": 0.01,
                        "bid_ask_imbalance": 0.5
                    }
                }
            }
        }
        
        # Generate signals
        signals = strategy.generate_signals(market_data)
        
        # Check that the signals are correct
        assert "SOL-USDC" in signals["signals"]
        assert "JTO-USDC" in signals["signals"]
        assert "confidence" in signals
        assert "position_size" in signals
        assert "timestamp" in signals
