#!/usr/bin/env python3
"""
Tests for the secure wallet module.
"""

import os
import sys
import unittest
import tempfile
from unittest.mock import patch, MagicMock

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from phase_4_deployment.wallet_sync.secure_wallet import SecureWallet

class TestSecureWallet(unittest.TestCase):
    """Test cases for the SecureWallet class."""
    
    def setUp(self):
        """Set up test environment."""
        # Create temporary directory for wallet files
        self.temp_dir = tempfile.TemporaryDirectory()
        self.wallet_dir = self.temp_dir.name
        
        # Create secure wallet instance
        self.wallet = SecureWallet(wallet_dir=self.wallet_dir)
        
        # Test data
        self.test_password = "test_password"
        self.test_private_key = "4NMwGqeU9rW9YHCHBEdKjrfzGnQwdWQVK5aRtJPHbCd2xnX8iUxzKtpj9JQBEUMXUUg7BjFVhPKXvtBYNKNXyJbF"
    
    def tearDown(self):
        """Clean up after tests."""
        self.temp_dir.cleanup()
    
    def test_generate_encryption_key(self):
        """Test generating encryption key."""
        # Generate key with fixed salt for reproducibility
        salt = b'test_salt_12345678'
        key = self.wallet.generate_encryption_key(self.test_password, salt)
        
        # Key should be a bytes object of correct length
        self.assertIsInstance(key, bytes)
        self.assertEqual(len(key), 44)  # Base64 encoded 32-byte key
    
    @patch('getpass.getpass')
    def test_load_encryption_key_new(self, mock_getpass):
        """Test loading encryption key when none exists."""
        # Mock getpass to return test password
        mock_getpass.side_effect = [self.test_password, self.test_password]
        
        # Load key (should create new one)
        key = self.wallet.load_encryption_key()
        
        # Key should be a bytes object of correct length
        self.assertIsInstance(key, bytes)
        self.assertEqual(len(key), 44)  # Base64 encoded 32-byte key
        
        # Key file should exist
        key_file = os.path.join(self.wallet_dir, '.key')
        self.assertTrue(os.path.exists(key_file))
    
    def test_encrypt_decrypt_private_key(self):
        """Test encrypting and decrypting private key."""
        # Generate key with fixed salt for reproducibility
        salt = b'test_salt_12345678'
        self.wallet.encryption_key = self.wallet.generate_encryption_key(self.test_password, salt)
        
        # Encrypt private key
        encrypted = self.wallet.encrypt_private_key(self.test_private_key)
        
        # Encrypted key should be a bytes object
        self.assertIsInstance(encrypted, bytes)
        
        # Decrypt private key
        decrypted = self.wallet.decrypt_private_key(encrypted)
        
        # Decrypted key should match original
        self.assertEqual(decrypted, self.test_private_key)
    
    @patch('phase_4_deployment.wallet_sync.secure_wallet.Keypair')
    def test_create_wallet_new(self, mock_keypair):
        """Test creating a new wallet."""
        # Mock Keypair
        mock_instance = MagicMock()
        mock_instance.pubkey.return_value = "test_public_key"
        mock_instance.to_base58_string.return_value = self.test_private_key
        mock_keypair.return_value = mock_instance
        
        # Create new wallet
        public_key, private_key = self.wallet.create_wallet()
        
        # Should return public and private keys
        self.assertEqual(public_key, "test_public_key")
        self.assertEqual(private_key, self.test_private_key)
    
    @patch('phase_4_deployment.wallet_sync.secure_wallet.Keypair')
    def test_create_wallet_existing(self, mock_keypair):
        """Test creating a wallet from existing private key."""
        # Mock Keypair.from_base58_string
        mock_instance = MagicMock()
        mock_instance.pubkey.return_value = "test_public_key"
        mock_keypair.from_base58_string.return_value = mock_instance
        
        # Create wallet from existing private key
        public_key, private_key = self.wallet.create_wallet(self.test_private_key)
        
        # Should return public and private keys
        self.assertEqual(public_key, "test_public_key")
        self.assertEqual(private_key, self.test_private_key)
    
    def test_save_load_wallet(self):
        """Test saving and loading wallet."""
        # Generate encryption key
        salt = b'test_salt_12345678'
        self.wallet.encryption_key = self.wallet.generate_encryption_key(self.test_password, salt)
        
        # Save wallet
        wallet_file = self.wallet.save_wallet("test_public_key", self.test_private_key)
        
        # Wallet file should exist
        self.assertTrue(os.path.exists(wallet_file))
        
        # Mock create_wallet to avoid actual keypair creation
        with patch.object(self.wallet, 'create_wallet') as mock_create:
            mock_create.return_value = ("test_public_key", self.test_private_key)
            
            # Load wallet
            public_key, private_key = self.wallet.load_wallet("test_public_key")
            
            # Should return correct keys
            self.assertEqual(public_key, "test_public_key")
            self.assertEqual(private_key, self.test_private_key)

if __name__ == '__main__':
    unittest.main()
