#!/usr/bin/env python3
"""
Test script for the transaction preparation service.
"""

import os
import sys
import json
import unittest
from pathlib import Path

# Add the parent directory to the path
sys.path.append(str(Path(__file__).parent.parent))

try:
    from solana_tx_utils.tx_prep import TransactionPreparationService
    USING_RUST_UTILS = True
except ImportError:
    USING_RUST_UTILS = False
    print("WARNING: solana_tx_utils.tx_prep not available, skipping tests")

@unittest.skipIf(not USING_RUST_UTILS, "solana_tx_utils.tx_prep not available")
class TestTransactionPreparationService(unittest.TestCase):
    """Test cases for the transaction preparation service."""
    
    def setUp(self):
        """Set up the test environment."""
        # Use a test RPC URL
        self.rpc_url = os.environ.get("SOLANA_RPC_URL", "https://api.mainnet-beta.solana.com")
        
        # Create the service
        self.service = TransactionPreparationService(self.rpc_url)
        
        # Test keypair path (create a test keypair if needed)
        self.test_keypair_path = os.environ.get("TEST_KEYPAIR_PATH")
        if self.test_keypair_path and os.path.exists(self.test_keypair_path):
            self.service.load_keypair("test", self.test_keypair_path)
    
    def test_get_recent_blockhash(self):
        """Test getting a recent blockhash."""
        blockhash = self.service.get_recent_blockhash()
        self.assertIsNotNone(blockhash)
        self.assertTrue(len(blockhash) > 0)
        print(f"Got recent blockhash: {blockhash}")
    
    @unittest.skipIf(not os.environ.get("TEST_KEYPAIR_PATH"), "TEST_KEYPAIR_PATH not set")
    def test_keypair_management(self):
        """Test keypair management."""
        # Get the public key
        pubkey = self.service.get_active_pubkey()
        self.assertIsNotNone(pubkey)
        self.assertTrue(len(pubkey) > 0)
        print(f"Active keypair public key: {pubkey}")
        
        # List keypairs
        keypairs = self.service.list_keypairs()
        self.assertIsNotNone(keypairs)
        self.assertTrue(len(keypairs) > 0)
        print(f"Loaded keypairs: {keypairs}")
    
    def test_build_transaction(self):
        """Test building a transaction."""
        # Skip if no keypair is available
        if not self.test_keypair_path:
            self.skipTest("TEST_KEYPAIR_PATH not set")
        
        # Get a recent blockhash
        blockhash = self.service.get_recent_blockhash()
        
        # Get the active pubkey
        pubkey = self.service.get_active_pubkey()
        
        # Build a simple transaction (just a memo instruction)
        instructions = [
            {
                "programId": "MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr",
                "accounts": [
                    {
                        "pubkey": pubkey,
                        "isSigner": True,
                        "isWritable": True
                    }
                ],
                "data": "48656c6c6f2c20776f726c6421"  # "Hello, world!" in hex
            }
        ]
        
        # Build the transaction
        tx_bytes = self.service.build_transaction(
            instructions=instructions,
            fee_payer=pubkey,
            recent_blockhash=blockhash,
            priority_fee_microlamports=10000,
            compute_unit_limit=200000,
            is_versioned=True
        )
        
        self.assertIsNotNone(tx_bytes)
        self.assertTrue(len(tx_bytes) > 0)
        print(f"Built transaction with {len(tx_bytes)} bytes")
        
        # Sign the transaction
        signed_tx_bytes = self.service.sign_transaction(tx_bytes, is_versioned=True)
        self.assertIsNotNone(signed_tx_bytes)
        self.assertTrue(len(signed_tx_bytes) > 0)
        print(f"Signed transaction with {len(signed_tx_bytes)} bytes")
        
        # Prepare for submission
        encoded_tx = self.service.prepare_for_submission(signed_tx_bytes, encoding="base58")
        self.assertIsNotNone(encoded_tx)
        self.assertTrue(len(encoded_tx) > 0)
        print(f"Encoded transaction: {encoded_tx[:20]}...")
        
        # Also test base64 encoding
        encoded_tx_base64 = self.service.prepare_for_submission(signed_tx_bytes, encoding="base64")
        self.assertIsNotNone(encoded_tx_base64)
        self.assertTrue(len(encoded_tx_base64) > 0)
        print(f"Base64 encoded transaction: {encoded_tx_base64[:20]}...")


if __name__ == "__main__":
    unittest.main()
