#!/usr/bin/env python3
"""
Transaction Executor

This module provides functionality for executing prepared transactions.
"""

import os
import sys
import json
import time
import logging
import asyncio
import subprocess
from datetime import datetime
from typing import Dict, Any, List, Optional, Union, Callable, Awaitable

# Install required packages
try:
    import zmq
    import zmq.asyncio
    import httpx
except ImportError:
    print("Installing required packages...")
    subprocess.check_call([sys.executable, "-m", "pip", "install", "pyzmq", "httpx"])
    import zmq
    import zmq.asyncio
    import httpx

# Import communication layer client
from shared.rust.comm_layer.client import RustCommClient, CommunicationError

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TransactionExecutionError(Exception):
    """Exception raised for transaction execution errors."""
    pass

class TransactionExecutor:
    """Executor for prepared transactions."""

    def __init__(
        self,
        config: Dict[str, Any],
        carbon_core_pub_endpoint: str = "tcp://127.0.0.1:5556",
        carbon_core_sub_endpoint: str = "tcp://127.0.0.1:5555",
        carbon_core_req_endpoint: str = "tcp://127.0.0.1:5557",
    ):
        """
        Initialize the transaction executor.

        Args:
            config: Configuration dictionary
            carbon_core_pub_endpoint: Carbon Core publisher endpoint
            carbon_core_sub_endpoint: Carbon Core subscriber endpoint
            carbon_core_req_endpoint: Carbon Core request-reply endpoint
        """
        self.config = config

        # Create communication client for Carbon Core
        self.carbon_core_client = RustCommClient(
            pub_endpoint=carbon_core_pub_endpoint,
            sub_endpoint=carbon_core_sub_endpoint,
            req_endpoint=carbon_core_req_endpoint,
        )

        # Initialize HTTP client
        self.http_client = httpx.AsyncClient(
            timeout=30.0,
            limits=httpx.Limits(max_connections=10, max_keepalive_connections=5),
        )

        # Initialize prepared transactions
        self.prepared_transactions = {}

        # Initialize executed transactions
        self.executed_transactions = {}

        # Initialize state
        self.running = False
        self.tasks = []

        logger.info("Initialized transaction executor")

    async def start(self):
        """Start the transaction executor."""
        if self.running:
            logger.warning("Transaction executor is already running")
            return

        logger.info("Starting transaction executor...")

        # Connect to Carbon Core
        await self.carbon_core_client.connect()
        logger.info("Connected to Carbon Core")

        # Subscribe to prepared transactions
        await self._subscribe_to_prepared_transactions()

        # Set running flag
        self.running = True

        # Start transaction executor tasks
        self.tasks = [
            asyncio.create_task(self._run_transaction_executor()),
            asyncio.create_task(self._run_transaction_result_publishing()),
        ]

        logger.info("Transaction executor started")

    async def stop(self):
        """Stop the transaction executor."""
        if not self.running:
            logger.warning("Transaction executor is not running")
            return

        logger.info("Stopping transaction executor...")

        # Set running flag
        self.running = False

        # Cancel tasks
        for task in self.tasks:
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass

        # Clear tasks
        self.tasks = []

        # Close HTTP client
        await self.http_client.aclose()

        # Disconnect from Carbon Core
        await self.carbon_core_client.disconnect()
        logger.info("Disconnected from Carbon Core")

        logger.info("Transaction executor stopped")

    async def _subscribe_to_prepared_transactions(self):
        """Subscribe to prepared transactions."""
        logger.info("Subscribing to prepared transactions...")

        # Get markets from configuration
        markets = self.config.get("market_microstructure", {}).get("markets", [])

        for market in markets:
            # Subscribe to prepared transactions
            await self.carbon_core_client.subscribe(f"prepared_transactions/{market}", self._handle_prepared_transaction_update)
            logger.info(f"Subscribed to prepared transactions for {market}")

    async def _run_transaction_executor(self):
        """Run transaction executor."""
        logger.info("Starting transaction executor loop...")

        try:
            while self.running:
                # Execute prepared transactions
                await self._execute_prepared_transactions()

                # Sleep for update interval
                await asyncio.sleep(self.config.get("transaction_execution", {}).get("update_interval_ms", 1000) / 1000)
        except asyncio.CancelledError:
            logger.info("Transaction executor loop cancelled")
            raise
        except Exception as e:
            logger.error(f"Error in transaction executor loop: {str(e)}")

    async def _run_transaction_result_publishing(self):
        """Run transaction result publishing."""
        logger.info("Starting transaction result publishing...")

        try:
            while self.running:
                # Publish executed transactions
                for market, transaction in self.executed_transactions.items():
                    try:
                        # Publish executed transaction
                        await self.carbon_core_client.publish(f"executed_transactions/{market}", {
                            "market": market,
                            "transaction": transaction,
                            "timestamp": datetime.now().isoformat(),
                        })

                        logger.debug(f"Published executed transaction for {market}")
                    except Exception as e:
                        logger.error(f"Error publishing executed transaction for {market}: {str(e)}")

                # Sleep for update interval
                await asyncio.sleep(self.config.get("transaction_execution", {}).get("publish_interval_ms", 1000) / 1000)
        except asyncio.CancelledError:
            logger.info("Transaction result publishing cancelled")
            raise
        except Exception as e:
            logger.error(f"Error in transaction result publishing: {str(e)}")

    async def _execute_prepared_transactions(self):
        """Execute prepared transactions."""
        # Get markets from configuration
        markets = self.config.get("market_microstructure", {}).get("markets", [])

        # Execute prepared transactions for each market
        for market in markets:
            if market not in self.prepared_transactions:
                continue

            try:
                # Get prepared transaction
                prepared_transaction = self.prepared_transactions[market]

                # Check if transaction is already executed
                if market in self.executed_transactions and self.executed_transactions[market].get("signature") == prepared_transaction.get("signature"):
                    continue

                # Execute transaction
                executed_transaction = await self._execute_transaction(market, prepared_transaction)

                # Update executed transactions
                self.executed_transactions[market] = executed_transaction

                logger.info(f"Executed transaction for {market}: {executed_transaction.get('signature')}")
            except Exception as e:
                logger.error(f"Error executing transaction for {market}: {str(e)}")

    async def _execute_transaction(self, market: str, prepared_transaction: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute a prepared transaction.

        Args:
            market: Market symbol
            prepared_transaction: Prepared transaction

        Returns:
            Dict[str, Any]: Executed transaction
        """
        try:
            # Get serialized transaction
            serialized_transaction = prepared_transaction.get("serialized_transaction", "")

            if not serialized_transaction:
                raise TransactionExecutionError("Missing serialized transaction")

            # Get RPC endpoint
            rpc_endpoint = self.config.get("rpc", {}).get("endpoint", "")

            if not rpc_endpoint:
                raise TransactionExecutionError("Missing RPC endpoint")

            # Check if simulation is enabled
            simulation_enabled = self.config.get("transaction_execution", {}).get("simulation_enabled", True)

            if simulation_enabled:
                try:
                    # Simulate transaction
                    simulation_result = await self._simulate_transaction(serialized_transaction, rpc_endpoint)

                    # Check simulation result
                    if not simulation_result.get("success", False):
                        raise TransactionExecutionError(f"Transaction simulation failed: {simulation_result.get('error', 'Unknown error')}")

                    logger.info(f"Transaction simulation successful for {market}")
                except Exception as e:
                    # Log the error but continue with dry run
                    logger.error(f"Error in transaction simulation for {market}: {str(e)}")
                    # Don't raise the error, just continue with dry run

            # Check if dry run is enabled
            dry_run = self.config.get("transaction_execution", {}).get("dry_run", True)

            if dry_run:
                # Create executed transaction with dummy signature
                executed_transaction = {
                    "market": market,
                    "action": prepared_transaction.get("action", ""),
                    "size": prepared_transaction.get("size", 0.0),
                    "price": prepared_transaction.get("price", 0.0),
                    "type": prepared_transaction.get("type", ""),
                    "signature": "DRY_RUN_" + str(int(time.time())),
                    "status": "success",
                    "timestamp": datetime.now().isoformat(),
                }

                logger.info(f"Dry run transaction for {market}")

                return executed_transaction

            # Send transaction
            send_result = await self._send_transaction(serialized_transaction, rpc_endpoint)

            # Check send result
            if not send_result.get("success", False):
                raise TransactionExecutionError(f"Transaction send failed: {send_result.get('error', 'Unknown error')}")

            # Get transaction signature
            signature = send_result.get("signature", "")

            # Create executed transaction
            executed_transaction = {
                "market": market,
                "action": prepared_transaction.get("action", ""),
                "size": prepared_transaction.get("size", 0.0),
                "price": prepared_transaction.get("price", 0.0),
                "type": prepared_transaction.get("type", ""),
                "signature": signature,
                "status": "success",
                "timestamp": datetime.now().isoformat(),
            }

            return executed_transaction
        except Exception as e:
            logger.error(f"Error executing transaction for {market}: {str(e)}")

            # Create failed executed transaction
            executed_transaction = {
                "market": market,
                "action": prepared_transaction.get("action", ""),
                "size": prepared_transaction.get("size", 0.0),
                "price": prepared_transaction.get("price", 0.0),
                "type": prepared_transaction.get("type", ""),
                "signature": "",
                "status": "failed",
                "error": str(e),
                "timestamp": datetime.now().isoformat(),
            }

            return executed_transaction

    async def _simulate_transaction(self, serialized_transaction: str, rpc_endpoint: str) -> Dict[str, Any]:
        """
        Simulate a transaction.

        Args:
            serialized_transaction: Serialized transaction
            rpc_endpoint: RPC endpoint

        Returns:
            Dict[str, Any]: Simulation result
        """
        try:
            # Prepare RPC request
            request = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "simulateTransaction",
                "params": [
                    serialized_transaction,
                    {"encoding": "base64", "commitment": "confirmed"}
                ]
            }

            # Send request
            response = await self.http_client.post(
                rpc_endpoint,
                json=request,
                headers={"Content-Type": "application/json"}
            )
            response.raise_for_status()

            # Parse response
            response_data = response.json()

            if "error" in response_data:
                return {
                    "success": False,
                    "error": response_data["error"],
                }

            # Get simulation result
            result = response_data.get("result", {})

            # Check if simulation was successful
            if result.get("err"):
                return {
                    "success": False,
                    "error": result["err"],
                }

            return {
                "success": True,
                "result": result,
            }
        except Exception as e:
            logger.error(f"Error simulating transaction: {str(e)}")
            return {
                "success": False,
                "error": str(e),
            }

    async def _send_transaction(self, serialized_transaction: str, rpc_endpoint: str) -> Dict[str, Any]:
        """
        Send a transaction.

        Args:
            serialized_transaction: Serialized transaction
            rpc_endpoint: RPC endpoint

        Returns:
            Dict[str, Any]: Send result
        """
        try:
            # Prepare RPC request
            request = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "sendTransaction",
                "params": [
                    serialized_transaction,
                    {"encoding": "base64", "skipPreflight": False, "preflightCommitment": "confirmed"}
                ]
            }

            # Send request
            response = await self.http_client.post(
                rpc_endpoint,
                json=request,
                headers={"Content-Type": "application/json"}
            )
            response.raise_for_status()

            # Parse response
            response_data = response.json()

            if "error" in response_data:
                return {
                    "success": False,
                    "error": response_data["error"],
                }

            # Get transaction signature
            signature = response_data.get("result", "")

            return {
                "success": True,
                "signature": signature,
            }
        except Exception as e:
            logger.error(f"Error sending transaction: {str(e)}")
            return {
                "success": False,
                "error": str(e),
            }

    async def _handle_prepared_transaction_update(self, message: Dict[str, Any]):
        """
        Handle prepared transaction update.

        Args:
            message: Prepared transaction update message
        """
        try:
            # Extract market
            market = message.get("data", {}).get("market", "")

            if not market:
                return

            # Extract prepared transaction
            prepared_transaction = message.get("data", {}).get("transaction", {})

            # Update prepared transactions
            self.prepared_transactions[market] = prepared_transaction

            logger.debug(f"Updated prepared transaction for {market}")
        except Exception as e:
            logger.error(f"Error handling prepared transaction update: {str(e)}")
