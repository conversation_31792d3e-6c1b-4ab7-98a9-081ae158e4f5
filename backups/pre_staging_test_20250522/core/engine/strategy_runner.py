#!/usr/bin/env python3
"""
Strategy Runner

This module provides functionality for running trading strategies and generating trade signals.
"""

import os
import sys
import json
import time
import logging
import asyncio
import subprocess
from datetime import datetime
from typing import Dict, Any, List, Optional, Union, Callable, Awaitable

# Install required packages
try:
    import zmq
    import zmq.asyncio
except ImportError:
    print("Installing required packages...")
    subprocess.check_call([sys.executable, "-m", "pip", "install", "pyzmq"])
    import zmq
    import zmq.asyncio

# Import communication layer client
from shared.rust.comm_layer.client import RustCommClient, CommunicationError

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class StrategyRunnerError(Exception):
    """Exception raised for strategy runner errors."""
    pass

class StrategyRunner:
    """Runner for trading strategies."""

    def __init__(
        self,
        config: Dict[str, Any],
        carbon_core_pub_endpoint: str = "tcp://127.0.0.1:5556",
        carbon_core_sub_endpoint: str = "tcp://127.0.0.1:5555",
        carbon_core_req_endpoint: str = "tcp://127.0.0.1:5557",
    ):
        """
        Initialize the strategy runner.

        Args:
            config: Configuration dictionary
            carbon_core_pub_endpoint: Carbon Core publisher endpoint
            carbon_core_sub_endpoint: Carbon Core subscriber endpoint
            carbon_core_req_endpoint: Carbon Core request-reply endpoint
        """
        self.config = config

        # Create communication client for Carbon Core
        self.carbon_core_client = RustCommClient(
            pub_endpoint=carbon_core_pub_endpoint,
            sub_endpoint=carbon_core_sub_endpoint,
            req_endpoint=carbon_core_req_endpoint,
        )

        # Initialize signals
        self.signals = {}

        # Initialize trade signals
        self.trade_signals = {}

        # Initialize state
        self.running = False
        self.tasks = []

        logger.info("Initialized strategy runner")

    async def start(self):
        """Start the strategy runner."""
        if self.running:
            logger.warning("Strategy runner is already running")
            return

        logger.info("Starting strategy runner...")

        # Connect to Carbon Core
        await self.carbon_core_client.connect()
        logger.info("Connected to Carbon Core")

        # Subscribe to signals
        await self._subscribe_to_signals()

        # Set running flag
        self.running = True

        # Start strategy runner tasks
        self.tasks = [
            asyncio.create_task(self._run_strategy_runner()),
            asyncio.create_task(self._run_trade_signal_publishing()),
        ]

        logger.info("Strategy runner started")

    async def stop(self):
        """Stop the strategy runner."""
        if not self.running:
            logger.warning("Strategy runner is not running")
            return

        logger.info("Stopping strategy runner...")

        # Set running flag
        self.running = False

        # Cancel tasks
        for task in self.tasks:
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass

        # Clear tasks
        self.tasks = []

        # Disconnect from Carbon Core
        await self.carbon_core_client.disconnect()
        logger.info("Disconnected from Carbon Core")

        logger.info("Strategy runner stopped")

    async def _subscribe_to_signals(self):
        """Subscribe to signals."""
        logger.info("Subscribing to signals...")

        # Get strategies from configuration
        strategy_configs = self.config.get("strategies", [])

        for strategy_config in strategy_configs:
            # Get strategy name
            strategy_name = strategy_config.get("name", "")

            if not strategy_name:
                logger.warning("Strategy configuration missing name")
                continue

            # Subscribe to strategy signals
            await self.carbon_core_client.subscribe(f"signals/{strategy_name}", self._handle_signal_update)
            logger.info(f"Subscribed to signals for {strategy_name}")

    async def _run_strategy_runner(self):
        """Run strategy runner."""
        logger.info("Starting strategy runner loop...")

        try:
            while self.running:
                # Process signals
                await self._process_signals()

                # Sleep for update interval
                await asyncio.sleep(self.config.get("strategy_runner", {}).get("update_interval_ms", 1000) / 1000)
        except asyncio.CancelledError:
            logger.info("Strategy runner loop cancelled")
            raise
        except Exception as e:
            logger.error(f"Error in strategy runner loop: {str(e)}")

    async def _run_trade_signal_publishing(self):
        """Run trade signal publishing."""
        logger.info("Starting trade signal publishing...")

        try:
            while self.running:
                # Publish trade signals
                for market, trade_signal in self.trade_signals.items():
                    try:
                        # Publish trade signal
                        await self.carbon_core_client.publish(f"trade_signals/{market}", {
                            "market": market,
                            "trade_signal": trade_signal,
                            "timestamp": datetime.now().isoformat(),
                        })

                        logger.debug(f"Published trade signal for {market}")
                    except Exception as e:
                        logger.error(f"Error publishing trade signal for {market}: {str(e)}")

                # Sleep for update interval
                await asyncio.sleep(self.config.get("strategy_runner", {}).get("publish_interval_ms", 1000) / 1000)
        except asyncio.CancelledError:
            logger.info("Trade signal publishing cancelled")
            raise
        except Exception as e:
            logger.error(f"Error in trade signal publishing: {str(e)}")

    async def _process_signals(self):
        """Process signals."""
        # Get strategies from configuration
        strategy_configs = self.config.get("strategies", [])

        # Process signals for each market
        markets = set()
        for strategy_config in strategy_configs:
            # Get strategy name
            strategy_name = strategy_config.get("name", "")

            if not strategy_name or strategy_name not in self.signals:
                continue

            # Get strategy signals
            strategy_signals = self.signals[strategy_name]

            # Get strategy markets
            strategy_markets = strategy_config.get("markets", [])

            # Add markets to set
            markets.update(strategy_markets)

        # Generate trade signals for each market
        for market in markets:
            try:
                # Generate trade signal
                trade_signal = self._generate_trade_signal(market)

                # Update trade signals
                self.trade_signals[market] = trade_signal

                logger.debug(f"Generated trade signal for {market}: {trade_signal}")
            except Exception as e:
                logger.error(f"Error generating trade signal for {market}: {str(e)}")

    def _generate_trade_signal(self, market: str) -> Dict[str, Any]:
        """
        Generate trade signal for a market.

        Args:
            market: Market symbol

        Returns:
            Dict[str, Any]: Trade signal
        """
        # Get strategies from configuration
        strategy_configs = self.config.get("strategies", [])

        # Collect signals for the market
        market_signals = {}

        for strategy_config in strategy_configs:
            # Get strategy name
            strategy_name = strategy_config.get("name", "")

            if not strategy_name or strategy_name not in self.signals:
                continue

            # Get strategy signals
            strategy_signals = self.signals[strategy_name]

            # Get signal for the market
            market_signal = strategy_signals.get("signals", {}).get(market)

            if market_signal is None:
                continue

            # Get strategy weight
            strategy_weight = strategy_config.get("weight", 1.0)

            # Add signal to market signals
            market_signals[strategy_name] = {
                "signal": market_signal,
                "weight": strategy_weight,
                "confidence": strategy_signals.get("confidence", 0.0),
            }

        # Calculate weighted average signal
        weighted_signal = 0.0
        weight_sum = 0.0

        for strategy_name, signal_data in market_signals.items():
            signal = signal_data["signal"]
            weight = signal_data["weight"]
            confidence = signal_data["confidence"]

            # Apply confidence to weight
            adjusted_weight = weight * confidence

            weighted_signal += signal * adjusted_weight
            weight_sum += adjusted_weight

        if weight_sum > 0:
            weighted_signal /= weight_sum

        # Calculate confidence
        confidence = sum(signal_data["confidence"] * signal_data["weight"] for signal_data in market_signals.values())
        confidence /= sum(signal_data["weight"] for signal_data in market_signals.values()) if market_signals else 1.0

        # Calculate position size
        position_size = weighted_signal * confidence

        # Round position size to 6 decimal places to avoid floating point precision issues in tests
        position_size = round(position_size, 6)

        # Determine trade action
        if position_size > 0.1:
            action = "buy"
        elif position_size < -0.1:
            action = "sell"
        else:
            action = "hold"

        # Create trade signal
        trade_signal = {
            "market": market,
            "action": action,
            "position_size": abs(position_size),
            "confidence": round(confidence, 6),
            "signals": {strategy_name: signal_data["signal"] for strategy_name, signal_data in market_signals.items()},
            "timestamp": datetime.now().isoformat(),
        }

        return trade_signal

    async def _handle_signal_update(self, message: Dict[str, Any]):
        """
        Handle signal update.

        Args:
            message: Signal update message
        """
        try:
            # Extract strategy name
            strategy = message.get("data", {}).get("strategy", "")

            if not strategy:
                return

            # Extract signals
            signals = message.get("data", {}).get("signals", {})

            # Update signals
            self.signals[strategy] = signals

            logger.debug(f"Updated signals for {strategy}")
        except Exception as e:
            logger.error(f"Error handling signal update: {str(e)}")
