#!/usr/bin/env python3
"""
Unified Runner for Synergy7 Trading System

This script provides a unified entry point for the Synergy7 Trading System,
supporting different operational modes (live, paper, backtest, simulation).
"""

import os
import sys
import time
import json
import logging
import asyncio
import argparse
import subprocess
from datetime import datetime
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("unified_runner")

# Import configuration modules
from utils.config.loader import load_config
from shared.utils.monitoring import get_monitoring_service, setup_telegram_alerts
from deployment.monitoring.health_server import start_health_server

class UnifiedRunner:
    """Unified runner for Synergy7 Trading System."""
    
    def __init__(self, mode: str, config_path: str = "config/default.yaml", env_file: str = ".env"):
        """
        Initialize the unified runner.
        
        Args:
            mode: Operational mode (live, paper, backtest, simulation)
            config_path: Path to the configuration file
            env_file: Path to the environment file
        """
        self.mode = mode
        self.config_path = config_path
        self.env_file = env_file
        self.config = None
        self.monitoring = None
        self.health_server = None
        self.streamlit_process = None
        
        # Set environment variables based on mode
        self._set_environment_variables()
    
    def _set_environment_variables(self):
        """Set environment variables based on the operational mode."""
        if self.mode == "live":
            os.environ["TRADING_ENABLED"] = "true"
            os.environ["PAPER_TRADING"] = "false"
            os.environ["BACKTESTING_ENABLED"] = "false"
            os.environ["DRY_RUN"] = "false"
        elif self.mode == "paper":
            os.environ["TRADING_ENABLED"] = "true"
            os.environ["PAPER_TRADING"] = "true"
            os.environ["BACKTESTING_ENABLED"] = "false"
            os.environ["DRY_RUN"] = "true"
        elif self.mode == "backtest":
            os.environ["TRADING_ENABLED"] = "false"
            os.environ["PAPER_TRADING"] = "false"
            os.environ["BACKTESTING_ENABLED"] = "true"
            os.environ["DRY_RUN"] = "true"
        elif self.mode == "simulation":
            os.environ["TRADING_ENABLED"] = "false"
            os.environ["PAPER_TRADING"] = "true"
            os.environ["BACKTESTING_ENABLED"] = "false"
            os.environ["DRY_RUN"] = "true"
        else:
            logger.error(f"Unknown mode: {self.mode}")
            raise ValueError(f"Unknown mode: {self.mode}")
        
        logger.info(f"Environment variables set for {self.mode} mode:")
        logger.info(f"TRADING_ENABLED: {os.environ.get('TRADING_ENABLED')}")
        logger.info(f"PAPER_TRADING: {os.environ.get('PAPER_TRADING')}")
        logger.info(f"BACKTESTING_ENABLED: {os.environ.get('BACKTESTING_ENABLED')}")
        logger.info(f"DRY_RUN: {os.environ.get('DRY_RUN')}")
    
    async def initialize(self):
        """Initialize the unified runner."""
        # Load configuration
        try:
            # Load environment-specific configuration based on mode
            environment = "production" if self.mode == "live" else "development"
            components = ["carbon_core"]
            
            self.config = load_config(environment=environment, components=components)
            logger.info(f"Configuration loaded successfully for environment: {environment}")
        except Exception as e:
            logger.error(f"Error loading configuration: {str(e)}")
            raise
        
        # Initialize monitoring
        self.monitoring = get_monitoring_service()
        
        # Register components for health checks
        self.monitoring.register_component("config", lambda: self.config is not None)
        
        # Start health check server
        health_server_config = self.config.get("deployment", {}).get("health_server", {})
        health_server_host = health_server_config.get("host", "0.0.0.0")
        health_server_port = health_server_config.get("port", 8080)
        
        self.health_server = start_health_server(host=health_server_host, port=health_server_port)
        logger.info(f"Health check server started on {health_server_host}:{health_server_port}")
        
        # Set up Telegram alerts if credentials are available
        telegram_bot_token = self.config.get("monitoring", {}).get("telegram_bot_token")
        if not telegram_bot_token:
            telegram_bot_token = os.environ.get("TELEGRAM_BOT_TOKEN")
            
        telegram_chat_id = self.config.get("monitoring", {}).get("telegram_chat_id")
        if not telegram_chat_id:
            telegram_chat_id = os.environ.get("TELEGRAM_CHAT_ID")
            
        if telegram_bot_token and telegram_chat_id:
            # Set up alert handler with rate limiting (5 minutes between alerts of the same type)
            alert_handler = setup_telegram_alerts(telegram_bot_token, telegram_chat_id, rate_limit_seconds=300)
            
            # Register alert handlers for different alert types
            self.monitoring.register_alert_handler("component_unhealthy", alert_handler)
            self.monitoring.register_alert_handler("low_balance", alert_handler)
            self.monitoring.register_alert_handler("transaction_error", alert_handler)
            self.monitoring.register_alert_handler("circuit_breaker_open", alert_handler)
            self.monitoring.register_alert_handler("system_resources", alert_handler)
            
            logger.info("Telegram alerts configured with rate limiting")
        else:
            logger.warning("Telegram alerts not configured - missing bot token or chat ID")
        
        # Start health checks
        self.monitoring.start_health_checks()
        logger.info("Monitoring and health checks started")
        
        # Start Streamlit dashboard if enabled
        if self.config.get("monitoring", {}).get("enabled", True):
            await self._start_streamlit_dashboard()
    
    async def _start_streamlit_dashboard(self):
        """Start the Streamlit dashboard."""
        try:
            # Check if Streamlit is installed
            subprocess.run(["streamlit", "--version"], check=True, capture_output=True)
            
            # Get Streamlit configuration
            streamlit_config = self.config.get("deployment", {}).get("streamlit", {})
            port = streamlit_config.get("port", 8501)
            headless = streamlit_config.get("headless", False)
            
            # Start Streamlit in a separate process
            cmd = [
                "streamlit", "run",
                os.path.join("deployment", "dashboard", "streamlit_dashboard.py"),
                "--server.port", str(port),
                "--server.headless", str(headless).lower(),
                "--server.enableCORS", "false",
                "--server.enableXsrfProtection", "false",
                "--browser.gatherUsageStats", "false",
            ]
            
            logger.info(f"Starting Streamlit dashboard: {' '.join(cmd)}")
            self.streamlit_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            logger.info(f"Streamlit dashboard started with PID {self.streamlit_process.pid}")
            logger.info(f"Streamlit dashboard URL: http://localhost:{port}")
        except subprocess.CalledProcessError:
            logger.warning("Streamlit not found. Please install it with: pip install streamlit")
        except Exception as e:
            logger.error(f"Error starting Streamlit dashboard: {str(e)}")
    
    async def run(self):
        """Run the unified runner in the specified mode."""
        logger.info(f"Running Synergy7 Trading System in {self.mode.upper()} mode")
        
        try:
            if self.mode == "live" or self.mode == "paper":
                # Import and run the live trading module
                from deployment.scripts.start_live_trading import run_live_trading
                await run_live_trading()
            elif self.mode == "backtest":
                # Import and run the backtest module
                from backtest.engine.run_backtest import run_backtest
                await run_backtest()
            elif self.mode == "simulation":
                # Import and run the simulation module
                from deployment.scripts.run_simulation import run_simulation
                await run_simulation()
            else:
                logger.error(f"Unknown mode: {self.mode}")
                raise ValueError(f"Unknown mode: {self.mode}")
        except Exception as e:
            logger.error(f"Error running {self.mode} mode: {str(e)}")
            raise
    
    async def shutdown(self):
        """Shutdown the unified runner."""
        logger.info("Shutting down unified runner")
        
        # Stop Streamlit dashboard
        if self.streamlit_process:
            logger.info(f"Stopping Streamlit dashboard (PID {self.streamlit_process.pid})")
            self.streamlit_process.terminate()
            try:
                self.streamlit_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                logger.warning("Streamlit dashboard did not terminate gracefully, killing it")
                self.streamlit_process.kill()
            self.streamlit_process = None
        
        # Stop health check server
        if self.health_server:
            logger.info("Stopping health check server")
            self.health_server.close()
            self.health_server = None
        
        # Stop monitoring
        if self.monitoring:
            logger.info("Stopping monitoring")
            self.monitoring.stop_health_checks()
            self.monitoring = None
        
        logger.info("Unified runner shutdown complete")

async def main():
    """Main function."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Unified Runner for Synergy7 Trading System")
    parser.add_argument("--mode", choices=["live", "paper", "backtest", "simulation"], default="paper",
                        help="Operational mode (default: paper)")
    parser.add_argument("--config", default="config/default.yaml",
                        help="Path to the configuration file (default: config/default.yaml)")
    parser.add_argument("--env", default=".env",
                        help="Path to the environment file (default: .env)")
    
    args = parser.parse_args()
    
    # Create and initialize the unified runner
    runner = UnifiedRunner(args.mode, args.config, args.env)
    
    try:
        # Initialize the runner
        await runner.initialize()
        
        # Run the runner
        await runner.run()
    except KeyboardInterrupt:
        logger.info("Interrupted by user")
    except Exception as e:
        logger.error(f"Error in unified runner: {str(e)}")
    finally:
        # Shutdown the runner
        await runner.shutdown()

if __name__ == "__main__":
    asyncio.run(main())
