#!/usr/bin/env python3
"""
Position Sizer Module for Synergy7 Trading System

This module provides dynamic position sizing based on volatility,
account balance, and risk parameters.
"""

import logging
import numpy as np
from typing import Dict, List, Any, Optional, Union, Tuple
import pandas as pd

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("position_sizer")

class PositionSizer:
    """
    Dynamic position sizer that adjusts position sizes based on
    volatility, account balance, and risk parameters.
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize the position sizer.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config or {}
        
        # Default risk parameters
        self.max_position_size = self.config.get("max_position_size", 0.1)
        self.max_portfolio_risk = self.config.get("max_portfolio_risk", 0.02)
        self.volatility_lookback = self.config.get("volatility_lookback", 20)
        self.volatility_scaling = self.config.get("volatility_scaling", True)
        self.min_position_size = self.config.get("min_position_size", 0.01)
        self.position_size_increment = self.config.get("position_size_increment", 0.01)
        
        logger.info("Initialized PositionSizer")
    
    def calculate_position_size(self,
                               price_data: pd.DataFrame,
                               account_balance: float,
                               market: str,
                               signal_strength: float = 1.0) -> Dict[str, Any]:
        """
        Calculate the position size based on volatility and risk parameters.
        
        Args:
            price_data: DataFrame containing price data with 'close' column
            account_balance: Current account balance
            market: Market symbol
            signal_strength: Strength of the trading signal (0.0 to 1.0)
            
        Returns:
            Dictionary containing position size information
        """
        # Validate inputs
        if price_data is None or len(price_data) < self.volatility_lookback:
            logger.warning(f"Insufficient price data for {market}, using minimum position size")
            return {
                "position_size": self.min_position_size,
                "position_value": account_balance * self.min_position_size,
                "volatility": None,
                "risk_adjusted": False
            }
        
        # Calculate volatility
        returns = price_data["close"].pct_change().dropna()
        volatility = returns.rolling(window=self.volatility_lookback).std().iloc[-1]
        
        # Default position size as percentage of account
        position_size = self.max_position_size
        
        # Adjust for volatility if enabled
        if self.volatility_scaling and volatility is not None:
            # Calculate average volatility over the past year (if available)
            if len(returns) >= 252:
                avg_volatility = returns.rolling(window=252).std().mean()
            else:
                avg_volatility = volatility
            
            # Scale position size inversely with volatility
            volatility_ratio = avg_volatility / volatility if volatility > 0 else 1.0
            position_size = min(
                self.max_position_size,
                self.max_position_size * volatility_ratio
            )
        
        # Adjust for signal strength
        position_size = position_size * signal_strength
        
        # Ensure position size is within bounds
        position_size = max(self.min_position_size, position_size)
        position_size = min(self.max_position_size, position_size)
        
        # Round to nearest increment
        position_size = round(position_size / self.position_size_increment) * self.position_size_increment
        
        # Calculate position value
        position_value = account_balance * position_size
        
        logger.info(f"Calculated position size for {market}: {position_size:.4f} ({position_value:.2f})")
        
        return {
            "position_size": position_size,
            "position_value": position_value,
            "volatility": volatility,
            "risk_adjusted": True
        }
    
    def calculate_stop_loss(self,
                           entry_price: float,
                           position_size: float,
                           account_balance: float,
                           price_data: pd.DataFrame = None,
                           is_long: bool = True) -> Dict[str, Any]:
        """
        Calculate stop loss level based on volatility and risk parameters.
        
        Args:
            entry_price: Entry price
            position_size: Position size as percentage of account
            account_balance: Current account balance
            price_data: DataFrame containing price data with 'close' column
            is_long: Whether the position is long (True) or short (False)
            
        Returns:
            Dictionary containing stop loss information
        """
        # Default risk per trade
        risk_per_trade = self.config.get("risk_per_trade", 0.01)
        
        # Calculate position value
        position_value = account_balance * position_size
        
        # Calculate maximum loss amount
        max_loss_amount = account_balance * risk_per_trade
        
        # Calculate price movement for stop loss
        price_movement_pct = max_loss_amount / position_value
        
        # Calculate stop loss price
        if is_long:
            stop_loss_price = entry_price * (1 - price_movement_pct)
        else:
            stop_loss_price = entry_price * (1 + price_movement_pct)
        
        # If price data is provided, adjust stop loss based on volatility
        if price_data is not None and len(price_data) >= self.volatility_lookback:
            # Calculate average true range (ATR)
            high = price_data["high"]
            low = price_data["low"]
            close = price_data["close"]
            
            tr1 = high - low
            tr2 = abs(high - close.shift(1))
            tr3 = abs(low - close.shift(1))
            
            true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
            atr = true_range.rolling(window=self.volatility_lookback).mean().iloc[-1]
            
            # Adjust stop loss based on ATR
            atr_multiplier = self.config.get("atr_multiplier", 2.0)
            
            if is_long:
                atr_stop_loss = entry_price - (atr * atr_multiplier)
                stop_loss_price = max(stop_loss_price, atr_stop_loss)
            else:
                atr_stop_loss = entry_price + (atr * atr_multiplier)
                stop_loss_price = min(stop_loss_price, atr_stop_loss)
        
        logger.info(f"Calculated stop loss: {stop_loss_price:.4f} (entry: {entry_price:.4f})")
        
        return {
            "stop_loss_price": stop_loss_price,
            "risk_amount": max_loss_amount,
            "risk_percentage": risk_per_trade
        }
    
    def calculate_take_profit(self,
                             entry_price: float,
                             stop_loss_price: float,
                             is_long: bool = True,
                             risk_reward_ratio: float = 2.0) -> float:
        """
        Calculate take profit level based on risk-reward ratio.
        
        Args:
            entry_price: Entry price
            stop_loss_price: Stop loss price
            is_long: Whether the position is long (True) or short (False)
            risk_reward_ratio: Desired risk-reward ratio
            
        Returns:
            Take profit price
        """
        # Calculate risk (price difference to stop loss)
        if is_long:
            risk = entry_price - stop_loss_price
            take_profit_price = entry_price + (risk * risk_reward_ratio)
        else:
            risk = stop_loss_price - entry_price
            take_profit_price = entry_price - (risk * risk_reward_ratio)
        
        logger.info(f"Calculated take profit: {take_profit_price:.4f} (entry: {entry_price:.4f})")
        
        return take_profit_price
