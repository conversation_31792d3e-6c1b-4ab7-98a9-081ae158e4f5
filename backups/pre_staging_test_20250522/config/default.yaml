# Synergy7 System Configuration
# Central configuration file for runner and backtester

# System mode
mode:
  live_trading: true
  paper_trading: false
  backtesting: false
  simulation: false

# Solana configuration
solana:
  rpc_url: ${HELIUS_RPC_URL}
  private_rpc_url: ${HELIUS_RPC_URL}
  fallback_rpc_url: ${FALLBACK_RPC_URL}
  commitment: confirmed
  max_retries: 3
  retry_delay: 1.0
  tx_timeout: 30
  provider: "helius"  # Using <PERSON><PERSON> as the primary RPC provider

# Wallet configuration
wallet:
  address: ${WALLET_ADDRESS}
  state_sync_interval: 60  # seconds
  position_update_interval: 300  # seconds
  max_positions: 10
  data_dir: "data"  # Updated path to centralized data directory

# Strategy configuration
strategies:
  - name: basic_ma_strategy
    enabled: true
    params:
      short_window: 20
      long_window: 50
      threshold: 0.02
  - name: wallet_momentum
    enabled: true
    params:
      lookback_period: 24  # hours
      min_wallet_count: 5
      momentum_threshold: 0.1
  - name: alpha_signal_blend
    enabled: false
    params:
      alpha_weight: 0.7
      momentum_weight: 0.3

# Risk management
risk:
  max_position_size_usd: 1000
  max_position_size_pct: 0.05  # 5% of portfolio
  stop_loss_pct: 0.10  # 10% loss
  take_profit_pct: 0.20  # 20% gain
  max_drawdown_pct: 0.15  # 15% max drawdown
  daily_loss_limit_usd: 500
  circuit_breaker_enabled: true

# Execution parameters
execution:
  slippage_tolerance: 0.01  # 1%
  max_spread_pct: 0.02  # 2%
  min_liquidity_usd: 10000
  order_type: "market"  # market, limit
  retry_failed_orders: true
  max_order_retries: 3

# Backtest configuration
backtest:
  start_date: "2023-01-01"
  end_date: "2023-12-31"
  initial_capital: 10000
  fee_pct: 0.001  # 0.1%
  data_source: "data/historical"  # Updated path to centralized data directory
  output_dir: "output/backtest"  # Updated path to centralized output directory

# Monitoring and alerts
monitoring:
  enabled: true
  update_interval: 300  # seconds
  telegram_alerts: true
  email_alerts: false
  performance_report_interval: 86400  # daily in seconds
  log_level: "INFO"

# API integrations
apis:
  helius:
    enabled: true
    api_key: ${HELIUS_API_KEY}
    endpoint: "https://api.helius.dev/v0"
    rpc_endpoint: "https://mainnet.helius-rpc.com/?api-key=${HELIUS_API_KEY}"
    ws_endpoint: "wss://mainnet.helius-rpc.com/?api-key=${HELIUS_API_KEY}"
    use_enhanced_apis: true
  coingecko:
    enabled: true
    api_key: ${COINGECKO_API_KEY}
    endpoint: "https://api.coingecko.com/api/v3"
  birdeye:
    enabled: true
    api_key: ${BIRDEYE_API_KEY}
    endpoint: "https://api.birdeye.so/v1"

# Deployment
deployment:
  docker:
    image: "synergy7system:latest"
    container_name: "synergy7_trading_bot"
    restart_policy: "unless-stopped"
  quantconnect:
    project_id: "your_project_id"
    backtest_id: "your_backtest_id"
