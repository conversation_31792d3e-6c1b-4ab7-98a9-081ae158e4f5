# Backtest Configuration

# General settings
general:
  start_date: "2023-01-01"
  end_date: "2023-12-31"
  initial_capital: 10000
  fee_pct: 0.001  # 0.1%
  data_source: "data/historical"
  output_dir: "output/backtest"

# Market settings
markets:
  - name: "SOL-USDC"
    enabled: true
    slippage_model: "linear"
    slippage_params:
      base: 0.001
      impact: 0.0001
    min_liquidity_usd: 10000
  - name: "JTO-USDC"
    enabled: true
    slippage_model: "linear"
    slippage_params:
      base: 0.002
      impact: 0.0002
    min_liquidity_usd: 5000
  - name: "BONK-USDC"
    enabled: true
    slippage_model: "linear"
    slippage_params:
      base: 0.003
      impact: 0.0003
    min_liquidity_usd: 2000

# Strategy settings
strategies:
  - name: "momentum"
    enabled: true
    params:
      short_window: 20
      long_window: 50
      threshold: 0.02
  - name: "mean_reversion"
    enabled: false  # Explicitly disabled as per strategy_finder.md directive
    params:
      window: 30
      std_dev: 2.0
      mean_window: 100
  - name: "order_book_imbalance"
    enabled: true
    params:
      window_size: 20
      threshold: 0.1
      depth: 10

# Risk management
risk:
  max_position_size_usd: 1000
  max_position_size_pct: 0.05  # 5% of portfolio
  stop_loss_pct: 0.10  # 10% loss
  take_profit_pct: 0.20  # 20% gain
  max_drawdown_pct: 0.15  # 15% max drawdown

# Reporting
reporting:
  metrics:
    - "sharpe_ratio"
    - "sortino_ratio"
    - "max_drawdown"
    - "win_rate"
    - "profit_factor"
    - "expectancy"
  plots:
    - "equity_curve"
    - "drawdown_chart"
    - "monthly_returns"
    - "trade_distribution"
  output_format: "html"
  save_trades: true
