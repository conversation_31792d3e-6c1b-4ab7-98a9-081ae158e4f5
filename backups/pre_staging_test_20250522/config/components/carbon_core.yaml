# Carbon Core Configuration

# Core configuration
core:
  enabled: true
  binary_path: "bin/carbon_core"
  log_level: "info"
  max_memory_mb: 1024
  worker_threads: 4
  update_interval_ms: 100

# Market Microstructure Settings
market_microstructure:
  enabled: true
  markets:
    - "SOL-USDC"
    - "JTO-USDC"
    - "BONK-USDC"
  order_book_depth: 20
  update_interval_ms: 100
  impact_window_size: 50
  liquidity_threshold: 10000

# Statistical Signal Processing Settings
statistical_signal_processing:
  enabled: true
  signal_types:
    - "price_momentum"
    - "volume_profile"
    - "order_flow_imbalance"
    - "volatility_regime"
  window_size: 100
  update_interval_ms: 50
  confidence_threshold: 0.7
  snr_threshold: 1.5

# Reinforcement Learning Execution Settings
rl_execution:
  enabled: true
  model_path: "models/rl_execution_model.pt"
  action_space:
    - "market"
    - "limit"
    - "wait"
  state_features:
    - "price"
    - "volume"
    - "spread"
    - "order_imbalance"
    - "volatility"
  reward_weights:
    execution_quality: 0.5
    slippage: 0.3
    time: 0.2
  learning_rate: 0.001
  update_interval_ms: 200
  online_learning: true

# Communication Settings
communication:
  protocol: "zeromq"
  zeromq:
    pub_endpoint: "tcp://127.0.0.1:5555"
    sub_endpoint: "tcp://127.0.0.1:5556"
    req_endpoint: "tcp://127.0.0.1:5557"
  message_format: "json"
  max_message_size: 1048576
  heartbeat_interval_ms: 1000
