# Synergy7 Testing Environment Configuration

# System mode
mode:
  live_trading: false
  paper_trading: true
  backtesting: false
  simulation: false

# Solana configuration
solana:
  rpc_url: "https://api.devnet.solana.com"
  private_rpc_url: "https://api.devnet.solana.com"
  fallback_rpc_url: "https://api.devnet.solana.com"
  commitment: "confirmed"
  max_retries: 3
  retry_delay: 1.0
  tx_timeout: 30
  provider: "solana"

# Risk management
risk:
  max_position_size_usd: 100  # Small size for testing
  max_position_size_pct: 0.1  # 10% of portfolio
  stop_loss_pct: 0.05  # 5% loss
  take_profit_pct: 0.10  # 10% gain
  max_drawdown_pct: 0.05  # 5% max drawdown
  daily_loss_limit_usd: 50  # Small limit for testing
  circuit_breaker_enabled: true

# Execution parameters
execution:
  slippage_tolerance: 0.01  # 1%
  max_spread_pct: 0.02  # 2%
  min_liquidity_usd: 1000  # Lower for testing
  order_type: "market"  # market, limit
  retry_failed_orders: true
  max_order_retries: 3
  dry_run: true  # No real transactions in testing

# Monitoring and alerts
monitoring:
  enabled: true
  update_interval: 60  # seconds
  telegram_alerts: false  # Disabled for testing
  email_alerts: false  # Disabled for testing
  performance_report_interval: 300  # 5 minutes in seconds
  log_level: "DEBUG"  # More verbose for testing

# API integrations
apis:
  helius:
    enabled: true
    api_key: ${HELIUS_API_KEY}
    endpoint: "https://api.helius.dev/v0"
    rpc_endpoint: "https://devnet.helius-rpc.com/?api-key=${HELIUS_API_KEY}"
    ws_endpoint: "wss://devnet.helius-rpc.com/?api-key=${HELIUS_API_KEY}"
    use_enhanced_apis: true
  coingecko:
    enabled: false  # Disabled for testing
    api_key: ${COINGECKO_API_KEY}
    endpoint: "https://api.coingecko.com/api/v3"
  birdeye:
    enabled: false  # Use hardcoded data for testing
    api_key: ${BIRDEYE_API_KEY}
    endpoint: "https://api.birdeye.so/v1"

# Deployment
deployment:
  docker:
    image: "synergy7:testing"
    container_name: "synergy7_testing"
    restart_policy: "no"
  streamlit:
    port: 8501
    headless: false
    theme:
      base: "dark"
      primary_color: "#FF4B4B"

# Test settings
test:
  enabled: true
  duration_seconds: 300
  log_level: "debug"
  simulate_market_data: true
  simulate_transactions: true

# Carbon Core
carbon_core:
  enabled: false  # Disabled for testing
  binary_path: "bin/carbon_core"
  config_path: "config/carbon_core.yaml"
  communication:
    protocol: "zeromq"
    host: "127.0.0.1"
    port: 5555
