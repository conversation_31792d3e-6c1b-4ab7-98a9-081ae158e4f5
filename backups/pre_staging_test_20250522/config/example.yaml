# Synergy7 System Example Configuration
# This file serves as a template for configuring the Synergy7 Trading System

# System mode
mode:
  live_trading: false  # Set to true for live trading
  paper_trading: true  # Set to true for paper trading (simulated trades)
  backtesting: false   # Set to true for backtesting
  simulation: false    # Set to true for simulation mode

# Solana configuration
solana:
  rpc_url: "https://api.mainnet-beta.solana.com"  # Replace with your RPC URL
  private_rpc_url: "https://api.mainnet-beta.solana.com"  # Replace with your private RPC URL
  fallback_rpc_url: "https://solana-api.projectserum.com"  # Fallback RPC URL
  commitment: "confirmed"  # Commitment level (confirmed, finalized, processed)
  max_retries: 3  # Maximum number of retries for RPC calls
  retry_delay: 1.0  # Delay between retries in seconds
  tx_timeout: 30  # Transaction timeout in seconds
  provider: "helius"  # RPC provider (helius, quicknode, etc.)

# Wallet configuration
wallet:
  address: "YOUR_WALLET_ADDRESS"  # Replace with your wallet address
  state_sync_interval: 60  # Wallet state sync interval in seconds
  position_update_interval: 300  # Position update interval in seconds
  max_positions: 10  # Maximum number of positions
  data_dir: "data"  # Data directory

# Strategy configuration
strategies:
  - name: basic_ma_strategy  # Strategy name
    enabled: true  # Set to true to enable the strategy
    params:
      short_window: 20  # Short moving average window
      long_window: 50  # Long moving average window
      threshold: 0.02  # Threshold for signal generation
  - name: wallet_momentum  # Strategy name
    enabled: true  # Set to true to enable the strategy
    params:
      lookback_period: 24  # Lookback period in hours
      min_wallet_count: 5  # Minimum number of wallets
      momentum_threshold: 0.1  # Momentum threshold

# Risk management
risk:
  max_position_size_usd: 1000  # Maximum position size in USD
  max_position_size_pct: 0.05  # Maximum position size as percentage of portfolio
  stop_loss_pct: 0.10  # Stop loss percentage
  take_profit_pct: 0.20  # Take profit percentage
  max_drawdown_pct: 0.15  # Maximum drawdown percentage
  daily_loss_limit_usd: 500  # Daily loss limit in USD
  circuit_breaker_enabled: true  # Enable circuit breaker

# Execution parameters
execution:
  slippage_tolerance: 0.01  # Slippage tolerance (1%)
  max_spread_pct: 0.02  # Maximum spread percentage (2%)
  min_liquidity_usd: 10000  # Minimum liquidity in USD
  order_type: "market"  # Order type (market, limit)
  retry_failed_orders: true  # Retry failed orders
  max_order_retries: 3  # Maximum number of order retries

# Backtest configuration
backtest:
  start_date: "2023-01-01"  # Start date for backtesting
  end_date: "2023-12-31"  # End date for backtesting
  initial_capital: 10000  # Initial capital for backtesting
  fee_pct: 0.001  # Fee percentage (0.1%)
  data_source: "data/historical"  # Data source directory
  output_dir: "output/backtest"  # Output directory

# Monitoring and alerts
monitoring:
  enabled: true  # Enable monitoring
  update_interval: 300  # Update interval in seconds
  telegram_alerts: true  # Enable Telegram alerts
  email_alerts: false  # Enable email alerts
  performance_report_interval: 86400  # Performance report interval in seconds (daily)
  log_level: "INFO"  # Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)

# API integrations
apis:
  helius:
    enabled: true  # Enable Helius API
    api_key: "YOUR_HELIUS_API_KEY"  # Replace with your Helius API key
    endpoint: "https://api.helius.dev/v0"  # Helius API endpoint
    rpc_endpoint: "https://mainnet.helius-rpc.com/?api-key=YOUR_HELIUS_API_KEY"  # Helius RPC endpoint
    ws_endpoint: "wss://mainnet.helius-rpc.com/?api-key=YOUR_HELIUS_API_KEY"  # Helius WebSocket endpoint
    use_enhanced_apis: true  # Use enhanced APIs
  coingecko:
    enabled: true  # Enable CoinGecko API
    api_key: "YOUR_COINGECKO_API_KEY"  # Replace with your CoinGecko API key
    endpoint: "https://api.coingecko.com/api/v3"  # CoinGecko API endpoint
  birdeye:
    enabled: true  # Enable Birdeye API
    api_key: "YOUR_BIRDEYE_API_KEY"  # Replace with your Birdeye API key
    endpoint: "https://api.birdeye.so/v1"  # Birdeye API endpoint

# Deployment
deployment:
  docker:
    image: "synergy7system:latest"  # Docker image name
    container_name: "synergy7_trading_bot"  # Container name
    restart_policy: "unless-stopped"  # Restart policy
